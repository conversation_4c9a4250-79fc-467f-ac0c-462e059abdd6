﻿<?xml version="1.0" encoding="UTF-8" ?>
<pages:PopupPage
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    CloseWhenBackgroundIsClicked="false"
    xmlns:t="clr-namespace:DrMuscle.Layout"
    xmlns:constants="clr-namespace:DrMuscle.Constants"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
    xmlns:local="clr-namespace:DrMuscle.Views"
    xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
             xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup"
        xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms"

    x:Class="DrMuscle.Views.NormalExercisePopup">
    <pages:PopupPage.Content>
      <Frame Padding="0" CornerRadius="4"
             HasShadow="False"
             IsClippedToBounds="True"
             HorizontalOptions="FillAndExpand"
        VerticalOptions="CenterAndExpand"
             BackgroundColor="White"
             Margin="20,10,20,0">
    <StackLayout
        Orientation="Vertical"
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="Center"
        Padding="0,0,0,10"
        >
      <StackLayout.Resources>
        <ResourceDictionary>
          <Style TargetType="Button" x:Key="ButtonStyle">
            <Setter Property="FontSize" Value="Medium" />
            <Setter Property="TextColor" Value="{x:Static constants:AppThemeConstants.BlueColor}" />
            <Setter Property="BorderColor" Value="Transparent" />
            <Setter Property="HorizontalOptions" Value="End" />
            <Setter Property="VerticalOptions" Value="CenterAndExpand" />
            <Setter Property="BackgroundColor" Value="Transparent" />
          </Style>
        </ResourceDictionary>
      </StackLayout.Resources>
        <ffimageloading:CachedImage x:Name="videoPlayer" Source="{Binding VideoUrl}" HeightRequest="200" Aspect="AspectFit" FadeAnimationForCachedImages="False" HorizontalOptions="FillAndExpand" BackgroundColor="White" IsVisible="{Binding IsVideoUrlAvailable}">
                    <ffimageloading:CachedImage.GestureRecognizers>
                        <TapGestureRecognizer />
                    </ffimageloading:CachedImage.GestureRecognizers>
                    </ffimageloading:CachedImage>
      <Label x:Name="LblTitle" Margin="15,15,10,0"  FontAttributes="Bold" FontSize="21" TextColor="{OnPlatform iOS=#000000, Android=#000000}"  HorizontalOptions="CenterAndExpand" VerticalOptions="Start" />
        <Label x:Name="LblDesc" Margin="15,0,10,0" TextColor="{OnPlatform iOS=#000000, Android=#000000}" FontSize="18" HorizontalOptions="CenterAndExpand" VerticalOptions="Start" />
      <StackLayout x:Name="PickerStack" Margin="7,15,7,8" VerticalOptions="Center" HorizontalOptions="FillAndExpand" BackgroundColor="White">
        <controls:DrMuscleEntry Margin="15,0,15,0" x:Name="EntryWeight" TextColor="Black" VerticalOptions="Start" Keyboard="Numeric" Text="" Placeholder="Enter weight" HorizontalOptions="FillAndExpand" BackgroundColor="Transparent" MaxLength="5" Completed="EntryWeight_Completed" />
          <local:PickerView
              Margin="15,-40,15,-40"
                    x:Name="PickerCM"
                    IsVisible="false"
              VerticalOptions="Start"
              HeightRequest="250"
                    HorizontalOptions="FillAndExpand"
                     />
          <BoxView HorizontalOptions="FillAndExpand" Margin="5,0" BackgroundColor="#D8D8D8"  HeightRequest="0.5" />
          
      </StackLayout>
      <StackLayout Orientation="Horizontal" Margin="10,0,10,5" VerticalOptions="EndAndExpand" HorizontalOptions="End">
          <Button x:Name="BtnCancel" Text="Cancel" Style="{StaticResource ButtonStyle}" TextColor="{OnPlatform iOS=#195377, Android=#195377}" HorizontalOptions="End" FontAttributes="Bold" WidthRequest="100" Clicked="BtnCancelClicked" BackgroundColor="White" />
          <Button x:Name="BtnConfirm" Text="Continue" Style="{StaticResource ButtonStyle}" TextColor="{OnPlatform iOS=#195377, Android=#195377}" HorizontalOptions="End" FontAttributes="Bold" WidthRequest="100" Clicked="BtnDoneClicked" BackgroundColor="White" />
      </StackLayout>
    </StackLayout>
          </Frame>
      </pages:PopupPage.Content>
</pages:PopupPage>
