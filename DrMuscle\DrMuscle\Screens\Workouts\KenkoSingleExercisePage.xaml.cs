﻿using Acr.UserDialogs;
using DrMuscle.Dependencies;
using DrMuscle.Screens.Exercises;
using DrMuscleWebApiSharedModel;
using SlideOverKit;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using System.Globalization;
using DrMuscle.Helpers;
using DrMuscle.Resx;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using DrMuscle.Message;
using DrMuscle.Screens.Me;
using DrMuscle.Layout;
using Newtonsoft.Json;
using Microsoft.AppCenter.Crashes;
using DrMuscle.Views;
using Rg.Plugins.Popup.Services;
using DrMuscle.Constants;
using DrMuscle.Cells;
using Plugin.Connectivity;
using DrMuscle.Services;
using System.Reflection;
using System.IO;
using DrMuscle.Screens.Subscription;
using Xamarin.Forms.Xaml;
using DrMuscle.Controls;
using DrMuscle.Utility;
using static Xamarin.Forms.Internals.GIFBitmap;

namespace DrMuscle.Screens.Workouts
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class KenkoSingleExercisePage : DrMusclePage, INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        protected void SetObservableProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = "")
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return;
            field = value;
            OnPropertyChanged(propertyName);
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            var changed = PropertyChanged;
            if (changed != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        private ObservableCollection<ExerciseWorkSetsModel> _exerciseItems;
        public ObservableCollection<ExerciseWorkSetsModel> exerciseItems
        {
            get { return _exerciseItems; }
            set
            {
                _exerciseItems = value;
                OnPropertyChanged("exerciseItems");
            }
        }

        public List<ObservableGroupCollection<ExerciseWorkSetsModel, WorkoutLogSerieModel>> GroupedData { get; set; }

        protected override void OnSizeAllocated(double width, double height)
        {
            base.OnSizeAllocated(width, height);
            if (Device.RuntimePlatform.Equals(Device.Android))
                StatusBarHeight.Height = 5;
            else
                StatusBarHeight.Height = App.StatusBarHeight;
            //double navigationBarHeight = Math.Abs(App.ScreenSize.Height - height - App.StatusBarHeight);
            // App.NavigationBarHeight = 146 + App.StatusBarHeight;// navigationBarHeight;

        }


        TimerPopup popup;
        bool isAppear = false;
        private GetUserProgramInfoResponseModel upi = null;
        private bool IsSettingsChanged { get; set; }
        StackLayout contextMenuStack;
        private bool _isAskedforLightSession, _isAskedforDeload;
        private IFirebase _firebase;
        private bool IsGlobalSettingsChanged { get; set; }
        private decimal _userBodyWeight = 0;
        private bool _injuryRehabRunning = false;
        private long _injuryRehabWithExerciseId = 0;
        private WorkoutLogSerieModelRef _backOffSet;

        private bool _isFreePlanPopup = false;
        public KenkoSingleExercisePage()
        {
            InitializeComponent();
            exerciseItems = new ObservableCollection<ExerciseWorkSetsModel>();
            //ExerciseListView.GroupHeaderTemplate = kenkoHeaderDataTemplateSelector;
            //ExerciseListView.GroupHeaderTemplate = new KenkoHeaderDataTemplateSelector
            //{
            //    RegularDateTemplate = RegularTemplate,
            //    FooterExerciseTemplate = HeaderTemplate
            //};
            ExerciseListView.ItemsSource = GroupedData;
            NavigationPage.SetHasNavigationBar(this, false);
            ExerciseListView.ItemTapped += ExerciseListView_ItemTapped;
            _firebase = DependencyService.Get<IFirebase>();

            if (LocalDBManager.Instance.GetDBSetting("PlatesKg") == null || LocalDBManager.Instance.GetDBSetting("PlatesLb") == null)
            {
                var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);

                var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
            }

            //SaveWorkoutButton.Clicked += SaveWorkoutButton_Clicked;
            RefreshLocalized();
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });
            MessagingCenter.Subscribe<Message.ExerciseDeleteMessage>(this, "ExerciseDeleteMessage", (obj) =>
            {
                IsSettingsChanged = false;
            });
            MessagingCenter.Subscribe<Message.GlobalSettingsChangeMessage>(this, "GlobalSettingsChangeMessage", (obj) => {
                if (obj.IsDisappear)
                    IsGlobalSettingsChanged = true;
            });

            MessagingCenter.Subscribe<LoadNormalExercise>(this, "LoadNormalExercise", (obj) =>
            {
                LocalDBManager.Instance.SetDBSetting($"Normal{obj.exerciseId}", "true");
                var item = exerciseItems.Where(x => x.Id == obj.exerciseId).FirstOrDefault();

                Timer.Instance.StopTimer();

                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(item.Id);
                try
                {
                    ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                    ((App)Application.Current).WorkoutLogContext.SaveContexts();
                }
                catch (Exception ex)
                {

                }
                {
                    item.Clear();
                    try
                    {
                        LocalDBManager.Instance.SetDBReco("RReps" + item.Id + "Normal" + "Deload", $"");
                        LocalDBManager.Instance.SetDBReco("RReps" + item.Id + "RestPause" + "Deload", $"");
                        if (obj.isReloadReco)
                        {
                            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + item.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + item.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    if (obj.isReloadReco)
                    {
                        item.RecoModel = null;
                    }
                    //item.IsPyramid = false;
                    if (LocalDBManager.Instance.GetDBSetting($"{DateTime.Now.Date}{item.Id}")?.Value == "1" && !item.IsFinished)
                    {
                        LocalDBManager.Instance.SetDBSetting($"{CurrentLog.Instance.CurrentWorkoutTemplate.Id}Challenge{item.BodyPartId}", $"{item.BodyPartId}");
                        LocalDBManager.Instance.SetDBSetting($"{DateTime.Now.Date}{item.Id}", "0");
                        var cnt = LocalDBManager.Instance.GetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}")?.Value;
                        if (!string.IsNullOrEmpty(cnt))
                        {
                            var challengeCount = int.Parse(cnt);
                            if (challengeCount == 1)
                                LocalDBManager.Instance.SetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}", "0");
                            else if (challengeCount == 2)
                                LocalDBManager.Instance.SetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}", "1");

                        }
                    }
                }
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        //ExerciseListView.BeginRefresh();
                        //ExerciseListView.EndRefresh();
                    });
                }
                CellHeaderTapped(new Button() { BindingContext = item }, null);

            });
            Timer.Instance.OnTimerChange += OnTimerChange;
            Timer.Instance.OnTimerDone += OnTimerDone;
            Timer.Instance.OnTimerStop += OnTimerStop;

         
        }

        private void RefreshLocalized()
        {
            Title = AppResources.ChooseExercise;
            // LblTodaysExercises.Text = AppResources.TodaYExercises;
            //  SaveWorkoutButton.Text = "Finish workout"; // AppResources.FinishAndSaveWorkout;
        }

        bool TimerBased = false;
        string timeRemain = "0";
        async void OnTimerDone()
        {
            try
            {
                
                BtnTimer.Text = null;
                BtnTimer.Image = "stopwatch.png";

                try
                {
                    if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                        LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                    if (TimerBased && (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" || CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId == 12 || CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased == true) && isAppear)
                    {
                        if (_backOffSet != null)
                            _backOffSet.ShowWorkTimer = false;
                        await Task.Delay(100);
                        TimerBased = false;
                        popup = new TimerPopup(false);
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", timeRemain);
                        popup.popupTitle = "Work";
                        popup?.SetTimerRepsSets("");
                        popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                        Timer.Instance.Remaining = int.Parse(popup.RemainingSeconds);
                        PopupNavigation.Instance.PushAsync(popup);
                        Timer.Instance.StartTimer();

                    }
                }
                catch (Exception ex)
                {

                }
            }
            catch (Exception ex)
            {

            }

        }

        void OnTimerStop()
        {
            try
            {
                //if (ToolbarItems.Count > 0)
                //{
                //    var index = 0;
                //    if (this.ToolbarItems.Count == 2)
                //    {
                //        index = 1;
                //    }
                //    this.ToolbarItems.RemoveAt(index);
                //    timerToolbarItem = new ToolbarItem("", "stopwatch.png", SlideTimerAction, ToolbarItemOrder.Primary, 0);
                //    this.ToolbarItems.Insert(index, timerToolbarItem);
                //}
                BtnTimer.Text = null;
                BtnTimer.Image = "stopwatch.png";
            }
            catch (Exception ex)
            {

            }
        }

        void OnTimerChange(int remaining)
        {
            try
            {

            Device.BeginInvokeOnMainThread(() =>
            {
                if (Timer.Instance.State == "RUNNING")
                {
                    if (CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId == 12 && Timer.Instance.IsWorkTimer)
                    {
                        var timeSpan = TimeSpan.FromSeconds(remaining);
                        BtnTimer.Text = timeSpan.ToString(@"mm\:ss");
                        BtnTimer.FontSize = 17;
                    }
                    else
                    {
                        BtnTimer.FontSize = 24;
                        BtnTimer.Text = remaining.ToString();
                    }
                    BtnTimer.Image = "";
                }
                else
                //if (remaining.ToString().Equals("0"))
                {
                    BtnTimer.Text = null;
                    BtnTimer.Image = "stopwatch.png";
                }
            });

            if (remaining == 2)
            {
                try
                {

                    var item = CurrentLog.Instance.ExerciseLog.Exercice;
                    var exer = exerciseItems.Where(x => x.Id == item.Id).FirstOrDefault();
                    if (item != null && !item.IsTimeBased)
                    {
                        List<WorkoutLogSerieModelRef> itemSets = new List<WorkoutLogSerieModelRef>();
                        foreach (WorkoutLogSerieModelRef i in exer)
                        {
                            itemSets.Add(i);
                        }
                        var sets = exer;
                        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                        {
                            var nextSet = itemSets.Where(x => x.IsNext).FirstOrDefault();
                            if (nextSet != null)
                            {
                                Timer.Instance.NextRepsCount = nextSet.Reps;
                            }
                            else
                            {
                                Timer.Instance.NextRepsCount = 0;
                            }
                        }
                    }
                    else
                    {
                        if (item != null && item.IsTimeBased)
                        {
                            List<WorkoutLogSerieModelRef> itemSets = new List<WorkoutLogSerieModelRef>();
                            foreach (WorkoutLogSerieModelRef i in exer)
                            {
                                itemSets.Add(i);
                            }
                            var sets = exer;
                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                            {
                                var nextSet = itemSets.Where(x => x.IsNext).FirstOrDefault();
                                if (nextSet != null)
                                {
                                    timeRemain = Convert.ToString(nextSet.Reps);
                                }
                            }
                        }
                        Timer.Instance.NextRepsCount = 0;
                    }
                }
                catch (Exception ex)
                {

                }
            }

            }
            catch (Exception ex)
            {

            }
        }
        private void UpdateSpeed(WorkoutLogSerieModelRef models)
        {
            if (contextMenuStack != null)
                HideContextButton();
            try
            {
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    //Update rest of sets from this update model
                    var index = item.IndexOf(models);

                    for (int i = index; i < item.Count; i++)
                    {
                        
                        WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[i];
                        updatingItem.Speed = models.Speed;
                        
                    }
                    break;
                }


            }
            catch (Exception ex)
            {

            }
        }

        private void UpdateOneRM(WorkoutLogSerieModelRef models, decimal weight, int reps)
        {
            if (contextMenuStack != null)
                HideContextButton();
            try
            {
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    //Update rest of sets from this update model
                    var index = item.IndexOf(models);

                    if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null && item.RecoModel.FirstWorkSetWeight.Entered != 0)
                    {
                        var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                        if (models.IsBodyweight)
                            weight = isKg ? item.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture);
                        else
                        {
                            weight = Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                        }
                        var lastOneRM = item.RecoModel.FirstWorkSet1RM.Kg;//
                        lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);

                        var newWeight = Math.Round(new MultiUnityWeight(weight, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);

                        var currentRM = TruncateDecimal(ComputeOneRM(newWeight, reps), 2);
                        if (models.IsAssisted)
                        {
                            currentRM = TruncateDecimal(ComputeOneRM(_userBodyWeight - newWeight, models.Reps), 2);
                        }
                        lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);

                        currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);

                        var worksets = string.Format("{0:0.#} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                        if (models.IsAssisted)
                            worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? _userBodyWeight - item.RecoModel.FirstWorkSetWeight.Kg : new MultiUnityWeight(_userBodyWeight, WeightUnities.kg).Lb - (item.RecoModel.FirstWorkSetWeight.Lb), 2), isKg ? "kg" : "lbs");     

                        if (models.IsBodyweight)
                        {
                            worksets = "body";
                            if (models.Id == 16508 || models.BodypartId == 12)
                            {
                                worksets = string.Format("{0:0.##}", item.RecoModel.Speed);// "fast";
                            }
                            
                        }
                        if (models.Id >= 16897 && models.Id <= 16907 || models.Id == 14279 || models.Id >= 21508 && models.Id <= 21514)
                        {
                            var band = Utility.HelperClass.GetBandsColor((double)Math.Round((isKg ? models.Weight.Kg : models.Weight.Lb), 2), isKg);
                            if (!string.IsNullOrEmpty(band))
                                worksets = band.ToLowerInvariant();
                        }
                        if (currentRM != 0)
                        {
                            var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                            if (item.RecoModel.FirstWorkSetWeight.Kg == TruncateDecimal(new MultiUnityWeight(weight + (item.IsWeighted ? _userBodyWeight : 0), isKg ? "kg" : "lb").Kg, 2) && item.RecoModel.FirstWorkSetReps == reps)
                                percentage = 0;
                            if (models.IsMaxChallenge)
                            {
                                models.LastTimeSet = string.Format("Last time: {0} x {1}", item.BodyPartId == 12 ? TimeSpan.FromSeconds(item.RecoModel.FirstWorkSetReps).ToString(@"mm\:ss") : item.RecoModel.FirstWorkSetReps.ToString(), worksets);
                                //models.SetTitle = string.Format("Try max reps", item.RecoModel.FirstWorkSetReps, worksets);
                            }

                            else
                            {
                                models.LastTimeSet = string.Format("Last time: {0} x {1}", item.BodyPartId == 12 ? TimeSpan.FromSeconds(item.RecoModel.FirstWorkSetReps).ToString(@"mm\:ss") : item.RecoModel.FirstWorkSetReps.ToString(), worksets);
                               // models.SetTitle = string.Format("Today: {0}{1:0.0}%", percentage >= 0 ? "+" : "", percentage);
                            }

                            if (models.HeaderTitle.Equals("Deload"))
                            {
                                models.SetTitle = string.Format("Deload: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                            }
                            else if (models.HeaderTitle.Equals("Light session"))
                            {
                                models.SetTitle = string.Format("Light session: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                            }
                            else
                                models.SetTitle = string.Format("Today: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                            popup?.SetLastTimeOnlyText(string.Format("{0}{1:0.0}%:", percentage >= 0 ? "+" : "", percentage), string.Format("Last time: {0} x {1}", item.BodyPartId == 12 ? TimeSpan.FromSeconds(item.RecoModel.FirstWorkSetReps).ToString(@"mm\:ss") : item.RecoModel.FirstWorkSetReps.ToString(), worksets));
                        }
                    }
                    break;
                }


            }
            catch (Exception ex)
            {

            }
        }
        private string GetKeyValue(bool isPlate, bool isDumbbell, bool isPulley, bool isBands, bool isKg)
        {
            var keyVal = "";
            if (isPlate)
            {

                if (isKg)
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("PlatesKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("PlatesKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomePlatesKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherPlatesKg").Value;
                    }
                }
                else
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("PlatesLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("PlatesLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomePlatesLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherPlatesLb").Value;
                    }
                }

            }
            if (isDumbbell)
            {

                if (isKg)
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("DumbbellKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("DumbbellKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomeDumbbellKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherDumbbellKg").Value;
                    }
                }
                else
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("DumbbellLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("DumbbellLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomeDumbbellLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherDumbbellLb").Value;
                    }
                }
            }
            if (isBands)
            {

                if (isKg)
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomeBandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherBandsKg").Value;
                    }
                }
                else
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomeBandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherBandsLb").Value;
                    }
                }
            }
            if (isPulley)
            {

                if (isKg)
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("PulleyKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("PulleyKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomePulleyKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherPulleyKg").Value;
                    }
                }
                else
                {
                    keyVal = LocalDBManager.Instance.GetDBSetting("PulleyLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("PulleyLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("HomePulleyLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        keyVal = LocalDBManager.Instance.GetDBSetting("OtherPulleyLb").Value;
                    }
                }
            }
            return keyVal;
        }
        private void SetCoachTipTitle(WorkoutLogSerieModelRef models, ExerciseWorkSetsModel item, bool isPopup = false)
        {
            try
            {

            
            bool isWeighted = true;
            var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
            var userWeight = isKg ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb ;

            if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null &&
                item.RecoModel.FirstWorkSetWeight.Entered != 0)
            {
                var lastOneRM =
                    ComputeOneRM(
                        new MultiUnityWeight(
                            isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb,
                            isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0),
                        item.RecoModel.FirstWorkSetReps);
                if (models.IsAssisted)
                {
                    lastOneRM = ComputeOneRM( (new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg), item.RecoModel.FirstWorkSetReps);
                }
                decimal currentRM = 0;
                if (models.IsBodyweight)
                    currentRM = ComputeOneRM(
                        new MultiUnityWeight(
                            isKg
                                ? item.RecoModel.FirstWorkSetWeight.Kg
                                : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture),
                            isKg ? "kg" : "lb").Kg, models.Reps);
                else
                {
                    //Older implementaion
                    //currentRM = ComputeOneRM(new MultiUnityWeight(isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg + (RecoComputation.IsWeightedExercise(item.Id) ? _userBodyWeight : 0), models.Reps);

                    ////New implementaion
                    var w = isKg
                        ? models.Weight.Kg
                        : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);

                    var newWeight =
                        Math.Round(
                            new MultiUnityWeight(w, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0),
                            2);
                    if (item.IsWeighted)
                    {
                        newWeight = Math.Round(new MultiUnityWeight(isKg ? models.Weight.Kg : models.Weight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);
                                
                        currentRM = TruncateDecimal(ComputeOneRM(newWeight, models.Reps), 2);
                            
                        lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);
                                
                    }
                    else
                    {
                        currentRM = TruncateDecimal(ComputeOneRM(newWeight, models.Reps), 2);
                    }

                    if (models.IsAssisted)
                    {
                        currentRM = TruncateDecimal(ComputeOneRM(_userBodyWeight - newWeight, models.Reps), 2);
                    }
                }

                //=====
                lastOneRM = Math.Round(
                    isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                currentRM = Math.Round(
                    isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                //=====



                var worksets = string.Format("{0:0.##} {1}",
                    Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2),
                    isKg ? "kg" : "lbs");

                if (models.IsAssisted)
                    worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? _userBodyWeight - item.RecoModel.FirstWorkSetWeight.Kg : new MultiUnityWeight(_userBodyWeight, WeightUnities.kg).Lb - (item.RecoModel.FirstWorkSetWeight.Lb), 2), isKg ? "kg" : "lbs");
                if (models.IsBodyweight)
                {
                    worksets = "body";
                    if (models.BodypartId == 12)
                    {
                            worksets = string.Format("{0:0.##}", item.RecoModel.Speed);//"fast";
                    }
                }
                    if (models.Id >= 16897 && models.Id <= 16907 || models.Id == 14279 || models.Id >= 21508 && models.Id <= 21514)
                    {
                        var matchBands = HelperClass.GetBandsColor((double)(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb), isKg);
                        if (!string.IsNullOrEmpty(matchBands))
                            worksets = matchBands.ToLower();
                    }
                    if (currentRM != 0)
                {
                    var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;

                    var newWeigh =
                        new MultiUnityWeight(
                            isKg
                                ? models.Weight.Kg
                                : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture),
                            isKg ? "kg" : "lb");
                    if (item.RecoModel.FirstWorkSetWeight.Kg == TruncateDecimal(newWeigh.Kg, 2) &&
                        item.RecoModel.FirstWorkSetReps == models.Reps)
                        percentage = 0;

                    var lastTimeTexttype = "";
                                                    if (item.RecoModel.RIR != null) {

                     if (item.IsEasy || item.IsMedium)
                        {
                            if (item.IsEasy && item.RecoModel.RIR < 3)
                                lastTimeTexttype = " (too hard)";
                            if (item.IsMedium && item.RecoModel.RIR < 2)
                                lastTimeTexttype = " (too hard)";
                        }
                        else
                        {
                            if (item.RecoModel.RIR == 0)
                                lastTimeTexttype = " (very hard)";
                        }

                    }
                    models.LastTimeSet =
                        string.Format("Last time: {0} x {1}{2}", item.BodyPartId == 12 ? TimeSpan.FromSeconds(item.RecoModel.FirstWorkSetReps).ToString(@"mm\:ss"): item.RecoModel.FirstWorkSetReps.ToString(), worksets, lastTimeTexttype);
                    models.SetTitle = string.Format("Today: {0}{1:0.00}%", percentage >= 0 ? "+" : "", percentage);
                    if (models.IsFirstWorkSet && models.HeaderTitle.Equals("Deload"))
                    {
                        models.SetTitle = string.Format("Deload: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                    }
                    else if (models.IsFirstWorkSet && models.HeaderTitle.Equals("Light session"))
                    {
                        models.SetTitle = string.Format("Light session: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                    }
                    else if (models.IsFirstWorkSet && models.IsMaxChallenge)
                    {
                        //models.SetTitle = "Try max reps";
                        models.SetTitle = string.Format("Try max reps: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                    }
                    if (isPopup)
                            popup.SetLastTimeText(string.Format("{0}{1:0.##}%:", percentage >= 0 ? "+" : "", percentage), string.Format("Last time: {0} x {1}", item.BodyPartId == 12 ? TimeSpan.FromSeconds(item.RecoModel.FirstWorkSetReps).ToString(@"mm\:ss") : item.RecoModel.FirstWorkSetReps.ToString(), worksets));
                    }

            }
            }
            catch (Exception ex)
            {

            }
        }
        
        private void UpdateWeoghtRepsMessageTapped(WorkoutLogSerieModelRef models)
        {
            try
            {
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    if (item.IsFinished)
                    {
                        item.RecoModel = null;
                        return;
                    }
                    //Update rest of sets from this update model

                    var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;

                    var index = item.IndexOf(models);
                    if (item.IsReversePyramid && item.IsWeighted)
                    {
                        bool isWeighted = true;
                        var userWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb;

                        if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null && item.RecoModel.FirstWorkSetWeight.Entered != 0)
                        {
                            // var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);
                            //
                            // decimal currentRM = 0;
                            // if (models.IsBodyweight)
                            //     currentRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg, models.Reps);
                            // else
                            // {
                            //     var w = isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            //     var newWeight = Math.Round(new MultiUnityWeight(w, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);
                            //     currentRM = TruncateDecimal(ComputeOneRM(newWeight, models.Reps), 2);
                            // }
                            //
                            //
                            // //=====
                            // lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                            // currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                            // //=====
                            //
                            //
                            //
                            // var worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                            //
                            // if (currentRM != 0)
                            // {
                            //     //var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                            //     var percentage = Device.RuntimePlatform.Equals(Device.Android) ? (currentRM - lastOneRM) * 100 / lastOneRM : (currentRM - lastOneRM) * 100 / lastOneRM;
                            //     var newWeigh = new MultiUnityWeight(isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg ? "kg" : "lb");
                            //     if (item.RecoModel.FirstWorkSetWeight.Kg == TruncateDecimal(newWeigh.Kg, 2) && item.RecoModel.FirstWorkSetReps == models.Reps)
                            //         percentage = 0;
                            //
                            //     models.LastTimeSet = string.Format("Last time: {0} x {1}", item.RecoModel.FirstWorkSetReps, worksets);
                            //     models.SetTitle = string.Format("Today: {0}{1:0.00}%", percentage >= 0 ? "+" : "", percentage);
                            // }
                            SetCoachTipTitle(models, item);
                        }


                        decimal weightdif = 0;
                        int repsdif = 0;
                        for (int j = index; j < item.Count; j++)
                        {

                            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j];
                            if (j == index)
                            {
                                updatingItem.Weight = models.Weight;
                                updatingItem.Reps = models.Reps;

                                if (updatingItem.WeightDouble != updatingItem.PreviousWeightDouble)
                                {
                                    var currentWeigght = Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                    var prevWeigght = Convert.ToDecimal(models.PreviousWeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                    weightdif = prevWeigght - currentWeigght;
                                }

                                if (models.PreviousReps != models.Reps)
                                {
                                    repsdif = updatingItem.PreviousReps - updatingItem.Reps;
                                }
                                continue;
                            }

                            if (!updatingItem.IsFinished && !updatingItem.IsWarmups)
                            {
                                decimal weight = 1;

                                var weigh = Convert.ToDecimal(updatingItem.PreviousWeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                if (weightdif > 0)
                                    weight = weigh - Math.Abs(weightdif / 2);
                                else
                                    weight = weigh + Math.Abs(weightdif / 2);
                                updatingItem.Weight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(weight, item.RecoModel.Increments == null ? isKg ? (decimal)2.0 : (decimal)5.0 : isKg ? item.RecoModel.Increments.Kg : item.RecoModel.Increments.Lb, isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb), isKg ? "kg" : "lb");

                                if (repsdif > 0)
                                    updatingItem.Reps = updatingItem.PreviousReps - Math.Abs(repsdif / 2);
                                else
                                    updatingItem.Reps = updatingItem.PreviousReps + Math.Abs(repsdif / 2);

                                if (weight <= 0)
                                {

                                    weight = updatingItem.Increments != null ? updatingItem.Increments.Kg : (decimal)2; ;

                                    updatingItem.Weight = new MultiUnityWeight(weight, "kg");
                                }
                                if (updatingItem.Reps < 1)
                                    updatingItem.Reps = 1;

                            }
                        }
                        //==========
                    }
                    else if (item.IsReversePyramid)
                    {

                        if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null && item.RecoModel.FirstWorkSetWeight.Entered != 0)
                        {
                            // var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);
                            //
                            // decimal currentRM = 0;
                            // if (models.IsBodyweight)
                            //     currentRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg, models.Reps);
                            // else
                            // {
                            //     var w = isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            //     var newWeight = Math.Round(new MultiUnityWeight(w, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);
                            //     currentRM = TruncateDecimal(ComputeOneRM(newWeight, models.Reps), 2);
                            // }
                            //
                            //
                            // //=====
                            // lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                            // currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                            // //=====
                            //
                            //
                            //
                            //
                            // var worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                            //
                            // if (currentRM != 0)
                            // {
                            //     var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                            //
                            //     var newWeigh = new MultiUnityWeight(isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg ? "kg" : "lb");
                            //     if (item.RecoModel.FirstWorkSetWeight.Kg == TruncateDecimal(newWeigh.Kg, 2) && item.RecoModel.FirstWorkSetReps == models.Reps)
                            //         percentage = 0;
                            //
                            //     models.LastTimeSet = string.Format("Last time: {0} x {1}", item.RecoModel.FirstWorkSetReps, worksets);
                            //     models.SetTitle = string.Format("Today: {0}{1:0.00}%", percentage >= 0 ? "+" : "", percentage);
                            // }
                            SetCoachTipTitle(models, item);
                        }


                        decimal weightdif = 0;
                        int repsdif = 0;
                        for (int j = index; j < item.Count; j++)
                        {

                            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j];
                            if (j == index)
                            {
                                updatingItem.Weight = models.Weight;
                                updatingItem.Reps = models.Reps;

                                if (updatingItem.WeightDouble != updatingItem.PreviousWeightDouble)
                                {
                                    var currentWeigght = Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                    var prevWeigght = Convert.ToDecimal(models.PreviousWeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                    weightdif = prevWeigght - currentWeigght;
                                }

                                if (models.PreviousReps != models.Reps)
                                {
                                    repsdif = updatingItem.PreviousReps - updatingItem.Reps;
                                }
                                continue;
                            }

                            if (!updatingItem.IsFinished && !updatingItem.IsWarmups)
                            {
                                decimal weight = 1;

                                var weigh = Convert.ToDecimal(updatingItem.PreviousWeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                if (weightdif > 0)
                                    weight = weigh - Math.Abs(weightdif / 2);
                                else
                                    weight = weigh + Math.Abs(weightdif / 2);
                                updatingItem.Weight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(weight, item.RecoModel.Increments == null ? isKg ? (decimal)2.0 : (decimal)5.0 : isKg ? item.RecoModel.Increments.Kg : item.RecoModel.Increments.Lb, isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb), isKg ? "kg" : "lb");

                                if (repsdif > 0)
                                    updatingItem.Reps = updatingItem.PreviousReps - Math.Abs(repsdif / 2);
                                else
                                    updatingItem.Reps = updatingItem.PreviousReps + Math.Abs(repsdif / 2);

                                if (weight <= 0)
                                {

                                    weight = updatingItem.Increments != null ? updatingItem.Increments.Kg : (decimal)2; ;

                                    updatingItem.Weight = new MultiUnityWeight(weight, "kg");
                                }
                                if (updatingItem.Reps < 1)
                                    updatingItem.Reps = 1;

                            }
                        }
                        //==========
                    }
                    else if (!item.IsPyramid)
                    {
                        var reps = models.Reps;
                        if (item.IsNormalSets)
                            reps = models.Reps;
                        else
                        {
                            if (models.IsFirstWorkSet)
                                reps = models.Reps <= 5 ? (int)Math.Ceiling((decimal)models.Reps / (decimal)3.0) : (int)models.Reps / 3;

                            if (reps <= 0)
                                reps = 1;
                        }
                        if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null && item.RecoModel.FirstWorkSetWeight.Entered != 0)
                        {
                            // var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);
                            //
                            // decimal currentRM = 0;
                            // if (models.IsBodyweight)
                            //     currentRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg, models.Reps);
                            // else
                            // {
                            //     //Older implementaion
                            //     //currentRM = ComputeOneRM(new MultiUnityWeight(isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg + (RecoComputation.IsWeightedExercise(item.Id) ? _userBodyWeight : 0), models.Reps);
                            //
                            //     ////New implementaion
                            //     var w = isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            //
                            //     var newWeight = Math.Round(new MultiUnityWeight(w, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);
                            //
                            //     currentRM = TruncateDecimal(ComputeOneRM(newWeight, models.Reps), 2);
                            //
                            //
                            //
                            // }
                            //
                            // //=====
                            // lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                            // currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                            // //=====
                            //
                            //
                            //
                            // var worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                            //
                            //
                            // if (models.IsBodyweight)
                            // {
                            //     worksets = "body";
                            //     if (models.Id == 16508)
                            //     {
                            //         worksets = "fast";
                            //     }
                            //     if (models.Id >= 16897 && models.Id <= 16907 || models.Id == 14279 || models.Id >= 21508 && models.Id <= 21514)
                            //     {
                            //         worksets = "bands";
                            //     }
                            // }
                            //
                            // if (currentRM != 0)
                            // {
                            //     var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                            //
                            //     var newWeigh = new MultiUnityWeight(isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg ? "kg" : "lb");
                            //     if (item.RecoModel.FirstWorkSetWeight.Kg == TruncateDecimal(newWeigh.Kg, 2) && item.RecoModel.FirstWorkSetReps == models.Reps)
                            //         percentage = 0;
                            //
                            //     models.LastTimeSet = string.Format("Last time: {0} x {1}", item.RecoModel.FirstWorkSetReps, worksets);
                            //     models.SetTitle = string.Format("Today: {0}{1:0.00}%", percentage >= 0 ? "+" : "", percentage);
                            // }
                            SetCoachTipTitle(models, item);
                        }

                        for (int i = index; i < item.Count; i++)
                        {
                            //if (item[i] == models)
                            //    continue;
                            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[i];
                            if (updatingItem.IsBackOffSet && !updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                            {
                                double? sliderVal = null;
                                var weights = isKg ? (item.IsWeighted ? _userBodyWeight : 0) + models.Weight.Kg : (item.IsWeighted ? _userBodyWeight : 0) + models.Weight.Lb;
                                if (isKg)
                                {

                                    if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("KgBarWeight")?.Value))
                                    {
                                        sliderVal = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting("KgBarWeight")?.Value, System.Globalization.CultureInfo.InvariantCulture);
                                    }
                                    else
                                        sliderVal = 20;
                                }
                                else
                                {
                                    if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("LbBarWeight")?.Value))
                                    {
                                        sliderVal = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting("LbBarWeight")?.Value, System.Globalization.CultureInfo.InvariantCulture);
                                    }
                                    else
                                        sliderVal = 45;
                                }
                                var mod = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(weights - (weights * (decimal)0.3), item.RecoModel.Increments == null ? (decimal)1.0 : isKg ? item.RecoModel.Increments.Kg : TruncateDecimal(item.RecoModel.Increments.Lb, 5), isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb) - (item.IsWeighted ? _userBodyWeight : 0), isKg ? "kg" : "lb");
                                if (item.RecoModel.isPlateAvailable)
                                {
                                    mod = RecoComputation.GetPlatesWeight(GetKeyValue(true, false, false, false, isKg), weights - (weights * (decimal)0.3), (double)sliderVal, isKg);
                                }
                                if (updatingItem.IsDumbbellAvailable)
                                {
                                    mod = RecoComputation.GetDumbbellWeight(GetKeyValue(false, true, false, false, isKg), weights - (weights * (decimal)0.3), isKg);
                                }
                                if (updatingItem.IsPulleyAvailable)
                                {
                                    mod = RecoComputation.GetPlatesWeight(GetKeyValue(false, false, true, false, isKg), weights - (weights * (decimal)0.3), 0, isKg);
                                }
                                if (updatingItem.IsBandsAvailable)
                                {
                                    mod = RecoComputation.GetBandsWeight(GetKeyValue(false, false, false, true, isKg), weights - (weights * (decimal)0.3), isKg);
                                }
                                if (item.IsWeighted)
                                {
                                    decimal wei = Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                    var inc = isKg ? item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg : item.RecoModel.Increments == null ? (decimal)5.0 : Math.Round(item.RecoModel.Increments.Lb);
                                    wei = item.IsWeighted ? wei + (isKg ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb) : wei;
                                    item.RecoModel.BackOffSetWeight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)wei - wei * (decimal)0.3, inc, isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb) - (item.IsWeighted ? (isKg ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb) : 0), isKg ? "kg" : "lb");
                                    if (item.RecoModel.BackOffSetWeight.Kg <= 0)
                                        item.RecoModel.BackOffSetWeight = new MultiUnityWeight(0, "kg");
                                    if (item.IsWeighted)
                                    {
                                        item.RecoModel.BackOffSetWeight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(isKg ? item.RecoModel.BackOffSetWeight.Kg : item.RecoModel.BackOffSetWeight.Lb, inc, isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb), isKg ? "kg" : "lb");
                                        mod = item.RecoModel.BackOffSetWeight;
                                    }
                                    //mod = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(isKg ? item.RecoModel.BackOffSetWeight.Kg : item.RecoModel.BackOffSetWeight.Lb, item.RecoModel.Increments == null ? (decimal)1.0 : isKg ? item.RecoModel.Increments.Kg : TruncateDecimal(item.RecoModel.Increments.Lb, 5), isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb), isKg ? "kg" : "lb");


                                  
                                }
                                if (item.IsAssisted)
                                {
                                    /*Computations: userbodyweight - last set weight 195-40= 145 applied 30 percent = 145-30% = 100 
                                195-100 = 100
                                */
                                    var wei = Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(),
                                        CultureInfo.InvariantCulture);
                                    wei =
                                        _userBodyWeight - (isKg? wei : new MultiUnityWeight(wei, "lb").Kg);
                                    wei = _userBodyWeight - ( (decimal)wei - wei * (decimal)0.3);
                                    if (wei<0)
                                        wei = 0;
                                    wei = isKg? wei : new MultiUnityWeight(wei, "kg").Lb;
                                    item.RecoModel.BackOffSetWeight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(wei,isKg ? item.RecoModel.Increments.Kg : item.RecoModel.Increments.Lb, isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb) - (item.IsWeighted ? (isKg ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb) : 0), isKg ? "kg" : "lb");
                                    mod = item.RecoModel.BackOffSetWeight;
                                }
                                if (mod.Kg <= 0 && item.IsWeighted)
                                    mod = new MultiUnityWeight(0, "kg");


                                updatingItem.Weight = mod;
                                if (Math.Abs(updatingItem.Weight.Kg - models.Weight.Kg) > 0)
                                {
                                    var ob = ((Math.Abs(models.Weight.Kg - updatingItem.Weight.Kg) / models.Weight.Kg) > (decimal)0.3 ? (decimal)0.7 : Math.Abs(models.Weight.Kg - updatingItem.Weight.Kg) / models.Weight.Kg * (decimal)0.7 / (decimal)0.3);// Changed 3.66 to 3 after Bruno issue
                                    if (item.IsWeighted)
                                    {
                                        var weightChange = (models.Weight.Kg + _userBodyWeight) - (item.RecoModel.BackOffSetWeight.Kg + _userBodyWeight);
                                        if (weightChange < 0)
                                            weightChange = 0;

                                        ob = ((Math.Abs(item.RecoModel.Weight.Kg - item.RecoModel.BackOffSetWeight.Kg) / (item.RecoModel.Weight.Kg == 0 ? 2 : item.RecoModel.Weight.Kg)) > (decimal)0.3 ? (decimal)0.7 : Math.Abs(item.RecoModel.Weight.Kg - item.RecoModel.BackOffSetWeight.Kg) / (item.RecoModel.Weight.Kg == 0 ? 2 : item.RecoModel.Weight.Kg) * (decimal)0.7);
                                        if (item.IsWeighted)
                                        {
                                            if ((Math.Abs((models.Weight.Kg + _userBodyWeight) - (item.RecoModel.BackOffSetWeight.Kg + _userBodyWeight)) / (models.Weight.Kg + _userBodyWeight)) > (decimal)0.3)
                                            {
                                                ob = (decimal)0.7;
                                            }
                                            else
                                            {
                                                ob = Math.Abs((models.Weight.Kg + _userBodyWeight) - (item.RecoModel.BackOffSetWeight.Kg + _userBodyWeight)) / (models.Weight.Kg + _userBodyWeight) * (decimal)0.7 / (decimal)0.3;

                                            }

                                            var newWeight = weightChange - (weightChange * (decimal)0.3);
                                            if (newWeight - _userBodyWeight > 0)
                                                ob = (decimal)0.7;
                                            else
                                            {
                                                var percentChange = models.Weight.Kg / (models.Weight.Kg + _userBodyWeight);
                                                if (percentChange > (decimal)0.3)
                                                    percentChange = (decimal)0.3;
                                                ob = percentChange * (decimal)0.7 / (decimal)0.3;
                                                
                                            }
                                        }
                                    }
                                    updatingItem.Reps = (int)reps + (int)Math.Ceiling(reps * ob);
                                }
                                else
                                {
                                    updatingItem.Reps = (int)(reps + Math.Ceiling(reps * 0.2));
                                }

                                continue;
                            }
                            if (!updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                            {
                                updatingItem.Weight = models.Weight;
                                if (reps != 0)
                                    updatingItem.Reps = reps;
                            }
                        }
                    }
                    else if (item.IsWeighted)
                    {
                        //Reverse pyramid
                        if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null && item.RecoModel.FirstWorkSetWeight.Entered != 0)
                        {
                            //var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                            // var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);
                            //
                            // decimal currentRM = 0;
                            // if (models.IsBodyweight)
                            //     currentRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg, models.Reps);
                            // else
                            // {
                            //     var w = isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            //     var newWeight = Math.Round(new MultiUnityWeight(w, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);
                            //     currentRM = TruncateDecimal(ComputeOneRM(newWeight, models.Reps), 2);
                            //
                            // }
                            //
                            // //=====
                            // lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                            // currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                            // //=====
                            //
                            // var worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                            //
                            // if (currentRM != 0)
                            // {
                            //     var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                            //
                            //     var newWeigh = new MultiUnityWeight(isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg ? "kg" : "lb");
                            //     if (item.RecoModel.FirstWorkSetWeight.Kg == TruncateDecimal(newWeigh.Kg, 2) && item.RecoModel.FirstWorkSetReps == models.Reps)
                            //         percentage = 0;
                            //
                            //     models.LastTimeSet = string.Format("Last time: {0} x {1}", item.RecoModel.FirstWorkSetReps, worksets);
                            //     models.SetTitle = string.Format("Today: {0}{1:0.00}%", percentage >= 0 ? "+" : "", percentage);
                            // }
                            SetCoachTipTitle(models, item);
                        }

                        bool isWeighted = true;
                        var userWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb;

                        for (int j = index; j < item.Count; j++)
                        {

                            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j];
                            if (j == index)
                            {
                                updatingItem.Weight = models.Weight;
                                updatingItem.Reps = models.Reps;
                                continue;
                            }

                            if (!updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                            {
                                var count = item.Where(x => x.IsWarmups == true).Count();
                                var rec = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j - 1];
                                var last = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[index];
                                var reps = rec.Reps + (j - count) + 1;
                                var lstWeight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                decimal weight = RecoComputation.RoundToNearestIncrementPyramid(lstWeight + userWeight - ((userWeight + lstWeight) * ((decimal)0.1)), item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg, userWeight, item.RecoModel.Max?.Kg) - userWeight;
                                if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                                {
                                    if (weight >= last.Weight.Kg)
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(userWeight + rec.Weight.Kg - ((userWeight + rec.Weight.Kg) * (decimal)0.1), item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg, userWeight, item.RecoModel.Max?.Kg) - userWeight;
                                        if (weight == last.Weight.Kg)
                                        {
                                            updatingItem.Reps = rec.Reps;
                                        }
                                        else
                                            updatingItem.Reps = reps;
                                    }
                                    else
                                        updatingItem.Reps = reps;
                                    updatingItem.Weight = new MultiUnityWeight(weight, "kg");
                                }
                                else
                                {
                                    var inc = updatingItem.Increments != null ? Math.Round(updatingItem.Increments.Lb, 2) : (decimal)5;

                                    weight = RecoComputation.RoundToNearestIncrementPyramid(userWeight + lstWeight - (userWeight + lstWeight) * ((decimal)0.1), inc, userWeight, item.RecoModel.Max?.Lb);

                                    if (SaveSetPage.RoundDownToNearestIncrement(weight, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb) >= SaveSetPage.RoundDownToNearestIncrement(rec.Weight.Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb))
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(userWeight + lstWeight - (((userWeight + lstWeight) * ((decimal)0.1))), item.RecoModel.Increments == null ? (decimal)5 : item.RecoModel.Increments.Lb, userWeight, item.RecoModel.Max?.Lb) - userWeight;
                                        if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb) == SaveSetPage.RoundDownToNearestIncrement(rec.Weight.Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb))
                                        {
                                            updatingItem.Reps = rec.Reps; //
                                        }
                                        else
                                        {
                                            updatingItem.Reps = reps;
                                        }
                                        weight = new MultiUnityWeight(weight, "lb").Lb;
                                    }
                                    else
                                        updatingItem.Reps = reps;
                                    updatingItem.Weight = new MultiUnityWeight(SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb), "lb");
                                }
                                if (weight <= 0)
                                {
                                    updatingItem.Reps = last.Reps;
                                    weight = updatingItem.Increments != null ? isKg ? updatingItem.Increments.Kg : updatingItem.Increments.Lb : isKg ? 2 : 5;
                                    //if (last.Weight.Kg > (decimal)1.15)
                                    //    updatingItem.Reps = last.Reps + j + 1;
                                    updatingItem.Weight = new MultiUnityWeight(0, "kg");
                                }
                                if (updatingItem.WeightDouble.ReplaceWithDot() == rec.WeightDouble.ReplaceWithDot())
                                    updatingItem.Reps = rec.Reps;
                            }
                        }
                    }
                    else
                    {
                        //Reverse pyramid
                        if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null && item.RecoModel.FirstWorkSetWeight.Entered != 0)
                        {
                            //var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                            var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);

                            decimal currentRM = 0;
                            if (models.IsBodyweight)
                                currentRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg, models.Reps);
                            else
                            {
                                var w = isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                var newWeight = Math.Round(new MultiUnityWeight(w, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);
                                currentRM = TruncateDecimal(ComputeOneRM(newWeight, models.Reps), 2);

                            }

                            //=====
                            lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                            currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                            //=====

                            var worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");

                            if (currentRM != 0)
                            {
                                var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;

                                var newWeigh = new MultiUnityWeight(isKg ? models.Weight.Kg : Convert.ToDecimal(models.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg ? "kg" : "lb");
                                if (item.RecoModel.FirstWorkSetWeight.Kg == TruncateDecimal(newWeigh.Kg, 2) && item.RecoModel.FirstWorkSetReps == models.Reps)
                                    percentage = 0;

                                models.LastTimeSet = string.Format("Last time: {0} x {1}", item.BodyPartId == 12 ? item.RecoModel.FirstWorkSetReps.ToString(@"mm\:ss") : item.RecoModel.FirstWorkSetReps.ToString(), worksets);
                                models.SetTitle = string.Format("Today: {0}{1:0.00}%", percentage >= 0 ? "+" : "", percentage);
                            }
                        }
                        for (int j = index; j < item.Count; j++)
                        {

                            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j];
                            if (j == index)
                            {
                                updatingItem.Weight = models.Weight;
                                updatingItem.Reps = models.Reps;
                                continue;
                            }

                            if (!updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                            {
                                var count = item.Where(x => x.IsWarmups == true).Count();
                                var rec = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j - 1];
                                var last = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[index];
                                var reps = rec.Reps + (j - count) + 1;
                                var lstWeight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                               // decimal weight = RecoComputation.RoundToNearestIncrementPyramid(lstWeight - (lstWeight * ((decimal)0.1)), item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                                decimal weight = RecoComputation.RoundToNearestIncrementPyramid(models.IsAssisted ? lstWeight + (lstWeight * ((decimal)0.1)) : lstWeight - (lstWeight * ((decimal)0.1)), item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                                if (item.IsAssisted)
                                {
                                    weight = SaveSetPage.RoundDownToNearestIncrement(
                                        lstWeight + (lstWeight * ((decimal)0.1)),
                                        item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg,
                                        item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg, true);
                                    weight =  RecoComputation.RoundToNearestIncrement(weight, item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                                }

                                if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                                {
                                    if (weight >= last.Weight.Kg && !models.IsAssisted)
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(rec.Weight.Kg - (item.RecoModel.Increments != null ? item.RecoModel.Increments.Kg : (rec.Weight.Kg * (decimal)0.1)), item.RecoModel.Increments == null ? (decimal)2.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                                        if (weight == last.Weight.Kg)
                                        {
                                            updatingItem.Reps = rec.Reps;
                                        }
                                        else
                                            updatingItem.Reps = reps;
                                    }
                                    else
                                        updatingItem.Reps = reps;
                                    updatingItem.Weight = new MultiUnityWeight(weight, "kg");
                                }
                                else
                                {
                                    var inc = updatingItem.Increments != null ? Math.Round(updatingItem.Increments.Lb, 2) : (decimal)5;

                                    if (models.IsAssisted)
                                    {
                                        weight = SaveSetPage.RoundDownToNearestIncrement(lstWeight + lstWeight * ((decimal)0.1), inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb, true);
                                    }
                                    else  
                                        weight = RecoComputation.RoundToNearestIncrementPyramid( lstWeight - lstWeight * ((decimal)0.1), inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb);

                                    if (SaveSetPage.RoundDownToNearestIncrement(weight, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb) >= SaveSetPage.RoundDownToNearestIncrement(rec.Weight.Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb))
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(lstWeight - (item.RecoModel.Increments != null ? item.RecoModel.Increments.Lb : (lstWeight * ((decimal)0.1))), item.RecoModel.Increments == null ? (decimal)5 : item.RecoModel.Increments.Lb, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb);
                                        if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb) == SaveSetPage.RoundDownToNearestIncrement(rec.Weight.Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb))
                                        {
                                            updatingItem.Reps = rec.Reps; //
                                        }
                                        else
                                        {
                                            updatingItem.Reps = reps;
                                        }
                                        weight = new MultiUnityWeight(weight, "lb").Lb;
                                    }
                                    else
                                        updatingItem.Reps = reps;
                                    updatingItem.Weight = new MultiUnityWeight(SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb, inc, item.RecoModel.Min?.Lb, item.RecoModel.Max?.Lb), "lb");
                                }
                                if (weight <= 0)
                                {
                                    updatingItem.Reps = last.Reps;
                                    weight = updatingItem.Increments != null ? isKg ? updatingItem.Increments.Kg : updatingItem.Increments.Lb : isKg ? 2 : 5;
                                    //if (last.Weight.Kg > (decimal)1.15)
                                    //    updatingItem.Reps = last.Reps + j + 1;
                                    updatingItem.Weight = new MultiUnityWeight(weight, isKg ? "kg" : "lb");
                                }
                                if (updatingItem.WeightDouble.ReplaceWithDot() == rec.WeightDouble.ReplaceWithDot())
                                    updatingItem.Reps = rec.Reps;
                            }
                        }
                    }
                    break;
                }


            }
            catch (Exception ex)
            {

            }
        }

        public decimal TruncateDecimal(decimal value, int precision)
        {
            decimal step = (decimal)Math.Pow(10, precision);
            decimal tmp = Math.Truncate(step * value);
            return tmp / step;
        }

        private void AddSetMessageTapped(WorkoutLogSerieModelRef models, bool hasFinished)
        {
            try
            {
                if (contextMenuStack != null)
                    HideContextButton();
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;

                    var newSet = new WorkoutLogSerieModelRef()
                    {
                        Id = item.Id,
                        IsLastSet = true,
                        IsFinished = false,
                        Weight = models.Weight,
                        Reps = models.Reps,
                        IsNext = models.IsFinished ? true : false,
                        SetNo = $"{item.Where(x=>x.IsWarmups == false).Count() + 1}",
                        IsFirstSide = item.IsFirstSide,
                        ExerciseName = item.Label,
                        EquipmentId = item.EquipmentId,
                        Increments = models.Increments,
                        SetTitle = "Last set—you can do this!",
                        IsTimeBased = models.IsTimeBased,
                        IsUnilateral = models.IsUnilateral,
                        IsBodyweight = models.IsBodyweight,
                        IsBackOffSet = models.IsBackOffSet,
                        IsFlexibility = models.IsFlexibility,
                        IsNormalset = models.IsNormalset,
                        BodypartId = models.BodypartId,
                        IsFirstWorkSet = models.IsFirstWorkSet,
                        IsActive = models.IsFinished ? true : false,
                        PreviousReps = models.Reps,
                        PreviousWeight = models.Weight,
                        Min = models.Min,
                        Max = models.Max,
                        IsExtraSet = true,
                        LastTimeSet = "",
                        IsOneHanded = item.IsOneHanded,
                        IsAssisted = item.IsAssisted,
                        Speed = models.Speed
                    };
                    //newSet.IsFirstSetFinished = true;
                    if (!models.IsFinished && item.Where(x => x.IsWarmups == false).Count() > 2 && !item.IsReversePyramid)
                    {

                        var models1 = (WorkoutLogSerieModelRef)item[item.IndexOf(models) - 1];

                        newSet = new WorkoutLogSerieModelRef()
                        {
                            Id = item.Id,
                            IsLastSet = hasFinished,
                            IsFinished = false,
                            Weight = models1.Weight,
                            Reps = models1.Reps,
                            IsNext = false,
                            SetNo = $"{item.Where(x=>x.IsWarmups == false).Count() + 1}",
                            IsFirstSide = item.IsFirstSide,
                            ExerciseName = item.Label,
                            EquipmentId = item.EquipmentId,
                            Increments = models1.Increments,
                            SetTitle = "You can do this!",
                            IsTimeBased = models1.IsTimeBased,
                            IsUnilateral = models1.IsUnilateral,
                            IsBodyweight = models1.IsBodyweight,
                            IsBackOffSet = models1.IsBackOffSet,
                            IsFlexibility = models1.IsFlexibility,
                            IsNormalset = models1.IsNormalset,
                            BodypartId = models1.BodypartId,
                            IsFirstWorkSet = models1.IsFirstWorkSet,
                            PreviousWeight = models1.PreviousWeight,
                            PreviousReps = models1.PreviousReps,
                            IsActive = false,
                            Min = models1.Min,
                            Max = models1.Max,
                            IsExtraSet = false,
                            LastTimeSet = "",
                            IsOneHanded = models1.IsOneHanded,
                            IsAssisted = models1.IsAssisted,
                            Speed = models1.Speed
                        };
                        //if (item.IsReversePyramid)
                        //{
                        //    newSet.Reps = (int)newSet.Reps + (int)Math.Ceiling(newSet.Reps * 0.4);
                        //    newSet.Weight = models.Weight;
                        //}
                    }
                    else if (!models.IsFinished)
                    {
                        models.IsLastSet = false;
                        models.IsFirstWorkSet = false;
                        //newSet.IsFirstSetFinished = true;
                    }
                    if (item.IsReversePyramid)
                    {
                        models.IsFirstWorkSet = false;
                        models.IsLastSet = false;
                        newSet.IsFirstWorkSet = true;

                    }
                    if (item.IsReversePyramid)
                    {
                        newSet.Reps = newSet.Reps - (int)((double)models.Reps * 0.3); //before was 0.4
                        var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
                        var weight = isKg ? (models.Weight.Kg + (models.Weight.Kg * (decimal)0.075)) : (models.Weight.Lb + (models.Weight.Lb * (decimal)0.075));
                        if (models.Reps != newSet.Reps)
                            newSet.Weight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(weight, item.RecoModel.Increments == null ? isKg ? (decimal)2.0 : (decimal)5.0 : isKg ? item.RecoModel.Increments.Kg : item.RecoModel.Increments.Lb, isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb), isKg ? "kg" : "lb");

                        newSet.PreviousReps = newSet.Reps;
                        newSet.PreviousWeight = newSet.Weight;
                    }
                    //models.IsFirstWorkSet = false;
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                    {
                        var listOfSets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                        if (listOfSets.Count(x => x.IsFinished) > 0)
                            newSet.IsFirstSetFinished = true;

                        //listOfSets.Add(newSet);
                        if (item.IsReversePyramid)
                        {
                            models.IsFirstWorkSet = false;
                            models.IsLastSet = false;
                            newSet.IsFirstWorkSet = true;
                            listOfSets.Add(newSet);
                        }
                        else
                        {
                            if (models.IsFinished || !models.IsLastSet)
                                listOfSets.Add(newSet);
                            else
                                listOfSets.Insert(item.IndexOf(models), newSet);
                        }
                    }
                    if (item.IsReversePyramid)
                    {
                        models.IsFirstWorkSet = false;
                        models.IsLastSet = false;
                        newSet.IsFirstWorkSet = true;
                        item.Add(newSet);
                    }
                    else
                    {
                        if (models.IsFinished || !models.IsLastSet)
                            item.Add(newSet);
                        else
                        {
                            item.Insert(item.IndexOf(models), newSet);
                        }
                    }

                    // for (var i = 0; i < item.Count; i++)
                    //     ((WorkoutLogSerieModelRef)item[i]).SetNo = $"{i + 1}";
                    SetIndex(item.ToList());
                    if (item.First().IsWarmups)
                    {
                        var warmString = item.Where(l => l.IsWarmups).ToList().Count < 2 ? "warm-up" : "warm-ups";
                        ((WorkoutLogSerieModelRef)item.First()).SetTitle = $"{item.Where(l => l.IsWarmups).ToList().Count} {warmString}, {item.Where(l => !l.IsWarmups).ToList().Count} work sets";
                    }
                    if (models.IsFinished)
                    {
                        ScrollToActiveSet(newSet, item);
                    }

                    Device.BeginInvokeOnMainThread(async () =>
                    {
                        await Task.Delay(200);
                        ExerciseListView.ScrollTo(models, ScrollToPosition.Start, true);
                    });
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    if (models.IsFinished)
                    {
                        if (Timer.Instance.State != "RUNNING")
                        {
                            if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                            {
                                LocalDBManager.Instance.SetDBSetting("timer_remaining", CurrentLog.Instance.GetRecommendationRestTime(models.Id, false, models.IsNormalset, CurrentLog.Instance.RecommendationsByExercise[models.Id].IsPyramid, models.IsFlexibility, CurrentLog.Instance.RecommendationsByExercise[models.Id].IsReversePyramid, models.IsMaxChallenge, models.Reps).ToString());
                            }
                            else
                            {
                                LocalDBManager.Instance.SetDBSetting("timer_remaining", App.globalTime.ToString());
                            }
                            Timer.Instance.StartTimer();
                        }
                    }
                    if (item.IsPyramid || item.IsReversePyramid)
                    {
                        var index = 0;
                        foreach (var item1 in item)
                        {
                            if (((WorkoutLogSerieModelRef)item1).IsFinished || item1.IsWarmups)
                                index++;
                            else
                                break;

                        }
                        if (!item.IsReversePyramid)
                            UpdateWeoghtRepsMessageTapped((WorkoutLogSerieModelRef)item[index]);
                    }

                    break;
                }
            }
            catch (Exception ex)
            {

            }
        }
        private void DeleteSetMessageTapped(WorkoutLogSerieModelRef models)
        {
            if (contextMenuStack != null)
                HideContextButton();
            models.IsFinished = false;
            models.IsEditing = true;
            models.IsNext = true;
            //if (Timer.Instance.State != "RUNNING")
            //    Xamarin.Forms.MessagingCenter.Send<SaveSetMessage>(new SaveSetMessage() { model = models, IsFinishExercise = false }, "SaveSetMessage");
            ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
            foreach (var item in exerciseItems)
            {
                if (!item.Contains(models))
                    continue;
                models.IsFinished = false;

                foreach (WorkoutLogSerieModelRef sets in item)
                {
                    sets.IsEditing = false;
                    sets.IsNext = false;
                }
                models.IsEditing = false;
                models.IsNext = true;
                //if (item.Count == 1)
                //    break;
                //if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                //{
                //    var listOfSets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                //    listOfSets.Remove(models);
                //}

                //item.Remove(models);
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                if (Device.RuntimePlatform.Equals(Device.iOS))
                    ExerciseListView.ItemsSource = exerciseItems;
                break;
            }
            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                //Device.BeginInvokeOnMainThread(() => {
                //    //ExerciseListView.BeginRefresh();
                //    //ExerciseListView.EndRefresh();
                //});
            }
            return;

            //TO rmeove complete set
            foreach (var item in exerciseItems)
            {
                if (!item.Contains(models))
                    continue;
                models.IsFinished = false;
                if (item.Count == 1)
                    break;
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                {
                    var listOfSets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                    listOfSets.Remove(models);
                }

                item.Remove(models);
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

            }
        }

        private void DeleteSetPermanentMessageTapped(WorkoutLogSerieModelRef models)
        {
            try
            {
                if (contextMenuStack != null)
                    HideContextButton();

                //TO rmeove complete set
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    if (item.Count == 1)
                        break;
                    var setIndexNo = item.IndexOf(models);
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                    {
                        var listOfSets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                        var removed = listOfSets.Remove(models);
                        if (models.IsNext)
                        {
                            if (!models.IsFinished)
                            {
                                if (listOfSets.Count > setIndexNo)
                                {
                                    listOfSets[setIndexNo].IsNext = true;
                                    listOfSets[setIndexNo].IsActive = true;
                                }
                            }
                        }
                        if (models.IsLastSet)
                            listOfSets.Last().IsLastSet = true;
                    }
                    item.Remove(models);
                    if (models.IsNext)
                    {
                        if (!models.IsFinished)
                        {
                            if (item.Count > setIndexNo)
                            {
                                ((WorkoutLogSerieModelRef)item[setIndexNo]).IsNext = true;
                                ((WorkoutLogSerieModelRef)item[setIndexNo]).IsActive = true;
                            }
                        }
                    }
                    if (models.IsLastSet)
                        ((WorkoutLogSerieModelRef)item.Last()).IsLastSet = true;
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    // for (var i = 0; i < item.Count; i++)
                    //     ((WorkoutLogSerieModelRef)item[i]).SetNo = $"{i + 1}";
                    SetIndex(item.ToList());
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void UpdateSetTitleMessageTapped(WorkoutLogSerieModelRef models)
        {
            foreach (var item in exerciseItems)
            {
                try
                {

                if (!item.Contains(models))
                    continue;
                    bool isRepsChanged = false;
                var index = item.IndexOf(models);
                if (item.Count > (index + 1))
                    ((WorkoutLogSerieModelRef)item[index + 1]).SetTitle = models.RIR == 0 ? "OK, hard—now try..." : "Got it! Now try...";
                    if (item.IsUnilateral && !item.IsFirstSide)
                        continue;
                    //if (models.RIR == 0 && !item.IsBodyweight)
                    //{
                    //    if (!item.IsPyramid)
                    //    { 
                    //        for (int i = index + 1; i < item.Count; i++)
                    //        {
                    //            //var rec = (WorkoutLogSerieModelRef)item[i];
                    //            //decimal weight = RecoComputation.RoundToNearestIncrement(rec.Weight.Kg - (rec.Weight.Kg * (decimal)0.1), item.RecoModel.Increments == null ? (decimal)1.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                    //            //decimal oldWeight = RecoComputation.RoundToNearestIncrement(rec.Weight.Kg, item.RecoModel.Increments == null ? (decimal)1.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                    //            //if (oldWeight == weight && i == index + 1)
                    //            //{
                    //            //    isRepsChanged = true;
                    //            //}
                    //            //if (isRepsChanged)
                    //            //    ((WorkoutLogSerieModelRef)item[i]).Reps -= ((WorkoutLogSerieModelRef)item[i]).Reps > 1 ? 1 : 0;
                    //            //else
                    //            //    ((WorkoutLogSerieModelRef)item[i]).Weight = new MultiUnityWeight(weight, "kg");

                    //            //if (i == index + 1)
                    //            //{

                    //            //        popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", rec.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, rec.EquipmentId == 4);
                    //            //    App.PCWeight = App.PCWeight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                    //            //    popup?.WeightCalculateAgain();
                    //            //}

                               
                    //                var rec = (WorkoutLogSerieModelRef)item[i];

                    //                int reps = (int)Math.Floor(rec.Reps - (rec.Reps * (decimal)0.1));
                    //                if (reps < 1)
                    //                    reps = 1;
                    //                ((WorkoutLogSerieModelRef)item[i]).Reps = reps;
                    //                if (i == index + 1)
                    //                {
                    //                    if (!item.IsBodyweight)
                    //                    {
                    //                        popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", rec.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, rec.EquipmentId == 4);
                    //                        App.PCWeight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                    //                        popup?.WeightCalculateAgain();
                    //                    }
                    //                    else if (rec.Id == 16508)
                    //                    {
                    //                        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Fast", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                    //                    }
                    //                    else if (rec.BodypartId == 12)
                    //                    {
                    //                        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Cooldown", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                    //                    }
                    //                    else if (rec.Id >= 16897 && rec.Id <= 16907 || rec.Id == 14279 || rec.Id >= 21508 && rec.Id <= 21514)//
                    //                    {
                    //                        popup?.SetTimerRepsSets(string.Format("{0} x {1} ", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString(), "Bands").ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, rec.EquipmentId == 4);
                    //                    }
                    //                    else
                    //                    {
                    //                        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Body", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, rec.EquipmentId == 4);
                    //                    }
                    //                }
                                
                    //        }
                    //    }
                    //    else
                    //    {
                    //        for (int j = index + 1; j < item.Count; j++)
                    //        {
                    //            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j];

                    //            if (!updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                    //            {
                    //                var count = item.Where(x => x.IsWarmups == true).Count();
                    //                var rec = updatingItem;

                    //                var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;

                    //                //decimal weight = RecoComputation.RoundToNearestIncrement(rec.Weight.Kg - (rec.Weight.Kg * (decimal)0.1), item.RecoModel.Increments == null ? (decimal)1.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                    //                decimal weight = RecoComputation.RoundToNearestIncrement(isKg ? rec.Weight.Kg - (rec.Weight.Kg * (decimal)0.1) : rec.Weight.Lb - (rec.Weight.Lb * (decimal)0.1), item.RecoModel.Increments == null ? (decimal)1.0 : isKg ? item.RecoModel.Increments.Kg : TruncateDecimal(item.RecoModel.Increments.Lb, 5), isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb);

                    //                var newWeight = new MultiUnityWeight(weight, isKg ? "kg" : "lb");
                    //                var oldWeight = isKg ? updatingItem.Weight.Kg : updatingItem.Weight.Lb;

                    //                if (oldWeight == weight && j == index + 1)
                    //                {
                    //                    isRepsChanged = true;
                    //                }
                    //                if (isRepsChanged)
                    //                    updatingItem.Reps -= updatingItem.Reps > 1 ? 1 : 0;
                    //                else
                    //                    updatingItem.Weight = newWeight;
                    //                if (j == index + 1)
                    //                {
                                        
                    //                    popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", updatingItem.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", updatingItem.IsFirstWorkSet && updatingItem.IsMaxChallenge ? "Max" : updatingItem.Reps.ToString()).ReplaceWithDot(), updatingItem.IsFirstWorkSet && updatingItem.IsMaxChallenge ? true : false, updatingItem.EquipmentId == 4);
                    //                    App.PCWeight = App.PCWeight = Convert.ToDecimal(updatingItem.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                    //                    popup?.WeightCalculateAgain();
                    //                }

                    //            }
                    //        }
                    //    }
                    //}
                    if (models.RIR == 0)
                    {
                        for (int i = index + 1; i < item.Count; i++)
                        {
                            var rec = (WorkoutLogSerieModelRef)item[i];

                            int reps = (int)Math.Floor(rec.Reps - (rec.Reps * (decimal)0.1));
                            if (reps < 1)
                                reps = 1;
                            ((WorkoutLogSerieModelRef)item[i]).Reps = reps;
                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(((WorkoutLogSerieModelRef)item[i]).Id))
                            {
                                var listSeries = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                                if (listSeries.Contains(rec))
                                {
                                    var toUpdateReps = listSeries[listSeries.IndexOf(rec)];
                                    toUpdateReps.Reps = reps;
                                }

                            }
                            if (i == index + 1)
                            {
                                //if (rec.Id == 16508)
                                //{

                                //        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Fast", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                //}
                                //else if (rec.BodypartId == 12)
                                //{
                                //    popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Cooldown", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                //}
                                //else if (rec.Id >= 16897 && rec.Id <= 16907 || rec.Id == 14279 || rec.Id >= 21508 && rec.Id <= 21514)//
                                //{

                                //        popup?.SetTimerRepsSets(string.Format("{0} x {1} ", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString(), "Bands").ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                //}
                                //else
                                //{

                                //        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Body", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                //}
                                if (!item.IsBodyweight)
                                {
                                    popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", rec.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, (rec.EquipmentId == 4 || rec.EquipmentId == 5) && !rec.IsOneHanded);
                                    App.PCWeight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                    UpdateBandsSetTitle(rec.Id, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg", rec);
                                    popup?.WeightCalculateAgain();
                                }
                                else if (rec.Id == 16508)
                                {
                                    popup?.SetTimerRepsSets(string.Format("{1} x {0} ", item.RecoModel.Speed, rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : TimeSpan.FromSeconds(rec.Reps).ToString(@"mm\:ss")).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                }
                                else if (rec.BodypartId == 12)
                                {
                                    popup?.SetTimerRepsSets(string.Format("{1} x {0} ", item.RecoModel.Speed, rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : TimeSpan.FromSeconds(rec.Reps).ToString(@"mm\:ss")).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                }
                                else if (rec.Id >= 16897 && rec.Id <= 16907 || rec.Id == 14279 || rec.Id >= 21508 && rec.Id <= 21514)//
                                {
                                    popup?.SetTimerRepsSets(string.Format("{0} x {1} ", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString(), "Bands").ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, (rec.EquipmentId == 4 || rec.EquipmentId == 5) && !rec.IsOneHanded);
                                }
                                else
                                {
                                    popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Body", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, (rec.EquipmentId == 4 || rec.EquipmentId == 5) && !rec.IsOneHanded);
                                }
                            }
                        }
                    }
                    if (item.IsNormalSets == false && item.IsPyramid == false && (models.RIR == 4 || models.RIR == 3))
                    {
                        for (int i = index + 1; i < item.Count; i++)
                        {
                            var rec = (WorkoutLogSerieModelRef)item[i];
                            if (rec.IsFinished)
                                continue;
                            ((WorkoutLogSerieModelRef)item[i]).Reps += 1;
                            if (i == index + 1)
                            {
                                if (item.IsBodyweight)
                                {
                                    if (rec.Id == 16508)
                                    {
                                        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", item.RecoModel.Speed, rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : TimeSpan.FromSeconds(rec.Reps).ToString(@"mm\:ss")).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                    }
                                    else if (rec.BodypartId == 12)
                                    {
                                        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", item.RecoModel.Speed, rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : TimeSpan.FromSeconds(rec.Reps).ToString(@"mm\:ss")).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                    }
                                    if (rec.Id >= 16897 && rec.Id <= 16907 || rec.Id == 14279 || rec.Id >= 21508 && rec.Id <= 21514)//
                                    {
                                        popup?.SetTimerRepsSets(string.Format("{0} x {1} ", rec.Reps.ToString(), "Bands").ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                    }
                                    else
                                    {
                                        popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Body", rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false);
                                    }
                                }
                                else
                                    popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", rec.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, (rec.EquipmentId == 4 || rec.EquipmentId == 5) && !rec.IsOneHanded);
                                App.PCWeight = App.PCWeight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {

                }
            }
        }

        private void UpdateBandsSetTitle(long id, bool isKg, WorkoutLogSerieModelRef rec)
        {
            if (id >= 16897 && id <= 16907 || id == 14279 || id >= 21508 && id <= 21514)
            {
                var matchBands = HelperClass.GetBandsColor((double)Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture), isKg);
                if (!string.IsNullOrEmpty(matchBands))
                {
                    popup?.SetTimerRepsSets(string.Format("{1} x {0}", matchBands, rec.IsFirstWorkSet && rec.IsMaxChallenge ? "Max" : rec.Reps.ToString()).ReplaceWithDot(), rec.IsFirstWorkSet && rec.IsMaxChallenge ? true : false, (rec.EquipmentId == 4 || rec.EquipmentId == 5) && !rec.IsOneHanded);
                }

            }
        }
        public decimal ComputeOneRM(decimal weight, int reps)
        {
            return (decimal)(AppThemeConstants.Coeficent * reps) * weight + weight;
        }

        private async void ScrollToActiveSet(WorkoutLogSerieModelRef set, ExerciseWorkSetsModel m)
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                if (set != null)
                {
                    //if (Device.RuntimePlatform.Equals(Device.iOS))
                    Device.BeginInvokeOnMainThread(async () =>
                    {
                        if (Device.RuntimePlatform.Equals(Device.iOS))
                            ExerciseListView.SelectedItem = set;
                        ExerciseListView.ScrollTo(set, ScrollToPosition.End, true);
                        await Task.Delay(100);
                        ExerciseListView.ScrollTo(set, ScrollToPosition.MakeVisible, true);

                    });
                }
            });
        }

        private async void SaveSetFromWatchMessageTapped(PhoneToWatchModel models)
        {
            try
            {

            if (models.WatchMessageType == WatchMessageType.EndTimer)
            {
                if (PopupNavigation.PopupStack.Count > 0)
                    PopupNavigation.Instance.PopAllAsync();
                return;
            }
            if (models.WatchMessageType == WatchMessageType.FinishSaveWorkout)
            {
                SavingExcercise();
                return;
            }
            var model = exerciseItems.FirstOrDefault(m => m.Id == models.Id);
            if (models.WatchMessageType == WatchMessageType.FinishExercise)
            {
                PushToDataServer(model);
                return;
            }
            if (models.WatchMessageType == WatchMessageType.FinishSide1)
            {
                Finished_Clicked(model);
                return;
            }
            if (models.WatchMessageType == WatchMessageType.RIR)
            {
                foreach (var set in model.Where(x => x.IsWarmups == false))
                {
                    set.RIR = models.RIR;
                }
                return;
            }
            foreach (WorkoutLogSerieModelRef item in model)
            {
                if (item.IsFinished)
                    continue;

                MessagingCenter.Send<FinishSetReceivedFromWatchOS>(new FinishSetReceivedFromWatchOS() { model = item, WatchMessageType = models.WatchMessageType }, "FinishSetReceivedFromWatchOS");
                break;
            }

            }
            catch (Exception ex)
            {

            }
        }

        private async void SaveSetMessageTapped(WorkoutLogSerieModelRef models, bool IsFinished)
        {
            if (contextMenuStack != null)
                HideContextButton();
            if (Device.RuntimePlatform.Equals(Device.Android))
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

            foreach (var item in exerciseItems)
            {
                if (!item.Contains(models))
                {
                    foreach (WorkoutLogSerieModelRef subItem in item)
                    {
                        subItem.IsActive = false;
                    }
                    continue;
                }

                
                if (IsFinished)
                {
                    //Called SaveSet
                    if (!((WorkoutLogSerieModelRef)item.LastOrDefault()).IsFirstSetFinished)
                        PushToDataServer(item);
                    else
                        Finished_Clicked(item);
                    return;
                }
                if (item.FirstOrDefault() != null)
                {
                    WorkoutLogSerieModelRef header = (WorkoutLogSerieModelRef)item.FirstOrDefault();
                    WorkoutLogSerieModelRef last = (WorkoutLogSerieModelRef)item.LastOrDefault();
                    last.IsFirstSetFinished = header.IsFinished;
                }
                if (models.IsWarmups)
                {
                    // foreach (WorkoutLogSerieModelRef set in item)
                    // {
                    //     var warmUponeRM = ComputeOneRM(set.Weight.Kg, set.Reps);
                    //     var firstSet = (WorkoutLogSerieModelRef)item.ElementAt(item.Count(x=>x.IsWarmups) );
                    //     var computeRM = ComputeOneRM(firstSet.Weight.Kg, firstSet.Reps);
                    //
                    //     if (!firstSet.IsFinished && warmUponeRM > computeRM)
                    //     {
                    //         foreach (WorkoutLogSerieModelRef it in item.Where(x => x.IsWarmups))
                    //         {
                    //             if (!it.IsFinished)
                    //             {
                    //                 it.Reps = set.Reps;
                    //                 it.Weight = set.Weight;
                    //             }
                    //         }
                    //         firstSet.Weight = set.Weight;
                    //         firstSet.Reps = set.Reps;
                    //         UpdateWeoghtRepsMessageTapped(firstSet);
                    //     }
                    //     if (set.IsLastWarmupSet)
                    //         break;
                    // }

                }
                if (models.IsLastSet && models.IsUnilateral && models.IsFirstSide)
                {
                    WorkoutLogSerieModelRef first = (DrMuscle.Layout.WorkoutLogSerieModelRef)item.First();
                    App.PCWeight = Convert.ToDecimal(first.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                    try
                    {
                        if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                            LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                        //if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true")
                        //{

                        //    popup = new TimerPopup();
                        //    popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                        //    popup.popupTitle = "";
                        //    if (models.IsBodyweight)
                        //        popup?.SetTimerRepsSets(string.Format("Body x {0} ", first.Reps).ReplaceWithDot());
                        //    else
                        //        popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2} ", first.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", first.Reps).ReplaceWithDot());
                        //    popup.SetTimerText();
                        //    //if (item.IsTimeBased)
                        //    //{
                        //    //    timeRemain = Convert.ToString(first.Reps);
                        //    //    TimerBased = true;
                        //    //}
                        //    //else
                        //        TimerBased = false;
                        //    PopupNavigation.Instance.PushAsync(popup);

                        //}
                        first.IsActive = true;
                        ExerciseListView.ScrollTo(first, ScrollToPosition.MakeVisible, true);
                    }
                    catch (Exception ex)
                    {

                    }
                    if (item.Count > 1)
                    {
                        if (Device.RuntimePlatform.Equals(Device.Android))
                        {
                            var index = item.IndexOf(models);
                            WorkoutLogSerieModelRef before = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[index > 1 ? index - 1 : index];
                            ScrollToActiveSet(before, item);
                        }
                    }


                    ScrollToActiveSet(models, item);

                }
                bool isAllSetfinished = true;
                foreach (WorkoutLogSerieModelRef logSerieModel in item)
                {
                    if (logSerieModel.IsFinished)
                        continue;
                    isAllSetfinished = false;
                    if (!logSerieModel.IsNext)
                    {
                        logSerieModel.IsNext = true;
                        MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NewSetBehind, SetModel = logSerieModel, Label = item.Label }, "SendWatchMessage");
                        App.PCWeight = Convert.ToDecimal(logSerieModel.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                        try
                        {
                            if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                                LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                            if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" || logSerieModel.BodypartId == 12 || logSerieModel.IsTimeBased == true)
                            {

                                popup = new TimerPopup(item.IsPlate);
                                popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                                popup.popupTitle = "";
                                _backOffSet = logSerieModel;
                                if (logSerieModel.IsBodyweight)
                                {
                                    if (logSerieModel.Id == 16508)
                                    {   
                                            popup?.SetTimerRepsSets(string.Format("{0} x {1} ", logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? "Max" : TimeSpan.FromSeconds(logSerieModel.Reps).ToString(@"mm\:ss"), string.Format("{0:0.##}", item.RecoModel.Speed)).ReplaceWithDot(), logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? true : false);
                                    }
                                    else if (item.BodyPartId == 12)
                                    {
                                        popup?.SetTimerRepsSets(string.Format("{0} x {1} ", logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? "Max" : TimeSpan.FromSeconds(logSerieModel.Reps).ToString(@"mm\:ss"), string.Format("{0:0.##}", item.RecoModel.Speed)).ReplaceWithDot(), logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? true : false);
                                        if (models.IsWarmups == false)
                                            popup.popupTitle = "Work";
                                    }
                                    else if (logSerieModel.Id >= 16897 && logSerieModel.Id <= 16907 || logSerieModel.Id == 14279 || logSerieModel.Id >= 21508 && logSerieModel.Id <= 21514)
                                    {  
                                            popup?.SetTimerRepsSets(string.Format("{0} x {1} ", logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? "Max" : logSerieModel.Reps.ToString(), "Bands").ReplaceWithDot(), logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? true : false);
                                    }
                                    else
                                    {      
                                            popup?.SetTimerRepsSets(string.Format("{0} x Body ", logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? "Max" : logSerieModel.Reps.ToString()).ReplaceWithDot(), logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? true : false);
                                    }
                                }
                                else
                                {                                    
                                        popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1}", logSerieModel.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? "Max" : logSerieModel.Reps.ToString()).ReplaceWithDot(), logSerieModel.IsFirstWorkSet && logSerieModel.IsMaxChallenge ? true : false, (logSerieModel.EquipmentId == 4 || logSerieModel.EquipmentId == 5) && !logSerieModel.IsOneHanded);
                                    UpdateBandsSetTitle(logSerieModel.Id, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg", logSerieModel);
                                }
                                popup.SetTimerText();
                                if (logSerieModel.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null)
                                {
                                    //  var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                                    //     var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg, item.RecoModel.FirstWorkSetReps);
                                    //     var currentRM = ComputeOneRM(Math.Round(new MultiUnityWeight(isKg ? logSerieModel.Weight.Kg : logSerieModel.Weight.Lb, isKg ? "kg" : "lb").Kg, 2), logSerieModel.Reps);
                                    //     var worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                                    //     if (item.IsWeighted)
                                    //     {
                                    //         var newWeight = Math.Round(new MultiUnityWeight(isKg ? logSerieModel.Weight.Kg : logSerieModel.Weight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), 2);
                                    //         
                                    //          currentRM = TruncateDecimal(ComputeOneRM(newWeight, logSerieModel.Reps), 2);
                                    //
                                    //         lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (item.IsWeighted ? _userBodyWeight : 0), item.RecoModel.FirstWorkSetReps);
                                    //         
                                    //     }
                                    //
                                    //    
                                    //
                                    //     if (models.IsBodyweight)
                                    //         currentRM = TruncateDecimal(ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(item.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg, logSerieModel.Reps),2);
                                    //     else
                                    //          currentRM = TruncateDecimal(currentRM, 2);
                                    //     lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                                    //         currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                                    //     
                                    //
                                    // if (logSerieModel.IsBodyweight)
                                    // {
                                    //     worksets = "body";
                                    //     if (logSerieModel.Id == 16508)
                                    //     {
                                    //         worksets = "fast";
                                    //     }
                                    //     else if (logSerieModel.BodypartId == 12)
                                    //     {
                                    //         worksets = "fast";
                                    //         popup.popupTitle = "Work";
                                    //     }
                                    //     if (logSerieModel.Id >= 16897 && logSerieModel.Id <= 16907 || logSerieModel.Id == 14279 || logSerieModel.Id >= 21508 && logSerieModel.Id <= 21514)
                                    //     {
                                    //         worksets = "bands";
                                    //     }
                                    // }
                                    //
                                    // if (currentRM != 0)
                                    // {
                                    //     var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                                    //     popup.SetLastTimeText(string.Format("{0}{1:0.##}%:", percentage >= 0 ? "+" : "", percentage), string.Format("Last time: {0} x {1}", item.RecoModel.FirstWorkSetReps, worksets));
                                    // }
                                    SetCoachTipTitle(logSerieModel, item, true);
                                }
                                if (item.IsTimeBased)
                                {
                                    timeRemain = Convert.ToString( logSerieModel.Reps);
                                    TimerBased = true;
                                    if (logSerieModel.BodypartId == 12 && logSerieModel.Id != 16508 && logSerieModel.IsWarmups == false)
                                        TimerBased = false;
                                }
                                else
                                    TimerBased = false;
                                PopupNavigation.Instance.PushAsync(popup);

                            }
                            logSerieModel.IsActive = true;
                            ExerciseListView.ScrollTo(logSerieModel, ScrollToPosition.MakeVisible, true);

                        }
                        catch (Exception ex)
                        {

                        }
                        if (Device.RuntimePlatform.Equals(Device.iOS))
                            ExerciseListView.ScrollTo(logSerieModel, ScrollToPosition.MakeVisible, true);
                        //else
                        //    ExerciseListView.ScrollTo(logSerieModel, ScrollToPosition.MakeVisible, true);
                        break;
                    }


                }
                if (isAllSetfinished && item.IsFirstSide == false && !item.IsReversePyramid)
                {
                    if (!App.IsConnectedToWatch)
                    {
                        ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                        {
                            Title = $"All sets done—finish exercise?",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Finish exercise",
                            CancelText = AppResources.Cancel,

                        };
                        var x = await UserDialogs.Instance.ConfirmAsync(ShowRIRPopUp);
                        if (x)
                        {
                            PushToDataServer(item);
                        }
                    }
                    else
                        MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.FinishExercise, SetModel = models }, "SendWatchMessage");
                }
                if (isAllSetfinished && item.IsFirstSide == true)
                {
                    if (!App.IsConnectedToWatch)
                    {
                        ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                        {
                            Title = $"All sets done for side 1—start side 2?",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Side 2",
                            CancelText = AppResources.Cancel,

                        };
                        var x = await UserDialogs.Instance.ConfirmAsync(ShowRIRPopUp);
                        if (x)
                        {
                            FinishedSide1_Clicked(item);
                        }
                    }
                    else
                        MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.FinishExercise, SetModel = models }, "SendWatchMessage");
                }
                if (isAllSetfinished && item.IsFirstSide == true && App.IsConnectedToWatch)
                    MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.FinishSide1, SetModel = models }, "SendWatchMessage");
            }
            ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

            try
            {
                ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                ((App)Application.Current).WorkoutLogContext.SaveContexts();
            }
            catch (Exception ex)
            {

            }
            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                //Device.BeginInvokeOnMainThread(() => {
                //    //ExerciseListView.BeginRefresh();
                //    //ExerciseListView.EndRefresh();
                //});
            }
        }

        private async void AskForFinishPyramidExercise(WorkoutLogSerieModelRef models)
        {
            try
            {
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                    {
                        foreach (WorkoutLogSerieModelRef subItem in item)
                        {
                            subItem.IsActive = false;
                        }
                        continue;
                    }
                    if (!item.IsReversePyramid)
                        break;
                    if (!App.IsConnectedToWatch)
                    {
                        ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                        {
                            Title = $"All sets done—finish exercise?",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Finish exercise",
                            CancelText = AppResources.Cancel,

                        };
                        var x = await UserDialogs.Instance.ConfirmAsync(ShowRIRPopUp);
                        if (x)
                        {
                            PushToDataServer(item);
                        }
                    }
                    else
                        MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.FinishExercise, SetModel = models }, "SendWatchMessage");
                }
            }
            catch (Exception)
            {

            }
        }


        public override async void OnBeforeShow()
        {
            base.OnBeforeShow();
            IsGlobalSettingsChanged = false;
            App.IsNUX = false;
            CurrentLog.Instance.IsFromExercise = true;
            exerciseItems.Clear();
            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
            await UpdateExerciseList();
            if (LocalDBManager.Instance.GetDBSetting($"Time{DateTime.Now.Year}") == null || LocalDBManager.Instance.GetDBSetting($"Time{DateTime.Now.Year}").Value == null)
                LocalDBManager.Instance.SetDBSetting($"Time{DateTime.Now.Year}", $"{DateTime.Now.Ticks}");
            
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            isAppear = false;
            try
            {
                DependencyService.Get<IKeyboardHelper>().HideKeyboard();
            }
            catch (Exception ex)
            {

            }
            MessagingCenter.Unsubscribe<Message.SaveSetMessage>(this, "SaveSetMessage");
            MessagingCenter.Unsubscribe<Message.UpdateSetTitleMessage>(this, "UpdateSetTitleMessage");
            MessagingCenter.Unsubscribe<Message.DeleteSetMessage>(this, "DeleteSetMessage");
            MessagingCenter.Unsubscribe<Message.CellUpdateMessage>(this, "CellUpdateMessage");
            MessagingCenter.Unsubscribe<Message.WeightRepsUpdatedMessage>(this, "WeightRepsUpdatedMessage");
            MessagingCenter.Unsubscribe<Message.AddSetMessage>(this, "AddSetMessage");
            MessagingCenter.Unsubscribe<Message.ReceivedWatchMessage>(this, "ReceivedWatchMessage");
            MessagingCenter.Unsubscribe<Message.FinishExerciseMessage>(this, "FinishExerciseMessage");
            //

        }
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            _firebase.SetScreenName("kenko_workout_single_exercise");
            MessagingCenter.Subscribe<Message.SaveSetMessage>(this, "SaveSetMessage", (obj) =>
            {
                SaveSetMessageTapped(obj.model, obj.IsFinishExercise);
            });

            MessagingCenter.Subscribe<Message.UpdateSetTitleMessage>(this, "UpdateSetTitleMessage", (obj) =>
            {
                UpdateSetTitleMessageTapped(obj.model);
            });

            MessagingCenter.Subscribe<Message.DeleteSetMessage>(this, "DeleteSetMessage", (obj) =>
            {
                if (!obj.isPermenantDelete)
                    DeleteSetMessageTapped(obj.model);
                else
                    DeleteSetPermanentMessageTapped(obj.model);
            });

            MessagingCenter.Subscribe<Message.FinishExerciseMessage>(this, "FinishExerciseMessage", (obj) =>
            {
                AskForFinishPyramidExercise(obj.model);

            });
            MessagingCenter.Subscribe<Message.OneRMChangedMessage>(this, "OneRMChangedMessage", (obj) =>
            {
                UpdateOneRM(obj.model, obj.Weight, obj.Reps);
            });

            
            MessagingCenter.Subscribe<Message.OneRMChangedMessage>(this, "SpeedChangedMessage", (obj) =>
            {
                UpdateSpeed(obj.model);
            });
            MessagingCenter.Subscribe<Message.CellUpdateMessage>(this, "CellUpdateMessage", (obj) =>
            {
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
            });

            //
            MessagingCenter.Subscribe<Message.WeightRepsUpdatedMessage>(this, "WeightRepsUpdatedMessage", (obj) =>
            {
                UpdateWeoghtRepsMessageTapped(obj.model);
            });

            MessagingCenter.Subscribe<Message.AddSetMessage>(this, "AddSetMessage", (obj) =>
            {
                AddSetMessageTapped(obj.model, obj.hasFinished);
            });

            MessagingCenter.Subscribe<ReceivedWatchMessage>(this, "ReceivedWatchMessage", (obj) => {
                Device.BeginInvokeOnMainThread(() => {
                    SaveSetFromWatchMessageTapped(obj.PhoneToWatchModel);
                });
            });
            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
            try
            {
                isAppear = true;
                    Task.Factory.StartNew(async () =>
                    {
                        if (Device.RuntimePlatform.Equals(Device.iOS))
                        ((RightSideMasterPage)SlideMenu).ResignedField();
                    });
                
                DependencyService.Get<IKeyboardHelper>().HideKeyboard();
            }
            catch (Exception ex)
            {

            }
            if (Navigation.NavigationStack.First() is MePage)
            {
                ChangeWorkout();
                return;
            }

            if (!_isFreePlanPopup && App.IsFreePlan)
            {
                _isFreePlanPopup = true;
                ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                {
                    Message = "You're on the free plan. You get one exercise recommendation a day. Upgrade for unlimited recommendations.",
                    Title = "Unlimited trial expired",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Upgrade",
                    CancelText = "Maybe later",
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            //Reload Settings
                        }
                        else
                        {

                        }
                    }
                };
                await Task.Delay(100);
                UserDialogs.Instance.Confirm(ShowWelcomePopUp2);

            }

            if (IsSettingsChanged || IsGlobalSettingsChanged)
            {
                try
                {

                    if (IsSettingsChanged && CurrentLog.Instance.WorkoutTemplateCurrentExercise != null)
                    {
                        if (CurrentLog.Instance.CurrentExercise != null)
                            CurrentLog.Instance.CurrentExercise.IsUnilateral = CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsUnilateral;

                    }
                    IsGlobalSettingsChanged = false;
                    IsSettingsChanged = false;
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                        var item = exerciseItems.FirstOrDefault();
                    var msg = $"Reload {CurrentLog.Instance.WorkoutTemplateCurrentExercise.Label}?";
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                    {
                        if(CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id].Count(x => x.IsFinished)>0)
                        {
                            msg = $"Reload {CurrentLog.Instance.WorkoutTemplateCurrentExercise.Label}? Your {CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id].Count(x => x.IsFinished)} saved sets will be reset.";
                        }
                    }
                    ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                {
                    Message = msg,
                    Title = "Settings changed",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Reload",
                    CancelText = AppResources.Cancel,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            //Reload Settings
                            
                            if (item!= null)
                            {
                                item.RecoModel = null;
                                Timer.Instance.StopTimer();
                                var weights = LocalDBManager.Instance.GetDBSetting($"SetupWeight{item.Id}")?.Value;
                                if (!string.IsNullOrEmpty(weights))
                                    LocalDBManager.Instance.SetDBReco("RReps" + item.Id + LocalDBManager.Instance.GetDBSetting("SetStyle").Value + "challenge", $"max");
                                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                                {
                                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(item.Id);
                                    item.Clear();
                                    try
                                    {
                                        LocalDBManager.Instance.SetDBReco("RReps" + item.Id + "Normal" + "Deload", $"");
                                        LocalDBManager.Instance.SetDBReco("RReps" + item.Id + "RestPause" + "Deload", $"");
                                        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + item.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                                        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + item.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                                    }
                                    catch (Exception ex)
                                    {

                                    }
                                    item.RecoModel = null;
                                  //  item.IsPyramid = false;
                                }
                                //exerciseItems.Clear();
                                UpdateExerciseList();
                                //CellHeaderTapped(new Button() { BindingContext = item }, null) ;
                            }
                        }
                        else
                        {
                            
                        }
                    }
                };
                await Task.Delay(100);
                UserDialogs.Instance.Confirm(ShowWelcomePopUp2);

                }
                catch (Exception ex)
                {

                }
            }
            if (CurrentLog.Instance.IsAddingExerciseLocally)
            {
                await UpdateExerciseList();
                CurrentLog.Instance.IsAddingExerciseLocally = false;
            }
            if (Config.ShowWelcomePopUp2 == false)
            {
                if (App.IsWelcomePopup2)
                    return;
                App.IsWelcomePopup2 = true;
                ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                {
                    Message = AppResources.ShowWelcomePopUp2Messagge,
                    Title = AppResources.ShowWelcomePopUp2Title,
                  AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = AppResources.GotIt,
                    CancelText = AppResources.RemindMe,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            Config.ShowWelcomePopUp2 = true;
                        }
                        else
                        {
                            Config.ShowWelcomePopUp2 = false;
                        }
                    }
                };
                await Task.Delay(100);
                UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
            try
            {
                if (CurrentLog.Instance.SwapContext != null)
                    CurrentLog.Instance.SwapContext = null;

            }
            catch (Exception ex)
            {

            }
            ExerciseListView.ItemsSource = exerciseItems;
        }

        private async void ChangeWorkout()
        {
            ConfirmConfig ShowConfirmPopUp = new ConfirmConfig()
            {
                Message = $"Change program and do {CurrentLog.Instance.CurrentWorkoutTemplate.Label} next?",
                Title = "",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Select workout",
                CancelText = AppResources.Cancel,
                OnAction = async (bool ok) =>
                {
                    if (ok)
                    {
                        ChangingWorkout();
                    }
                    else
                    {
                        await PagesFactory.PopAsync();
                    }
                }
            };
            UserDialogs.Instance.Confirm(ShowConfirmPopUp);
        }

        private async void ChangingWorkout()
        {
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                {
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                        LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                    }
                }


            }
            catch (Exception ex)
            {

            }
            try
            {
                if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp == 999)
                {
                    foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                    {
                        LocalDBManager.Instance.SetDBSetting($"workout{exerciceModel.Id}", "false");
                    }
                    await PagesFactory.PopToRootAsync();
                    return;
                }
            }
            catch (Exception ex)
            {

            }
            bool isSystem = false;
            BooleanModel successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkoutV2(new SaveWorkoutModel() { WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id });
            try
            {
                if (successWorkoutLog.Result)
                {
                    Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage(), "UpdatedWorkoutMessage");
                }
                LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
                LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());
                isSystem = CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise;
            }
            catch (Exception ex)
            {

            }

            var nextworkoutName = CurrentLog.Instance.CurrentWorkoutTemplate.Label;
            CurrentLog.Instance.CurrentWorkoutTemplate = null;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
            CurrentLog.Instance.WorkoutStarted = false;
            string fname = LocalDBManager.Instance.GetDBSetting("firstname").Value;

            try
            {
                AlertConfig p = new AlertConfig()
                {
                    Title = $"{AppResources.GotIt} {fname}!",
                    Message = $"Your next workout will be {nextworkoutName}.",
                    OkText = AppResources.Ok,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = async () =>
                {
                    await PagesFactory.PopToRootAsync();
                };
                UserDialogs.Instance.Alert(p);

            }

            catch (Exception ex)
            {
                await PagesFactory.PopToRootAsync();
            }

        }
        private async void FinishedSide1_Clicked(ExerciseWorkSetsModel model)
        {

            try
            {

                if (model.IsFirstSide && model.IsUnilateral)
                {

                    model.IsFirstSide = false;
                    List<WorkoutLogSerieModelRef> setList = new List<WorkoutLogSerieModelRef>();
                    foreach (WorkoutLogSerieModelRef item in model)
                    {
                        item.IsFirstSide = false;
                        item.IsFinished = false;
                        item.IsNext = false;
                        item.IsActive = false;
                        item.IsEditing = false;
                        item.IsFirstSetFinished = false;
                        setList.Add(item);
                    }
                    WorkoutLogSerieModelRef workout = ((WorkoutLogSerieModelRef)model.First());
                    workout.IsNext = true;
                    workout.IsActive = true;
                    App.PCWeight = Convert.ToDecimal(workout.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NewSet, SetModel = workout, Label = model.Label }, "SendWatchMessage");
                    if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                    {

                        if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" && Timer.Instance.State == "RUNNING")
                        {

                            popup = new TimerPopup(model.IsPlate);
                            popup.RemainingSeconds = Convert.ToString(Timer.Instance.Remaining);
                            popup.popupTitle = "";
                            if (workout.IsBodyweight)
                            {
                                if (workout.BodypartId == 12)
                                    popup?.SetTimerRepsSets(string.Format("{0} x {1}", workout.IsFirstWorkSet && workout.IsMaxChallenge ? "Max" : workout.Reps.ToString(), model.RecoModel.Speed ).ReplaceWithDot(), workout.IsFirstWorkSet && workout.IsMaxChallenge ? true : false);
                                else
                                    popup?.SetTimerRepsSets(string.Format("{0} x Body", workout.IsFirstWorkSet && workout.IsMaxChallenge ? "Max" : workout.Reps.ToString()).ReplaceWithDot(), workout.IsFirstWorkSet && workout.IsMaxChallenge ? true : false);
                                popup?.SetReadyForTitle();
                            }
                            else
                            {

                                popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", workout.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", workout.IsFirstWorkSet && workout.IsMaxChallenge ? "Max" : workout.Reps.ToString()).ReplaceWithDot(), workout.IsFirstWorkSet && workout.IsMaxChallenge ? true : false, (workout.EquipmentId == 4 || workout.EquipmentId == 5) && !workout.IsOneHanded);
                                popup?.SetReadyForTitle();
                            }
                            popup.SetTimerText();

                            TimerBased = false;
                            PopupNavigation.Instance.PushAsync(popup);

                        }
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", App.globalTime.ToString());
                        //timeRemain = TimerEntry;
                    }



                    ScrollToSnap(setList, model);
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                    {

                    }
                    else
                    {
                        Device.BeginInvokeOnMainThread(() =>
                        {
                            ExerciseListView.ScrollTo(setList.First(), ScrollToPosition.Start, false);
                        });
                    }
                    if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value != "true" && !App.IsConnectedToWatch)
                    {

                    }
                    try
                    {
                        ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                        ((App)Application.Current).WorkoutLogContext.SaveContexts();
                    }
                    catch (Exception ex)
                    {

                    }

                    return;
                }

            }
            catch (Exception ex)
            {

            }


        }
        private async void Finished_Clicked(ExerciseWorkSetsModel model)
        {

            if (model.IsFirstSide && model.IsUnilateral)
            {

                model.IsFirstSide = false;
                List<WorkoutLogSerieModelRef> setList = new List<WorkoutLogSerieModelRef>();

                foreach (WorkoutLogSerieModelRef item in model)
                {
                    item.IsFirstSide = false;
                    item.IsFinished = false;
                    item.IsNext = false;
                    item.IsActive = false;
                    item.IsEditing = false;
                    item.IsFirstSetFinished = false;
                    setList.Add(item);
                }
                WorkoutLogSerieModelRef workout = ((WorkoutLogSerieModelRef)model.First());
                workout.IsNext = true;
                workout.IsActive = true;
                App.PCWeight = Convert.ToDecimal(workout.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                //if (workout.IsWarmups)
                //{
                //    var time = workout.IsLastWarmupSet ? "55" : CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight ? "30" : "40";
                //    LocalDBManager.Instance.SetDBSetting("timer_remaining", time);

                //}
                //else
                //{
                MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NewSet, SetModel = workout, Label = model.Label }, "SendWatchMessage");
                if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                    {
                    //LocalDBManager.Instance.SetDBSetting("timer_remaining", CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, false, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets).ToString());
                    //LocalDBManager.Instance.SetDBSetting("timer_remaining", "25");
                    //timeRemain = CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, false, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets).ToString();
                    if ((LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" ||  workout.BodypartId == 12 || workout.IsTimeBased == true) && Timer.Instance.State == "RUNNING")
                    {

                        popup = new TimerPopup(model.IsPlate);
                        popup.RemainingSeconds = Convert.ToString(Timer.Instance.Remaining);
                        popup.popupTitle = "";
                        if (workout.IsBodyweight)
                        { 
                            
                                popup?.SetTimerRepsSets(string.Format("{0} x Body ", workout.IsFirstWorkSet && workout.IsMaxChallenge ? "Max" : workout.Reps.ToString()).ReplaceWithDot(), workout.IsFirstWorkSet && workout.IsMaxChallenge ? true : false);
                            popup?.SetReadyForTitle();
                        }
                        else
                        {
                        
                            popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1}", workout.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", workout.IsFirstWorkSet && workout.IsMaxChallenge ? "Max" : workout.Reps.ToString()).ReplaceWithDot(), workout.IsFirstWorkSet && workout.IsMaxChallenge ? true : false, (workout.EquipmentId == 4 || workout.EquipmentId == 5) && !workout.IsOneHanded);
                            popup?.SetReadyForTitle();
                        }
                        popup.SetTimerText();
                        //if (item.IsTimeBased)
                        //{
                        //    timeRemain = Convert.ToString(first.Reps);
                        //    TimerBased = true;
                        //}
                        //else
                        TimerBased = false;
                        PopupNavigation.Instance.PushAsync(popup);

                    }
                }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", App.globalTime.ToString());
                        //timeRemain = TimerEntry;
                    }
                //}
                //Timer.Instance.StopTimer();
                //Timer.Instance.stopRequest = false;
                //Timer.Instance.StartTimer();
                //Open fullscreen timer
                //if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true")
                //{

                //    popup = new TimerPopup();
                //    popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                //    popup.popupTitle = "";
                //    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2} ", workout.WeightDouble.ReplaceWithDot(), LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", workout.Reps).ReplaceWithDot());
                //    popup.SetTimerText();
                //    if (model.IsTimeBased)
                //    {
                //        timeRemain = Convert.ToString(workout.Reps);
                //        TimerBased = true;
                //    }
                //    else
                //        TimerBased = false;
                //    PopupNavigation.Instance.PushAsync(popup);

                //}
                
                AlertConfig ShowExplainRIRPopUp = new AlertConfig()
                {
                    Title = "Well done! Now do all sets for side 2",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = AppResources.Ok,

                };

                ScrollToSnap(setList, model);
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                    setList.First().IsSizeChanged = !setList.First().IsSizeChanged;
                }
                else
                {
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        ExerciseListView.ScrollTo(setList.First(), ScrollToPosition.Start, false);
                    });
                }
                if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value != "true")
                    UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
                //if (Config.ShowAllSetPopup == false)
                //{
                //    if (App.ShowAllSetPopup)
                //        return;
                //    App.ShowAllSetPopup = true;
                //    ConfirmConfig ShowWelcomePopUp4 = new ConfirmConfig()
                //    {

                //        Title = "Well done! Now do all sets for side 2",
                //        //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //        OkText = AppResources.GotIt,
                //        CancelText = AppResources.RemindMe,
                //        OnAction = async (bool ok) =>
                //        {
                //            if (ok)
                //            {
                //                Config.ShowAllSetPopup = true;
                //            }
                //            else
                //            {
                //                Config.ShowAllSetPopup = false;
                //            }
                //        }
                //    };
                //    await Task.Delay(100);
                //    UserDialogs.Instance.Confirm(ShowWelcomePopUp4);
                //}
                return;
            }

            if (model.IsFinished)
            {
                foreach (WorkoutLogSerieModelRef item in model)
                {

                var newWorkOutLog = new WorkoutLogSerieModel()
                {
                    Id = item.Id,
                    Reps = item.Reps,
                    Weight = item.Weight,
                    Exercice = GetExerciseModel(model)
                };
                BooleanModel result = await DrMuscleRestClient.Instance.EditWorkoutLogSeries(newWorkOutLog);
                    
                }
                model.IsNextExercise = false;
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                return;
            }
            ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
            {
                Title = $"Save {model.Label}?",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Save",
                CancelText = AppResources.Cancel,
                OnAction = async (bool ok) =>
                {
                    if (ok)
                    {
                        PushToDataServer(model);
                    }
                    else
                    {
                    }
                }
            };
            UserDialogs.Instance.Confirm(ShowRIRPopUp);
        }

        private void ScrollToSnap(List<WorkoutLogSerieModelRef> setList, ExerciseWorkSetsModel m)
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                if (setList.Count > 0)
                {
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                        ExerciseListView.ScrollTo(setList.First(), ScrollToPosition.Start, true);
                    int position = 0;
                    foreach (var itemGood in exerciseItems)
                    {
                        if (itemGood == m)
                            break;
                        position += 1;
                        position += itemGood.Count;
                    }
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                        ExerciseListView.ItemPosition = exerciseItems.IndexOf(m);
                    else
                        ExerciseListView.ItemPosition = position;
                    ExerciseListView.IsScrolled = !ExerciseListView.IsScrolled;
                }
            });
        }

        private async void PushToDataServer(ExerciseWorkSetsModel model)
        {

            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                {
                    ConnectionErrorPopup();

                    return;
                }
                if (LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Date}") == null)
                    LocalDBManager.Instance.SetDBSetting($"Exercises{DateTime.Now.Date}", "1");
                else
                {
                    var exeCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Date}").Value);
                    exeCount += 1;
                    LocalDBManager.Instance.SetDBSetting($"Exercises{DateTime.Now.Date}", $"{exeCount}");
                }

            }
            catch (Exception ex)
            {

            }
            List<WorkoutLogSerieModel> serieModelList = new List<WorkoutLogSerieModel>();

            foreach (WorkoutLogSerieModelRef l in model)
            {
                if (l.IsFinished)
                {
                    serieModelList.Add(l);
                }
            }
                    if (serieModelList.Count == 0)
            {
                //UserDialogs.Instance.AlertAsync("", AppResources.Error);
                var r = await UserDialogs.Instance.ConfirmAsync(new ConfirmConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Title = $"Skip exercise—are you sure?",
                    CancelText = AppResources.Cancel,
                    OkText = AppResources.Skip
                });
                if (r)
                {
                    //LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{model.Id}", "true");
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(model.Id))
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(model.Id);

                    ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                    ((App)Application.Current).WorkoutLogContext.SaveContexts();
                    PagesFactory.PopAsync();
                }
                return;
            }
            bool result = true;
            try
            {
                
                CurrentLog.Instance.EndExerciseActivityPage = this.GetType();
                int? RIR = null;
                var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;

                serieModelList = new List<WorkoutLogSerieModel>();
                foreach (WorkoutLogSerieModelRef l in model)
                {
                    if (l.IsFinished)
                    {
                        if (l.IsFirstWorkSet)
                            RIR = l.RIR;
                        var weightDouble = Convert.ToDecimal(l.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                        WorkoutLogSerieModelEx serieModel = new WorkoutLogSerieModelEx()
                        {
                            Exercice = new ExerciceModel() { Id = model.Id },
                            Reps = l.Reps,
                            UserId = CurrentLog.Instance.ExerciseLog.UserId,
                            Weight = model.IsAssisted ? new MultiUnityWeight(_userBodyWeight - new MultiUnityWeight(weightDouble, isKg ? "kg" : "lb").Kg, WeightUnities.kg) : new MultiUnityWeight(weightDouble, isKg ? "kg" : "lb"),
                            RIR = RIR,
                            IsWarmups = l.IsWarmups,
                            OneRM = new MultiUnityWeight(Math.Round(ComputeOneRM(model.IsAssisted ? new MultiUnityWeight(_userBodyWeight - new MultiUnityWeight(weightDouble, isKg ? "kg" : "lb").Kg, WeightUnities.kg).Kg : new MultiUnityWeight(weightDouble, isKg ? "kg" : "lb").Kg, l.Reps),2),"kg")
                        };
                        if (!l.IsWarmups)
                        {
                            if (serieModel.Weight.Kg == 0)
                                serieModel.Weight = new MultiUnityWeight(2, "kg");
                            if (LocalDBManager.Instance.GetDBSetting($"Sets{DateTime.Now.Date}") == null)
                                LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", "1");
                            else
                            {
                                var setCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"Sets{DateTime.Now.Date}").Value);
                                setCount += 1;
                                LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", $"{setCount}");
                            }
                        }
                        serieModelList.Add(serieModel);
                        //BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerie(serieModel);
                        //result = result && b.Result;
                    }
                }

                OneRMModel model1 = new OneRMModel()
                {
                    OneRM = model.RecoModel.FirstWorkSet1RM,
                    OneRMDate = DateTime.Now.AddDays(-1),
                    Reps = model.RecoModel.FirstWorkSetReps,
                    Weight = model.RecoModel.FirstWorkSetWeight
                };
                var maxModel = serieModelList.OrderByDescending(x => x.OneRM.Kg).FirstOrDefault();
                OneRMModel model0 = new OneRMModel()
                {
                    OneRM = maxModel.OneRM,
                    OneRMDate = DateTime.Now,
                    Reps = maxModel.Reps,
                    Weight = maxModel.Weight
                };
                var endPopup = new EndExercisePopup(model0, model1, model.Label, model.Id);
                await PopupNavigation.Instance.PushAsync(endPopup);
                BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerieListwithoutLoader(serieModelList);
                result = result && b.Result;
                endPopup.OnBeforeShow();

                //CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Increments
                DateTime? maxDate = null;
                try
                {
                    string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                    string exId = $"{model.Id}";
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                    {
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        {
                            maxDate = DateTime.Now;
                        }
                    }

                    await DrMuscleRestClient.Instance.AddUpdateExerciseUserLightSessionWithBodyPart(new LightSessionModel()
                    {
                        ExerciseId = model.Id,
                        IsLightSession = CurrentLog.Instance.RecommendationsByExercise[model.Id].IsLightSession,
                        MaxChallengeDate = maxDate,
                        IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "false" ? false : true,
                        BodypartId = (long)model.BodyPartId
                    });
                }
                catch (Exception ex)
                {

                }
            }
            catch (Exception ex)
            {
                var properties = new Dictionary<string, string>
                {
                    { "FinishExercise", $"{ex.StackTrace}" }
                };
                Crashes.TrackError(ex, properties);
            }
            try
            {
                if (result)
                {
                    LocalDBManager.Instance.SetDBSetting($"SetupWeight{model.Id}", "");
                    if (Timer.Instance.State == "RUNNING")
                    {
                        await Timer.Instance.StopTimer();
                    }

                    if ((Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == model.Id) == null)
                        (Application.Current as App)?.FinishedExercices.Add(GetExerciseModel(model));

                    LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                    LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                    DependencyService.Get<IFirebase>().LogEvent("finished_exercise", "");
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(model.Id))
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(model.Id);

                    ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                    ((App)Application.Current).WorkoutLogContext.SaveContexts();
                    //foreach (var item in items)
                    //{
                    //    item.IsExerciseFinished = true;
                    //}
                    //items.First().IsNext = true;

                    CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(model);
                    CurrentLog.Instance.IsFromExercise = true;

                    DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1151);
                    if (LocalDBManager.Instance.GetDBSetting("Reminder5th") == null)
                        LocalDBManager.Instance.SetDBSetting("Reminder5th", "true");
                    if (LocalDBManager.Instance.GetDBSetting("Reminder5th").Value == "true")
                    {
                        var timeSpan = new TimeSpan(4, DateTime.Now.Hour, DateTime.Now.Minute, 0); //new TimeSpan(4, DateTime.Now.Hour, DateTime.Now.Minute, 0); //new TimeSpan(0, DateTime.Now.Hour, DateTime.Now.AddMinutes(5).Minute, 0);//
                        DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification("Dr. Muscle", "No workout in 5 days—do a 10-min bodyweight workout to stay on track?", timeSpan, 1151, NotificationInterval.Week);
                    }

                    CurrentLog.Instance.EndExerciseActivityPage = GetType();
                    //await PopupNavigation.Instance.PushAsync(new EndExercisePopup());
                    //await PagesFactory.PushAsync<EndExercisePage>();
                }
            }
            catch (Exception ex)
            {
                var properties = new Dictionary<string, string>
                    {
                        { "FinishExerciseResultHandle", $"{ex.StackTrace}" }
                    };
                Crashes.TrackError(ex, properties);
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
            }
        }


        private async void SaveWorkoutButton_Clicked(object sender, EventArgs e)
        {
            ConfirmConfig ShowConfirmPopUp = new ConfirmConfig()
            {
                Message = AppResources.AreYouSureYouAreFinishedAndWantToSaveTodaysWorkout,
                Title = AppResources.FinishAndSaveWorkoutQuestion,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = AppResources.FinishAndSave,
                CancelText = AppResources.Cancel,
                OnAction = async (bool ok) =>
                {
                    if (ok)
                    {
                        SavingExcercise();
                    }
                }
            };
            UserDialogs.Instance.Confirm(ShowConfirmPopUp);
        }

        private async void SavingExcercise()
        {
            //if (!await CanGoFurther())
            //{
            //    return;
            //}
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                {
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                        LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                    }
                }
            }
            catch (Exception ex)
            {

            }
            try
            {
                if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp == 999)
                {
                    foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                    {
                        LocalDBManager.Instance.SetDBSetting($"workout{exerciceModel.Id}", "false");
                    }
                    Navigation.PopToRootAsync();
                    
                    return;
                }
            }
            catch (Exception ex)
            {
                await PagesFactory.PopToRootAsync();
            }
            var responseLog = await DrMuscleRestClient.Instance.SaveGetWorkoutInfo(new SaveWorkoutModel() { WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id });

            LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
            LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());
            bool isSystem = CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise;
            foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
            {
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(exerciceModel.Id))
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(exerciceModel.Id);

                LocalDBManager.Instance.SetDBSetting($"workout{exerciceModel.Id}", "false");
                try
                {
                    bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == exerciceModel.Id);
                    if (isSwapped)
                    {
                        long targetExerciseId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == exerciceModel.Id).TargetExerciseId;

                        var obj = (Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == targetExerciseId);
                        if (obj != null)
                        {
                            LocalDBManager.Instance.SetDBSetting($"workout{targetExerciseId}", "false");
                            (Application.Current as App)?.FinishedExercices.Remove(obj);
                        }
                    }
                }
                catch (Exception ex)
                {

                }

            }
            CurrentLog.Instance.CurrentWorkoutTemplate = null;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
            CurrentLog.Instance.WorkoutStarted = false;
            string fname = LocalDBManager.Instance.GetDBSetting("firstname").Value;
            string Msg = $"{AppResources.Congratulations} {fname}!";
            try
            {
                if (responseLog != null && responseLog.RecommendedProgram != null)
                {

                    if (responseLog.RecommendedProgram.RemainingToLevelUp > 0)
                        Msg += $" {AppResources.YouAre1WorkoutCloserToNewExercisesYourProgramWillLevelUpIn} {responseLog.RecommendedProgram.RemainingToLevelUp} {AppResources.WorkoutsFullStop}";

                }

                if (responseLog != null)
                {
                    Xamarin.Forms.MessagingCenter.Send<FinishWorkoutMessage>(new FinishWorkoutMessage() { PopupMessage = Msg }, "FinishWorkoutMessage");
                }
                Navigation.PopToRootAsync();
            }
            catch (Exception ex)
            {
                await PagesFactory.PopToRootAsync();
            }

        }


        private async void PlateTapped(object sender, EventArgs e)
        {
            try
            {
                DependencyService.Get<IKeyboardHelper>().HideKeyboard();
            }
            catch (Exception ex)
            {

            }
            //if (Device.RuntimePlatform.Equals(Device.Android))
            //{

            //    var items = exerciseItems.Where(x => x.IsNextExercise).ToList();
            //    var isOpen = false;
            //        if (items != null && items.Count>0)
            //    {
            //        foreach (ExerciseWorkSetsModel item in items)
            //        {
            //            if (item.Count > 0)
            //            {
            //                foreach (WorkoutLogSerieModelRef model in item)
            //                {
            //                    if(model.IsNext && !model.IsFinished)
            //                    {
            //                        var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;

            //                        CurrentLog.Instance.CurrentWeight = isKg ? model.Weight.Kg : model.Weight.Lb;

            //                        var page = new PlateCalculatorPopup();
            //                        await PopupNavigation.Instance.PushAsync(page);
            //                    }
            //                    isOpen = true;
            //                    break;
            //                } 
            //            }
            //            if (isOpen)
            //                break;
            //        }


            //    }

            //}
            //else

            CurrentLog.Instance.CurrentWeight = App.PCWeight;

            var page = new PlateCalculatorPopup();
            await PopupNavigation.Instance.PushAsync(page);
            //Xamarin.Forms.MessagingCenter.Send<PlateCalulatorMessage>(new PlateCalulatorMessage(), "PlateCalulatorMessage");

        }
        private async void TimerTapped(object sender, EventArgs e)
        {
            SlideTimerAction();
        }

        private async void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            try
            {

            if (contextMenuStack != null)
                HideContextButton();
                //if (ExerciseListView.SelectedItem == null)
                //    return;
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                    //Device.BeginInvokeOnMainThread(() => {
                    //    //ExerciseListView.BeginRefresh();
                    //    //ExerciseListView.EndRefresh();
                    //});
                }
                WorkoutLogSerieModelRef workout = (WorkoutLogSerieModelRef)e.Item;
            if (workout != null)
            {
                if (!workout.IsFinished)
                {
                    if (ExerciseListView.SelectedItem != null)
                        ExerciseListView.SelectedItem = null;
                    return;
                }
                workout.IsFinished = true;
                workout.IsEditing = true;
                workout.IsNext = true;
                //if (Timer.Instance.State != "RUNNING")
                //    Xamarin.Forms.MessagingCenter.Send<SaveSetMessage>(new SaveSetMessage() { model = workout, IsFinishExercise = false }, "SaveSetMessage");
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                if (Device.RuntimePlatform.Equals(Device.iOS))
                    {
                    workout.IsSizeChanged = !workout.IsSizeChanged;
                    ExerciseListView.ItemsSource = exerciseItems;
                    }
                else
                        ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

                }
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                    //Device.BeginInvokeOnMainThread(() => {
                    //    ExerciseListView.BeginRefresh();
                    //    ExerciseListView.EndRefresh();
                    //});
                }

            }
            catch (Exception ex)
            {

            }

        }
        private async void PicktorialTapped(object sender, EventArgs e)
        {
            if(contextMenuStack != null)
                HideContextButton();
            ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;
            if (!string.IsNullOrEmpty(m.VideoUrl))
            {
                CurrentLog.Instance.VideoExercise = GetExerciseModel(m);
                await PagesFactory.PushAsync<ExerciseVideoPage>();
            }

            // OnCancelClicked(sender, e);
        }

        private async void CellHeaderTapped(object sender, EventArgs e)
        {
            _isAskedforLightSession = false;
            _isAskedforDeload = false;
            if (!CrossConnectivity.Current.IsConnected)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
                return;
            }
            if (contextMenuStack != null)
                HideContextButton();
            var currentOpenExer = exerciseItems.Where(x => x.IsNextExercise).FirstOrDefault();

            ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;


            try
            {

                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                LocalDBManager.Instance.SetDBReco("RReps" + m.Id + setStyle + "challenge", $"");

            }
            catch (Exception ex)
            {

            }
            if (m.IsNextExercise && m.Count>0)
            {
                m.Clear();
                m.IsNextExercise = false;
                return;   
            }
            m.IsNextExercise = true;// !m.IsNextExercise;

            if (m.IsFinished)
            {
                try
                {
                    List<WorkoutLogSerieModelRef> setList = new List<WorkoutLogSerieModelRef>();

                    List<HistoryModel> historyModel = await DrMuscleRestClient.Instance.GetLastExerciseHistory(m.Id);
                    if (historyModel.Count > 0)
                    {
                        var logSerie = historyModel.First().Exercises[0].Sets.OrderBy(x=>x.LogDate).ToList();
                        var warmupList = logSerie.Where(l => l.IsWarmups).ToList();
                        for (int i = 0; i < warmupList.Count; i++)
                        {
                            setList.Add(new WorkoutLogSerieModelRef()
                            {
                                Weight = warmupList[i].Weight,
                                IsWarmups = warmupList[i].IsWarmups,
                                Reps = warmupList[i].Reps,
                                SetNo = $"W",
                                IsLastWarmupSet = i == warmupList.Where(l => l.IsWarmups).ToList().Count - 1 ? true : false,
                                IsHeaderCell = i == 0 ? true : false,
                                HeaderImage = "",
                                HeaderTitle = "",
                                ExerciseName = m.Label,
                                EquipmentId = m.EquipmentId,
                                SetTitle = i == 0 ? "Let's warm up:" : "",
                                IsFinished = true,
                                IsExerciseFinished = true,
                                Id = logSerie[i].Id,
                                IsTimeBased = m.IsTimeBased,
                                IsBodyweight = m.IsBodyweight,
                                IsNormalset = m.IsNormalSets

                            });

                        }
                        if (setList.Count > 1)
                        {
                            setList.Last().SetTitle = "Last warm-up set:";
                        }

                        var worksetsList = logSerie.Where(l => l.IsWarmups == false).ToList();
                    for (int j = 0; j < worksetsList.Count; j++)
                    {
                        
                        var rec = new WorkoutLogSerieModelRef()
                        {
                            Weight = worksetsList[j].Weight,
                            IsWarmups = worksetsList[j].IsWarmups,
                            Reps = worksetsList[j].Reps,
                            SetNo = $"{setList.Where(x=>x.IsWarmups == false).Count() + 1}",
                            ExerciseName = m.Label,
                            EquipmentId = m.EquipmentId,
                            IsFirstWorkSet = j == 0 ? true : false,
                            SetTitle = j==0 ? "1st work set—you got this:" : "",
                            IsFinished = true,
                            IsExerciseFinished = true,
                            Id = worksetsList[j].Id,
                            IsTimeBased = m.IsTimeBased,
                            IsBodyweight = m.IsBodyweight,
                            IsNormalset = m.IsNormalSets
                        };
                        if (setList.Count == 0)
                        {
                            rec.IsHeaderCell = true;
                            rec.HeaderImage = "";
                            rec.HeaderTitle = "";
                        }
                        setList.Add(rec);
                    }

                    
                    if ((setList.Count - logSerie.Where(l => l.IsWarmups).ToList().Count) > 3)
                    {
                        setList[setList.Count - 2].SetTitle = "Almost done—keep it up!";
                        setList.Last().SetTitle = "Last set—you can do this!";
                    }
                    else if ((setList.Count - logSerie.Where(l => l.IsWarmups).ToList().Count) > 2)
                    {
                        setList.Last().SetTitle = "Last set—you can do this!";
                    }

                        if (setList.First().IsWarmups)
                    {
                        var warmString = setList.Where(l => l.IsWarmups).ToList().Count < 2 ? "warm-up" : "warm-ups";
                        setList.First().SetTitle = $"{setList.Where(l => l.IsWarmups).ToList().Count} {warmString}, {setList.Where(l => !l.IsWarmups).ToList().Count} work sets";
                    }

                        if (setList.Count > 0)
                    {
                        setList.Last().IsLastSet = true;
                        //if (m.IsFirstSide)
                        //    setList.Last().IsFirstSide = true;
                    }
                        SetIndex(setList);
                        foreach (var item in setList)
                    {
                        m.Add(item);
                    }
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                    {
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[m.Id] = new ObservableCollection<WorkoutLogSerieModelRef>(setList);
                    }
                    else
                    {
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, new ObservableCollection<WorkoutLogSerieModelRef>(setList));
                    }
                }
                   
                }
                catch (Exception ex)
                {

                }
                return;
            }
            if (m.RecoModel != null)
            {
                FetchReco(m, null);
                return;
            }
            NewExerciseLogResponseModel newExercise = await DrMuscleRestClient.Instance.IsNewExerciseWithSessionInfo(new ExerciceModel() { Id = m.Id });
                if (newExercise != null)
                {
                    if (!newExercise.IsNewExercise)
                    {
                        try
                        {
                            DateTime? lastLogDate = newExercise.LastLogDate;
                            int? sessionDays = null;


                            string WeightRecommandation;
                            RecommendationModel reco = null;

                            //Todo: clean up on 2019 01 18
                            if (LocalDBManager.Instance.GetDBSetting("SetStyle") == null)
                                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                            if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
                                LocalDBManager.Instance.SetDBSetting("QuickMode", "false");
                            var bodyPartname = "";


                            switch (m.BodyPartId)
                            {
                                case 1:

                                    break;
                                case 2:
                                    bodyPartname = "Shoulders";
                                    break;
                                case 3:
                                    bodyPartname = "Chest";
                                    break;
                                case 4:
                                    bodyPartname = "Back";
                                    break;
                                case 5:
                                    bodyPartname = "Biceps";
                                    break;
                                case 6:
                                    bodyPartname = "Triceps";
                                    break;
                                case 7:
                                    bodyPartname = "Abs";
                                    break;
                                case 8:
                                    bodyPartname = "Legs";
                                    break;
                                case 9:
                                    bodyPartname = "Calves";
                                    break;
                                case 10:
                                    bodyPartname = "Neck";
                                    break;
                                case 11:
                                    bodyPartname = "Forearm";
                                    break;
                            default:
                                    //
                                    break;
                            }
                        if (CurrentLog.Instance.ExerciseLog == null)
                        {
                            CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                            CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);
                        }
                        if (lastLogDate != null)
                            {
                                var days = (int)(DateTime.Now - (DateTime)lastLogDate).TotalDays;
                            if (days >= 5 && days <= 9)
                                sessionDays = days;
                            if (days > 9)
                                {
                                    //ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                                    //{
                                    //    Title = "Light session?",
                                    //    Message = string.IsNullOrEmpty(bodyPartname) == false ? $"The last time you trained {bodyPartname.ToLower()} was {days} {AppResources.DaysAgoDoALightSessionToRecover}" : string.Format("{0} {1} {2} {3} {4}", "The last time you trained", CurrentLog.Instance.ExerciseLog.Exercice.Label, AppResources.was, days, AppResources.DaysAgoDoALightSessionToRecover),
                                    //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                    //    OkText = AppResources.LightSession,
                                    //    CancelText = AppResources.Cancel,
                                    //};
                                    //try
                                    //{
                                    //    LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                                    //    LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                                    //}
                                    //catch (Exception ex)
                                    //{

                                    //}
                                if (LocalDBManager.Instance.GetDBSetting($"Normal{m.Id}")?.Value != "true")
                                {
                                    LocalDBManager.Instance.SetDBSetting($"IsAskedLightSession{CurrentLog.Instance.CurrentWorkoutTemplate.Id}_{m.BodyPartId}", "true");
                                    if (days > 50)
                                        days = 50;
                                    sessionDays = days;
                                    App.WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
                                    App.BodypartId = (int)CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId;
                                    App.Days = days;
                                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                                    {
                                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                                        m.RecoModel = null;
                                    }
                                }
                                else
                                {

                                    days = 1;
                                    sessionDays = 1;
                                }
                                //var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                                //if (isConfirm)
                                //{
                                //    if (days > 50)
                                //        days = 50;
                                //    sessionDays = days;
                                //    App.BodypartId = (int)CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId;
                                //    App.Days = days;
                                //if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                                //    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                                //if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                                //    {
                                //        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                                //        m.RecoModel = null;
                                //    }
                                //}
                                //else
                                //{
                                //    _isAskedforLightSession = true;
                                //    sessionDays = null;
                                //    App.Days = 0;
                                //    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                                //    {
                                //        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                                //        m.RecoModel = null;
                                //    }
                                //}

                            }
                            }

                        if (_injuryRehabRunning && _injuryRehabWithExerciseId == m.Id)
                        {
                            UserDialogs.Instance.HideLoading();
                            InjuryRehabSetup(m);
                        }
                        else
                            await FetchReco(m, sessionDays);
                        }
                        catch (Exception ex)
                        {

                        }

                    }
                    else
                    {
                    _injuryRehabRunning = false;
                    RunExercise(m);
                    }
                }
                else
                    await FetchReco(m);
           
        }

        private async Task FetchReco(ExerciseWorkSetsModel m, int? sessionDays = null)
        {
            if (m.IsNextExercise)
            {
                long? workoutId = null;
                

                if (m.Count > 0)
                {
                    m.Clear();
                    return;
                }

                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                {
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                }
                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;

                string exId = $"{m.Id}";
                var lastTime = LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle);


                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                {
                    var sets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[m.Id];

                    if (m.RecoModel == null)
                    {
                        if (lastTime != null)
                        {
                            try
                            {
                                if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value))
                                {
                                    var LastRecoPlus1Day = Convert.ToDateTime(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value);
                                    if (LastRecoPlus1Day > DateTime.Now)
                                    {
                                        var recommendation = RecoContext.GetReco("Reco" + exId + setStyle);
                                        if (recommendation != null)
                                        {
                                            m.RecoModel = recommendation;
                                            m.RecoModel.IsDeload = false;
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Exception is:{ex.ToString()}");
                            }
                        }
                    }

                    if (m.RecoModel != null)
                    {
                        m.IsPyramid = m.RecoModel.IsPyramid;
                        m.IsReversePyramid = m.RecoModel.IsReversePyramid;
                        if (CurrentLog.Instance.ExerciseLog == null)
                        {
                            CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                        }
                        CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);
                        

                        if (CurrentLog.Instance.RecommendationsByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                            CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id] = m.RecoModel;
                        else
                            CurrentLog.Instance.RecommendationsByExercise.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, m.RecoModel);

                    }
                    foreach (var cacheSet in sets)
                    {
                        m.Add(cacheSet);
                    }
                    if (sets.Any(x => x.IsFinished == false))
                        MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NewSet, SetModel = sets.FirstOrDefault(x => x.IsFinished == false), Label = m.Label }, "SendWatchMessage");
                    else
                        MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.FinishExercise, SetModel = sets.Last(), Label = m.Label }, "SendWatchMessage");
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                        ExerciseListView.ScrollTo(sets.First(), ScrollToPosition.Start, true);
                    await Task.Delay(300);

                    ExerciseListView.ItemPosition = exerciseItems.IndexOf(m);
                    ExerciseListView.IsScrolled = !ExerciseListView.IsScrolled;
                    if (m.RecoModel != null)
                    {
                        ScrollToSnap(sets.ToList(), m);
                        return;
                    }
                    
                }

                
                if (lastTime != null)
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value))
                        {
                            var LastRecoPlus1Day = Convert.ToDateTime(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value);
                            if (LastRecoPlus1Day > DateTime.Now)
                            {
                                var recommendation = RecoContext.GetReco("Reco" + exId + setStyle);
                                if (recommendation != null)
                                {
                                    m.RecoModel = recommendation;
                                    m.RecoModel.IsDeload = false;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Exception is:{ex.ToString()}");
                    }
                }
                if (LocalDBManager.Instance.GetDBSetting("IsPyramid") == null)
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");

                bool? isQuick = false;
                if (LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "null")
                    isQuick = null;
                else
                    isQuick = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false;
                bool IsLastLoad = false;
                if (m.RecoModel == null)
                {
                    if (App.IsFreePlan)
                    {
                        var dailyReset = LocalDBManager.Instance.GetDBSetting("DailyReset");
                        var weeklyReset = LocalDBManager.Instance.GetDBSetting("WeeklyReset");
                        if (dailyReset == null || string.IsNullOrEmpty(dailyReset?.Value))
                            LocalDBManager.Instance.SetDBSetting("DailyReset", "0");
                        if (weeklyReset == null || string.IsNullOrEmpty(weeklyReset?.Value))
                            LocalDBManager.Instance.SetDBSetting("WeeklyReset", "0");
                        if (int.Parse(LocalDBManager.Instance.GetDBSetting("WeeklyReset")?.Value) > 7 || int.Parse(LocalDBManager.Instance.GetDBSetting("DailyReset")?.Value) > 0)
                            IsLastLoad = true;
                        if (IsLastLoad)
                        {
                            //Show popup here
                            LocalDBManager.Instance.SetDBSetting("DailyReset", "2");
                            
                            ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                            {
                                Title = "No free recommendations left today",
                                Message = $"Loading your last sets and reps. Upgrade for unlimited recommendations.",
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = "Upgrade",
                                CancelText = "Maybe later",
                            };
                            var res = await UserDialogs.Instance.ConfirmAsync(ShowRIRPopUp);
                            if (res)
                            {
                                await PagesFactory.PushAsync<SubscriptionPage>();
                                return;
                            }
                        }
                        else
                        {

                            var count = int.Parse(LocalDBManager.Instance.GetDBSetting("WeeklyReset")?.Value) + 1;
                            LocalDBManager.Instance.SetDBSetting("WeeklyReset", Convert.ToString(count));
                            LocalDBManager.Instance.SetDBSetting("DailyReset", "1");
                            DrMuscleRestClient.Instance.SetUserExerciseCount(new UserInfosModel()
                            {
                                DailyExerciseCount = 1,
                                WeeklyExerciseCount = count
                            });
                        }

                    }
                    if (LocalDBManager.Instance.GetDBSetting("SetStyle").Value == "Normal" || m.Id == 16508 || m.BodyPartId == 12 || m.IsFlexibility)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("IsPyramid").Value == "true" && m.IsBodyweight && m.BodyPartId != 12 && !m.IsFlexibility)
                        {
                            m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationRestPauseRIRForExerciseWithoutDeload(new GetRecommendationForExerciseModel()
                            {
                                Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                ExerciseId = m.Id,
                                IsQuickMode = isQuick,
                                LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                WorkoutId = workoutId,
                                IsFreePlan = IsLastLoad
                            }); ;
                        }
                        else
                        {
                            m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationNormalRIRForExerciseWithoutDeload(new GetRecommendationForExerciseModel()
                            {
                                Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                ExerciseId = m.Id,
                                IsQuickMode = isQuick,
                                LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                WorkoutId = workoutId,
                                IsFreePlan = IsLastLoad
                            });
                        }

                    }
                    else
                    {

                        m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationRestPauseRIRForExerciseWithoutDeload(new GetRecommendationForExerciseModel()
                        {
                            Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                            ExerciseId = m.Id,
                            IsQuickMode = isQuick,
                            LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                            WorkoutId = workoutId,
                            IsFreePlan = IsLastLoad
                        });

                    }

                    if (m.RecoModel != null)
                    {
                        if (m.RecoModel.IsDefaultUnilateral != null)
                            m.IsUnilateral = (bool)m.RecoModel.IsDefaultUnilateral;
                        if (m.RecoModel.HistorySet != null)
                             m.RecoModel.HistorySet.Reverse();
                    }
                }
                bool ShowWarmups = false;
                if (m.RecoModel != null)
                {
                    if (LocalDBManager.Instance.GetDBSetting($"Normal{m.Id}")?.Value == "true" || !string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting($"InjuryWeight{m.Id}")?.Value))
                    {
                        m.RecoModel.days = 0;
                        LocalDBManager.Instance.SetDBReco("RReps" + m.Id + setStyle + "challenge", $"");
                        LocalDBManager.Instance.SetDBReco("RReps" + m.Id + setStyle + "delaod", $"");
                        sessionDays = null;
                    }
                    if (m.BodyPartId == 12)
                    {
                        if (m.RecoModel.IsNormalSets != true)
                        {
                            m.RecoModel.IsNormalSets = true;
                            m.RecoModel.Series = m.RecoModel.Series + m.RecoModel.NbPauses;
                            m.RecoModel.NbPauses = 0;
                        }
                        if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("CardioSpeed")?.Value))
                        {
                            m.RecoModel.Speed = 4.0;
                            LocalDBManager.Instance.SetDBSetting("CardioSpeed", "4.0");
                        }
                        else
                        {
                            var speed = LocalDBManager.Instance.GetDBSetting("CardioSpeed")?.Value?.ReplaceWithDot();
                            try
                            {
                                m.RecoModel.Speed = Convert.ToDouble(speed, CultureInfo.InvariantCulture);
                            }
                            catch (Exception ex)
                            {
                                m.RecoModel.Speed = 4.0;
                            }
                        }
                    }
                    if (m.IsAssisted && m.RecoModel.Weight.Kg> _userBodyWeight)
                    {
                        m.RecoModel.Weight = new MultiUnityWeight(_userBodyWeight, WeightUnities.kg);
                    }
                    if (m.IsFlexibility)
                    {
                        try
                        {
                            var mobilityreps = LocalDBManager.Instance.GetDBSetting("MobilityRep")?.Value;
                            int reps = 0;
                            if (!string.IsNullOrEmpty(mobilityreps))
                                reps = int.Parse(mobilityreps);
                            if (reps < m.RecoModel.Reps)
                                m.RecoModel.Reps = reps;
                        }
                        catch (Exception) { }
                    }
                    var weights = LocalDBManager.Instance.GetDBSetting($"SetupWeight{m.Id}")?.Value;
                    if (!string.IsNullOrEmpty(weights))
                    {

                        if (!m.IsBodyweight)
                        {
                            m.RecoModel.Weight = new MultiUnityWeight(Convert.ToDecimal(weights, CultureInfo.InvariantCulture), "kg", true);
                            m.RecoModel.IsManual = true;
                            m.RecoModel.Reps = 4;
                            m.RecoModel.NbRepsPauses = 2;
                            m.RecoModel.IsBackOffSet = false;
                            m.RecoModel.BackOffSetWeight = null;
                        }
                        else
                        {
                            var reps = LocalDBManager.Instance.GetDBSetting($"SetupReps{m.Id}")?.Value;
                            if (!string.IsNullOrEmpty(reps))
                            {
                                if (int.Parse(reps) > 2)
                                    m.RecoModel.Reps = m.BodyPartId == 12 ? int.Parse(reps) : int.Parse(reps) - 2;
                                else if (int.Parse(reps) == 2)
                                    m.RecoModel.Reps = 1;
                            }
                            else
                            if (m.RecoModel.Reps > 2)
                                m.RecoModel.Reps = m.RecoModel.Reps - 2;
                        }
                        m.RecoModel.NbRepsPauses = 1;

                        ShowWarmups = true;

                        //AlertConfig challengeConfig = new AlertConfig()
                        //{
                        //    Title = $"{m.Label}: ready for a new record?",
                        //    Message = $"Try a challenge. Do as many reps as you can on your first work set. Stop before your form breaks down.",
                        //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //    OkText = "Try a challenge",
                        //};
                        //await UserDialogs.Instance.AlertAsync(challengeConfig);
                    }
                    var isKg1 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;

                    var injuryWeights = LocalDBManager.Instance.GetDBSetting($"InjuryWeight{m.Id}")?.Value;
                    if (!string.IsNullOrEmpty(injuryWeights))
                    {
                        _injuryRehabRunning = false;
                        m.RecoModel.IsDeload = false;
                        m.RecoModel.IsMaxChallenge = false;
                        sessionDays = null;
                        if (!m.IsBodyweight)
                        {
                            m.RecoModel.Weight = new MultiUnityWeight(Convert.ToDecimal(injuryWeights, CultureInfo.InvariantCulture), isKg1 ? "kg" : "lb", true);
                            m.RecoModel.Reps = 5;
                            m.RecoModel.NbRepsPauses = 2;
                            m.RecoModel.NbPauses = 2;
                            m.RecoModel.Series = 1;
                        }
                        else
                        {
                            var reps = LocalDBManager.Instance.GetDBSetting($"InjuryReps{m.Id}")?.Value;
                            if (!string.IsNullOrEmpty(reps))
                            {
                                m.RecoModel.Reps = int.Parse(reps);
                            }
                            else if (m.RecoModel.Reps > 2)
                                m.RecoModel.Reps = m.RecoModel.Reps - 2;
                            m.RecoModel.NbPauses = 2;
                        }
                        //m.RecoModel.NbRepsPauses = 1;

                        ShowWarmups = false;
                    }
                    var keyVal = "";

                    if (m.RecoModel.isPlateAvailable)
                    {

                        if (isKg1)
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("PlatesKg").Value;

                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("PlatesKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomePlatesKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherPlatesKg").Value;
                            }
                        }
                        else
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("PlatesLb").Value;
                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("PlatesLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomePlatesLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherPlatesLb").Value;
                            }
                        }

                    }
                    if (m.RecoModel.isDumbbellAvailable)
                    {

                        if (isKg1)
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("DumbbellKg").Value;

                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("DumbbellKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomeDumbbellKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherDumbbellKg").Value;
                            }
                        }
                        else
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("PlatesLb").Value;
                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("DumbbellLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomeDumbbellLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherDumbbellLb").Value;
                            }
                        }
                    }
                    if (m.RecoModel.isBandsAvailable)
                    {

                        if (isKg1)
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;

                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomeBandsKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherBandsKg").Value;
                            }
                        }
                        else
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomeBandsLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherBandsLb").Value;
                            }
                        }
                    }
                    if (m.RecoModel.isPulleyAvailable)
                    {

                        if (isKg1)
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("PulleyKg").Value;

                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("PulleyKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomePulleyKg").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherPulleyKg").Value;
                            }
                        }
                        else
                        {
                            keyVal = LocalDBManager.Instance.GetDBSetting("PulleyLb").Value;
                            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("PulleyLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("HomePulleyLb").Value;
                            }
                            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                            {
                                keyVal = LocalDBManager.Instance.GetDBSetting("OtherPulleyLb").Value;
                            }
                        }
                    }
                    RecoComputation.ComputeWarmups(m.RecoModel,m.Id, m, keyVal, isKg1, _userBodyWeight);
                    m.RecoModel.IsLightSession = sessionDays == null ? false : sessionDays > 9 ? true : false;
                    if (m.RecoModel.IsLightSession)
                        m.RecoModel.IsDeload = false;
                    bool IsMaxChallenge = false;

                    if (m.RecoModel.IsDeload || _isAskedforLightSession)
                    {
                        m.RecoModel.IsMaxChallenge = false;
                        _isAskedforLightSession = false;
                    }
                    if (m.IsBodyweight)
                    {
                        m.RecoModel.IsPyramid = false;
                        m.RecoModel.IsReversePyramid = false;
                        m.IsPyramid = false;
                        m.IsReversePyramid = false;
                    }
                    if (m.RecoModel.IsPyramid)
                    {
                        m.IsPyramid = true;
                        m.RecoModel.IsBackOffSet = false;
                        m.RecoModel.BackOffSetWeight = null;
                    }
                    //if (m.RecoModel.IsPyramid)
                    //{
                    //    if ((m.RecoModel.Increments != null && m.RecoModel.Increments.Kg >= m.RecoModel.Weight.Kg) || (m.RecoModel.Increments == null && m.RecoModel.Weight.Kg <= 2) || (m.RecoModel.Min != null && m.RecoModel.Min.Kg >= m.RecoModel.Weight.Kg))
                    //    {
                    //        m.RecoModel.IsPyramid = false;
                    //        m.IsPyramid = false;
                    //        if (m.RecoModel.IsNormalSets)
                    //        {
                    //            m.RecoModel.IsNormalSets = false;
                    //            m.IsNormalSets = false;
                    //            m.RecoModel.NbPauses = m.RecoModel.Series - 1;
                    //            m.RecoModel.Series = 1;
                    //            m.RecoModel.NbRepsPauses = m.RecoModel.Reps <= 5 ? (int)Math.Ceiling((decimal)m.RecoModel.Reps / (decimal)3) : (int)m.RecoModel.Reps / 3;
                    //        }
                    //    }
                    //    else
                    //    { 
                    //        m.IsPyramid = true;
                    //        m.IsPyramid = true;
                    //        if (m.RecoModel.IsNormalSets == true)
                    //        {
                    //            //if (m.RecoModel.Series <3)
                    //            //m.RecoModel.Series = 3;
                    //        }
                    //        else
                    //        {
                    //            m.RecoModel.NbPauses = m.RecoModel.NbPauses + m.RecoModel.Series;
                    //            m.RecoModel.Series = 0;
                    //            //if (m.RecoModel.NbPauses < 3)
                    //            //m.RecoModel.NbPauses = 3;
                    //        }
                    //    }
                    //}
                    if (m.RecoModel.IsReversePyramid)
                    {
                        m.IsReversePyramid = true;
                        m.RecoModel.NbPauses = m.RecoModel.NbPauses + m.RecoModel.Series;
                        m.RecoModel.Series = 0;
                    }
                    if (m.RecoModel.Reps <= 0)
                        m.RecoModel.Reps = 1;
                    if (m.RecoModel.NbRepsPauses <= 0)
                        m.RecoModel.NbRepsPauses = 1;

                    RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                    LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + exId + setStyle, DateTime.Now.AddDays(3).ToString());
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                    {
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                        {
                            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");
                            m.RecoModel.IsDeload = true;
                            m.RecoModel.RecommendationInKg = m.RecoModel.Weight.Kg - (m.RecoModel.Weight.Kg * (decimal)0.1);
                            
                            
                            m.RecoModel.RecommendationInKg = m.RecoModel.Weight.Kg - (m.RecoModel.Weight.Kg * (decimal)0.1);
                            if (m.RecoModel.IsNormalSets || m.RecoModel.IsReversePyramid)
                            {
                                m.RecoModel = RecoComputation.GetNormalDeload(m.RecoModel, keyVal, isKg1);
                            }
                            else
                            {
                                m.RecoModel = RecoComputation.GetRestPauseDeload(m.RecoModel, keyVal, isKg1);
                            }
                            
                            m.RecoModel.IsLightSession = true;
                        }
                    }
                    var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                    if (m.RecoModel.IsDeload && !m.RecoModel.IsLightSession)
                    {
                        var per = string.Format("{0:0.00}%", m.RecoModel.OneRMPercentage*100);
                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                        {
                            Title = $"Deload {m.Label}?",
                            Message = $"Strength {per} last time—deload to recover?",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Deload",
                            CancelText = AppResources.Cancel,
                        };
                        _isAskedforDeload = true;
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                        //if (isConfirm)
                        //{
                        //    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");

                        //    if (m.RecoModel.IsNormalSets || m.RecoModel.IsReversePyramid)
                        //    {
                        //        m.RecoModel = RecoComputation.GetNormalDeload(m.RecoModel, keyVal, isKg1);
                        //    }
                        //    else
                        //    {
                        //        m.RecoModel = RecoComputation.GetRestPauseDeload(m.RecoModel, keyVal, isKg1);
                        //    }

                        //}
                        //else
                        //{
                        //    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"");
                        //    m.RecoModel.IsDeload = false;
                        //    RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                        //}
                        if (LocalDBManager.Instance.GetDBSetting($"Normal{m.Id}")?.Value != "true")
                        {
                            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");
                            //Create new Reco for deload
                            m.RecoModel.RecommendationInKg = m.RecoModel.Weight.Kg - (m.RecoModel.Weight.Kg * (decimal)0.1);
                            if (m.RecoModel.IsNormalSets || m.RecoModel.IsReversePyramid)
                            {
                                m.RecoModel = RecoComputation.GetNormalDeload(m.RecoModel, keyVal, isKg);
                            }
                            else
                            {
                                m.RecoModel = RecoComputation.GetRestPauseDeload(m.RecoModel, keyVal, isKg);
                            }
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting($"IsAskedDeload{CurrentLog.Instance.CurrentWorkoutTemplate.Id}_{m.BodyPartId}", "true");
                            m.RecoModel.IsDeload = false;
                            RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                        }
                    }
                    
                    m.IsNormalSets = m.RecoModel.IsNormalSets;
                    
                    string lbl3text = "";
                    string iconOrange = "";
                    
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                    {
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        {
                            m.RecoModel.Reps = (int)Math.Ceiling((decimal)m.RecoModel.Reps + ((decimal)m.RecoModel.Reps * (decimal)0.1));
                            m.RecoModel.NbRepsPauses = (int)Math.Ceiling((decimal)m.RecoModel.NbRepsPauses + ((decimal)m.RecoModel.NbRepsPauses * (decimal)0.1));
                            m.RecoModel.IsMaxChallenge = false;
                            IsMaxChallenge = true;
                        }
                    }
                    if (m.IsFlexibility)
                        m.RecoModel.IsMaxChallenge = false;
                    if (m.RecoModel.IsMaxChallenge)
                    {
                        bool isMaxChallenge = true;
                        //ConfirmConfig supersetConfig = new ConfirmConfig()
                        //{
                        //    Title = $"{m.Label}: ready for a new record?",
                        //    Message = $"Try a challenge. Do as many reps as you can on your first work set. Stop before your form breaks down.",
                        //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //    OkText = "Try a challenge",//AppResources.Challenge,
                        //    CancelText = AppResources.Cancel,
                        //};
                        //bool isMaxChallenge = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
                        //if (isMaxChallenge)
                        //{

                        //    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                        //    //LocalDBManager.Instance.SetDBSetting("RReps" + exId + setStyle, $"{reco.Reps}");
                        //    try
                        //    {
                        //        m.RecoModel.Reps = (int)Math.Ceiling((decimal)m.RecoModel.Reps + ((decimal)m.RecoModel.Reps * (decimal)0.1));
                        //        m.RecoModel.NbRepsPauses = (int)Math.Ceiling((decimal)m.RecoModel.NbRepsPauses + ((decimal)m.RecoModel.NbRepsPauses * (decimal)0.1));
                        //        IsMaxChallenge = true;
                        //    }
                        //    catch (Exception ex)
                        //    {

                        //    }
                        //    _firebase.LogEvent("challenge_time", "Yes");
                        //}
                        //else
                        //{
                        //    m.RecoModel.IsMaxChallenge = false;
                        //    RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                        //    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                        //    if (LocalDBManager.Instance.GetDBSetting("timer_autoset") == null)
                        //        LocalDBManager.Instance.SetDBSetting("timer_autoset", "true");
                        //    //if (CurrentLog.Instance.RecommendationsByExercise.Count > 0 && LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                        //    //    LocalDBManager.Instance.SetDBSetting("timer_remaining", CurrentLog.Instance.GetRecommendationRestTime(m.Id, false, CurrentLog.Instance.RecommendationsByExercise[m.Id].IsNormalSets).ToString());
                        //    _firebase.LogEvent("challenge_time", "No");
                        //}
                        if (LocalDBManager.Instance.GetDBSetting($"Normal{m.Id}")?.Value != "true")
                        {
                            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                            if (LocalDBManager.Instance.GetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}") == null)
                                LocalDBManager.Instance.SetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}", "1");
                            else
                            {
                                var cnt = LocalDBManager.Instance.GetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}")?.Value;
                                if (!string.IsNullOrEmpty(cnt))
                                {
                                    var challengeCount = int.Parse(cnt);

                                    if (challengeCount == 1)
                                        LocalDBManager.Instance.SetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}", "2");
                                    if (challengeCount == 0)
                                        LocalDBManager.Instance.SetDBSetting($"{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}", "1");
                                }
                            }

                        }
                    }
                    lbl3text = "";
                    var isAddMoreSet = false;
                    if (sessionDays != null)
                    {

                        if (sessionDays >= 5 && sessionDays <= 9 && !_isAskedforDeload && !IsLastLoad && string.IsNullOrEmpty(injuryWeights))
                        {
                            var bodyPartname = CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId == 1 ? "" : Constants.AppThemeConstants.GetBodyPartName(CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId);
                            //ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                            //{
                            //    Title = $"{(string.IsNullOrEmpty(bodyPartname) == false ? bodyPartname.ToLower().FirstCharToUpper() : m.Label)} fully recovered",
                            //    Message = $"Last trained: {sessionDays} days ago. Add 1 set?",//string.Format("{0} {1} {2} {3} {4}", AppResources.TheLastTimeYouDid, string.IsNullOrEmpty(bodyPartname) == false ? bodyPartname.ToLower() : m.Label, AppResources.was, sessionDays, AppResources.DaysAgoYouShouldBeFullyRecoveredDoExtraSet),
                            //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            //    OkText = "+1 set",
                            //    CancelText = AppResources.Cancel,
                            //};

                            //var isAddMoreSet = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                            //if (isAddMoreSet)
                            //{
                            //    if (m.RecoModel.NbPauses == 0)
                            //        m.RecoModel.Series += 1;
                            //    else
                            //        m.RecoModel.NbPauses += 1;
                            //}
                            if (LocalDBManager.Instance.GetDBSetting($"Normal{m.Id}")?.Value != "true")
                            {
                                isAddMoreSet = true;
                                if (!m.RecoModel.IsReversePyramid && m.RecoModel.NbPauses == 0)
                                    m.RecoModel.Series += 1;
                                else
                                    m.RecoModel.NbPauses += 1;
                                m.RecoModel.days = sessionDays;
                                RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                                LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + exId + setStyle, DateTime.Now.AddDays(3).ToString());
                            }
                            else
                                LocalDBManager.Instance.SetDBSetting($"IsAskedAddExtraSet{CurrentLog.Instance.CurrentWorkoutTemplate.Id}_{m.BodyPartId}", "true");
                        }
                    }
                    CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                    CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);
                    if (CurrentLog.Instance.RecommendationsByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                        CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id] = m.RecoModel;
                    else
                        CurrentLog.Instance.RecommendationsByExercise.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, m.RecoModel);

                    if (sessionDays > 9)
                    {
                        lbl3text = "Light session";
                        iconOrange = "orange.png";
                    }
                    else
                        iconOrange = "";

                    if (m.RecoModel.IsDeload)
                    {
                        LocalDBManager.Instance.SetDBSetting("RecoDeload", "true");
                        lbl3text = "Deload";
                        iconOrange = "orange.png";
                    }
                    else if (m.RecoModel.IsMaxChallenge)
                    {
                        LocalDBManager.Instance.SetDBSetting("RecoDeload", "false");
                        lbl3text = "Challenge";
                        iconOrange = "done2.png";
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("RecoDeload", "false");
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                        {
                            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                            {
                                lbl3text = "Challenge";
                                iconOrange = "done2.png";
                            }
                        }
                    }

                    if (m.Count > 0)
                    {
                        m.Clear();
                        return;
                    }
                    List<WorkoutLogSerieModelRef> setList = new List<WorkoutLogSerieModelRef>();

                    bool isLowerWeightNotPossible = false;
                    if (m.IsUnilateral)
                        m.IsFirstSide = true;
                    if (m.RecoModel.WarmUpsList.Count > 0)
                    {
                        for (int i = 0; i < m.RecoModel.WarmUpsList.Count; i++)
                        {
                            setList.Add(new WorkoutLogSerieModelRef()
                            {
                                Id = m.Id,
                                Weight = !m.IsWeighted && m.RecoModel.WarmUpsList[i].WarmUpWeightSet.Kg < 1 && i != 0 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)1 * (m.RecoModel.Increments == null ? 1 : m.RecoModel.Increments.Kg), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg), "kg") : m.RecoModel.WarmUpsList[i].WarmUpWeightSet,
                                IsPlateAvailable = m.RecoModel.isPlateAvailable,
                                IsDumbbellAvailable = m.RecoModel.isDumbbellAvailable,
                                IsBandsAvailable = m.RecoModel.isBandsAvailable,
                                IsPulleyAvailable = m.RecoModel.isPulleyAvailable,
                                IsWarmups = true,
                                Reps = m.RecoModel.WarmUpsList[i].WarmUpReps,
                                PreviousReps = m.RecoModel.WarmUpsList[i].WarmUpReps,
                                SetNo = $"W",
                                IsLastWarmupSet = i == m.RecoModel.WarmUpsList.Count - 1 ? true : false,
                                IsHeaderCell = i == 0 ? true : false,
                                ShowWorkTimer =  m.IsTimeBased ? true : false,
                                HeaderImage = "",
                                HeaderTitle = lbl3text,
                                ExerciseName = m.Label,
                                IsFlexibility = m.IsFlexibility,
                                EquipmentId = m.EquipmentId,
                                Increments = m.RecoModel.Increments,
                                SetTitle = i == 0 ? "Let's warm up:" : "",
                                IsTimeBased = m.IsTimeBased,
                                IsUnilateral = m.IsUnilateral,
                                IsBodyweight = m.IsBodyweight,
                                IsNormalset = m.IsNormalSets,
                                BodypartId = m.BodyPartId,
                                IsJustSetup = ShowWarmups,
                                VideoUrl = i == 0 ? m.LocalVideo : null,
                                LastTimeSet = "",
                                IsOneHanded = m.IsOneHanded,
                                IsAssisted = m.IsAssisted,
                                Speed = m.RecoModel.Speed
                            });
                            if (m.RecoModel.HistorySet != null)
                            {
                                if (m.RecoModel.HistorySet.Count > i)
                                {
                                    var r = m.RecoModel.HistorySet[i];



                                    var WeightText = m.IsBodyweight ? "body" : $"{string.Format("{0:0.#}", Math.Round((isKg ? r.Weight.Kg : r.Weight.Lb), 2))} {(isKg ? "kg" : "lbs")}";

                                    if (m.IsAssisted)
                                        WeightText = string.Format("{0:0.##} {1}", Math.Round(isKg ? _userBodyWeight - r.Weight.Kg : new MultiUnityWeight(_userBodyWeight, WeightUnities.kg).Lb - (r.Weight.Lb), 2), isKg ? "kg" : "lbs");

                                    if (m.Id == 16508)
                                    {
                                        WeightText = string.Format("{0:0.##}", m.RecoModel.Speed);// setList.Last().IsWarmups ? "brisk" : "fast";
                                    }
                                    else if (m.BodyPartId == 12)
                                    {
                                        WeightText = string.Format("{0:0.##}", m.RecoModel.Speed); //setList.Last().IsWarmups ? "brisk" : "cooldown";

                                    }
                                    if (m.Id >= 16897 && m.Id <= 16907 || m.Id == 14279 || m.Id >= 21508 && m.Id <= 21514)
                                    {
                                        var band = Utility.HelperClass.GetBandsColor((double)Math.Round((isKg ? r.Weight.Kg : r.Weight.Lb), 2), isKg);
                                        if (!string.IsNullOrEmpty(band))
                                            WeightText = band.ToLowerInvariant();
                                    }



                                    if (r.IsWarmups)
                                    {
                                        setList.Last().LastTimeSet = $"Last time: { (m.BodyPartId == 12 ? TimeSpan.FromSeconds(r.Reps).ToString(@"mm\:ss") : r.Reps.ToString())} x {WeightText}";
                                    }
                                }
                            }

                        
                    }
                        if (setList.Count>1)
                        {
                            setList.Last().SetTitle = "Last warm-up set:";
                        }
                    }
                    bool isMarkFirstSet = false;
                    for (int j = 0; j < m.RecoModel.Series; j++)
                    {
                        isMarkFirstSet = true;
                        var AssistedWeight = new MultiUnityWeight(_userBodyWeight - m.RecoModel.Weight.Kg, 0);
                        if (AssistedWeight.Kg < 0)
                            AssistedWeight = new MultiUnityWeight(0, WeightUnities.kg);
                        var rec = new WorkoutLogSerieModelRef()
                        {
                            Id = m.Id,
                            Weight = m.IsAssisted ? AssistedWeight  : m.RecoModel.isPlateAvailable || m.RecoModel.isDumbbellAvailable || m.RecoModel.isBandsAvailable || m.RecoModel.isPulleyAvailable ? m.RecoModel.Weight : m.RecoModel.Weight.Kg < 1 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)1 * (m.RecoModel.Increments == null ? 1 : m.RecoModel.Increments.Kg), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg), "kg") : m.RecoModel.Weight,
                            IsPlateAvailable = m.RecoModel.isPlateAvailable,
                            IsDumbbellAvailable = m.RecoModel.isDumbbellAvailable,
                            IsBandsAvailable = m.RecoModel.isBandsAvailable,
                            IsPulleyAvailable = m.RecoModel.isPulleyAvailable,
                            IsWarmups = false,
                            Reps = m.RecoModel.Reps,
                            PreviousReps = m.RecoModel.Reps,
                            SetNo = $"{setList.Where(x=>x.IsWarmups == false).Count() + 1}",
                            ExerciseName = m.Label,
                            EquipmentId = m.EquipmentId,
                            IsFirstWorkSet = j == 0 ? true : false,
                            Increments = m.RecoModel.Increments,
                            SetTitle = j == 0 ?  "1st work set" : "",
                            IsTimeBased = m.IsTimeBased,
                            HeaderTitle = lbl3text,
                            IsUnilateral = m.IsUnilateral,
                            IsFlexibility = m.IsFlexibility,
                            IsBodyweight = m.IsBodyweight,
                            IsNormalset = m.IsNormalSets,
                            IsFirstSide = m.IsFirstSide,
                            IsMaxChallenge = j == 0 ? IsMaxChallenge : false,
                            BodypartId = m.BodyPartId,
                            LastTimeSet = "",
                            IsOneHanded = m.IsOneHanded,
                            IsAssisted = m.IsAssisted,
                            Speed = m.RecoModel.Speed,
                            ShowWorkTimer = m.IsTimeBased ? true : false,
                        };
                        if (j == 0 && m.RecoModel.FirstWorkSetWeight != null)
                        {
                           // var worksets = string.Format("{0:0.##} {1}", Math.Round(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : m.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                           //      var days = 0;
                           //      if (m.RecoModel.LastLogDate != null)
                           //          days = (DateTime.Now - ((DateTime)m.RecoModel.LastLogDate).ToLocalTime()).Days;
                           //      var dayString = days == 0 ? "Today" : days == 1 ? "1 day ago" : $"{days} days ago";
                           //      if (m.RecoModel.IsBodyweight)
                           //          worksets = "body";
                           //
                           //      if (m.Id == 16508 )
                           //      {
                           //          worksets = rec.IsWarmups ? "brisk" : "fast";
                           //      }
                           //      else if (m.BodyPartId == 12 && m.Count > 0)
                           //      {
                           //          worksets = setList.Last().IsWarmups ? "brisk" : "cooldown";
                           //
                           //      }
                           //      if (m.Id >= 16897 && m.Id <= 16907 || m.Id == 14279 || m.Id >= 21508 && m.Id <= 21514)
                           //      {
                           //          worksets = "bands";
                           //      }
                           //
                           //
                           //
                           //      var lastOneRM = m.RecoModel.FirstWorkSetWeight.Kg;//
                           //      lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : m.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (m.IsWeighted ? _userBodyWeight : 0), m.RecoModel.FirstWorkSetReps);
                           //
                           //
                           //
                           //
                           //
                           //      decimal weight = 0;
                           //
                           //      if (m.IsBodyweight)
                           //          weight = isKg ? m.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(m.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture);
                           //      else
                           //      {
                           //          weight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                           //      }
                           //
                           //
                           //      var newWeight = Math.Round(new MultiUnityWeight(weight, isKg ? "kg" : "lb").Kg + (m.IsWeighted ? _userBodyWeight : 0), 2);
                           //      if (m.IsBodyweight)
                           //          newWeight = new MultiUnityWeight(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(m.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg;
                           //
                           //      var currentRM = TruncateDecimal(ComputeOneRM(newWeight, m.RecoModel.Reps), 2);
                           //      lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                           //      currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);


                            
                            //var currentRM = ComputeOneRM(m.RecoModel.Weight.Kg, rec.Reps);
                            // if (currentRM != 0)
                            // {
                            //     var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                            //     rec.LastTimeSet = string.Format("Last time: {0} x {1}", m.RecoModel.FirstWorkSetReps, worksets);
                            //     rec.SetTitle = string.Format("Today: {0}{1:0.0}%", percentage >= 0 ? "+" : "", percentage);
                            //     
                            //         try
                            //         {
                            //
                            //             //rec.LastTimeSet = string.Format("Last time: {0} x {1}", m.RecoModel.FirstWorkSetReps, worksets);
                            //
                            //             if (lbl3text.Equals("Deload"))
                            //             {
                            //                 rec.SetTitle = string.Format("Deload: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                            //             }
                            //             else if (lbl3text.Equals("Light session"))
                            //             {
                            //                 rec.SetTitle = string.Format("Light session: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                            //             }
                            //             else
                            //                 rec.SetTitle = string.Format("Today: {0}{1:0.##}%", percentage >= 0 ? "+" : "", percentage);
                            //
                            //         }
                            //         catch (Exception ex)
                            //         {
                            //
                            //         }
                            //     
                            // }
                            SetCoachTipTitle(rec, m);

                        }
                        if (j > 0 && m.RecoModel.IsPyramid)
                        {
                            rec.Reps = setList.Last().Reps + j + 1;

                            if (!m.IsWeighted)
                            {
                                rec.Reps = setList.Last().Reps + j + 1;

                                var lstkgWeight = Convert.ToDecimal(setList.Last().WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                decimal weight = RecoComputation.RoundToNearestIncrementPyramid(
                                    lstkgWeight - (lstkgWeight * ((decimal)0.1)),
                                    m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg,
                                    m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                if (m.IsAssisted)
                                {
                                    weight = SaveSetPage.RoundDownToNearestIncrement(
                                        lstkgWeight + (lstkgWeight * ((decimal)0.1)),
                                        m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg,
                                        m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg, true);
                                    weight =  RecoComputation.RoundToNearestIncrement(weight, m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                }
                                if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                                {
                                    var inc = m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg;
                                    if (SaveSetPage.RoundDownToNearestIncrement(weight, inc, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg) >= SaveSetPage.RoundDownToNearestIncrement(setList.Last().Weight.Kg, inc, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg)   && !m.IsAssisted)
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(setList.Last().Weight.Kg - (m.RecoModel.Increments != null ? m.RecoModel.Increments.Kg : (setList.Last().Weight.Kg * (decimal)0.1)), m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                        if (weight == setList.Last().Weight.Kg)
                                        {
                                            rec.Reps = setList.Last().Reps;
                                            isLowerWeightNotPossible = true;
                                        }
                                        //else
                                        //    updatingItem.Reps = reps;
                                    }
                                    //else
                                    //updatingItem.Reps = reps;
                                    rec.Weight = new MultiUnityWeight(weight, "kg");
                                }
                                else
                                {
                                    var lstWeight = Convert.ToDecimal(setList.Last().WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                    var inc = m.RecoModel.Increments != null ? Math.Round(m.RecoModel.Increments.Lb, 2) : (decimal)5;
                                    if (m.IsAssisted)
                                    {
                                        weight = SaveSetPage.RoundDownToNearestIncrement(lstWeight + lstWeight * ((decimal)0.1), inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb, true);
                                    }
                                    else  
                                        weight = RecoComputation.RoundToNearestIncrementPyramid( lstWeight - lstWeight * ((decimal)0.1), inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb);


                                    if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb) >= SaveSetPage.RoundDownToNearestIncrement(setList.Last().Weight.Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb)  && !m.IsAssisted)
                                    {

                                        weight = RecoComputation.RoundToNearestIncrementPyramid(setList.Last().Weight.Lb - (m.RecoModel.Increments != null ? Math.Round(m.RecoModel.Increments.Lb, 2) : (setList.Last().Weight.Lb * ((decimal)0.1))), inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb);
                                        if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb) == SaveSetPage.RoundDownToNearestIncrement(rec.Weight.Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb))
                                        {
                                            rec.Reps = setList.Last().Reps; //
                                            isLowerWeightNotPossible = true;
                                        }
                                        //else
                                        //{
                                        //    updatingItem.Reps = reps;
                                        //}
                                        weight = new MultiUnityWeight(weight, "lb").Kg;
                                        if (weight == 0)
                                            weight = isKg ? 2 : 5;
                                        rec.Weight = new MultiUnityWeight(weight, isKg ? "kg" : "lb");
                                        if (rec.WeightDouble == setList.Last().WeightDouble)
                                        {
                                            rec.Reps = setList.Last().Reps;
                                            isLowerWeightNotPossible = true;
                                        }
                                    }
                                    else
                                    {
                                        if (m.IsAssisted)
                                        {
                                            rec.Weight = new MultiUnityWeight(SaveSetPage.RoundDownToNearestIncrement(weight, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb), "lb");
                                        } 
                                        else 
                                            rec.Weight = new MultiUnityWeight(SaveSetPage.RoundDownToNearestIncrementLb(weight, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb), "lb");    

                                    }

                                    if (rec.WeightDouble == setList.Last().WeightDouble)
                                    {
                                        rec.Reps = setList.Last().Reps;
                                        isLowerWeightNotPossible = true;
                                    }
                                }
                                if (weight <= 0)
                                {
                                    rec.Reps = setList.Last().Reps;
                                    weight = m.RecoModel.Increments != null ? m.RecoModel.Increments.Kg : (decimal)2;
                                    if (setList.Last().Weight.Kg > (decimal)1.15)
                                        rec.Reps = setList.Last().Reps + j + 1;
                                    rec.Weight = new MultiUnityWeight(weight, "kg");
                                    if (rec.WeightDouble == setList.Last().WeightDouble)
                                        rec.Reps = setList.Last().Reps;
                                    isLowerWeightNotPossible = true;
                                }

                            }
                            else
                            {
                                var lstkgWeight = Convert.ToDecimal(setList.Last().WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                                bool isWeighted = true;
                                var userWeight = isWeighted ? LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb : 0;
                                lstkgWeight += userWeight;
                                int? minKg = null;
                                int? minLb = null;
                                if (isWeighted)
                                {
                                    minKg = (int?)(m.RecoModel.Min == null ? userWeight : userWeight + m.RecoModel.Min.Kg);
                                    minLb = (int?)(m.RecoModel.Min == null ? userWeight : userWeight + m.RecoModel.Min.Lb);
                                }

                                decimal weight = RecoComputation.RoundToNearestIncrementPyramid(lstkgWeight - (lstkgWeight * ((decimal)0.1)), m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, minKg, m.RecoModel.Max?.Kg);

                                if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                                {
                                    var inc = m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg;
                                    if (SaveSetPage.RoundDownToNearestIncrement(weight, inc, minKg, m.RecoModel.Max?.Kg) >= SaveSetPage.RoundDownToNearestIncrement(userWeight + setList.Last().Weight.Kg, inc, minKg, m.RecoModel.Max?.Kg))
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(userWeight + setList.Last().Weight.Kg - ((userWeight + setList.Last().Weight.Kg) * (decimal)0.1), m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, minKg, m.RecoModel.Max?.Kg);
                                        if (weight == setList.Last().Weight.Kg)
                                        {
                                            rec.Reps = setList.Last().Reps;
                                            isLowerWeightNotPossible = true;
                                        }
                                        //else
                                        //    updatingItem.Reps = reps;
                                    }
                                    //else
                                    //updatingItem.Reps = reps;
                                    rec.Weight = new MultiUnityWeight(weight - userWeight, "kg");
                                    if (rec.WeightDouble == setList.Last().WeightDouble)
                                    {
                                        rec.Reps = setList.Last().Reps;
                                        isLowerWeightNotPossible = true;
                                    }
                                }
                                else
                                {
                                    var lstWeight = Convert.ToDecimal(setList.Last().WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture) + userWeight;
                                    var inc = m.RecoModel.Increments != null ? Math.Round(m.RecoModel.Increments.Lb, 2) : (decimal)5;
                                    weight = RecoComputation.RoundToNearestIncrementPyramid(lstWeight - lstWeight * ((decimal)0.1), inc, minLb, m.RecoModel.Max?.Lb);


                                    if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb) >= SaveSetPage.RoundDownToNearestIncrement(setList.Last().Weight.Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb))
                                    {

                                        weight = RecoComputation.RoundToNearestIncrementPyramid((userWeight + setList.Last().Weight.Lb) - ((userWeight + setList.Last().Weight.Lb) * ((decimal)0.1)), inc, minLb, m.RecoModel.Max?.Lb) - userWeight;
                                        if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "lb").Lb + userWeight, inc, minLb, m.RecoModel.Max?.Lb) == SaveSetPage.RoundDownToNearestIncrement(rec.Weight.Lb + userWeight, inc, minLb, m.RecoModel.Max?.Lb))
                                        {
                                            rec.Reps = setList.Last().Reps; //
                                            isLowerWeightNotPossible = true;
                                        }
                                        //else
                                        //{
                                        //    updatingItem.Reps = reps;
                                        //}
                                        weight = new MultiUnityWeight(weight, "lb").Kg;

                                        if (weight == 0)
                                        {
                                            weight = isWeighted ? 0 : isKg ? 2 : 5;
                                            rec.Weight = new MultiUnityWeight(weight, isKg ? "kg" : "lb");
                                        }
                                        else
                                            rec.Weight = new MultiUnityWeight(weight, "kg");
                                        if (rec.WeightDouble == setList.Last().WeightDouble)
                                        {
                                            rec.Reps = setList.Last().Reps;
                                            isLowerWeightNotPossible = true;
                                        }
                                    }
                                    else
                                        rec.Weight = new MultiUnityWeight(SaveSetPage.RoundDownToNearestIncrementLb(weight, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb), "lb");

                                    if (rec.WeightDouble == setList.Last().WeightDouble)
                                    {
                                        rec.Reps = setList.Last().Reps;
                                        isLowerWeightNotPossible = true;
                                    }
                                }
                                if (weight <= 0 && !isWeighted)
                                {

                                    rec.Reps = setList.Last().Reps;
                                    weight = m.RecoModel.Increments != null ? m.RecoModel.Increments.Kg : (decimal)2; ;
                                    if (setList.Last().Weight.Kg > (decimal)1.15)
                                        rec.Reps = setList.Last().Reps + j + 1;
                                    rec.Weight = new MultiUnityWeight(weight, "kg");
                                    if (rec.WeightDouble == setList.Last().WeightDouble)
                                        rec.Reps = setList.Last().Reps;
                                    isLowerWeightNotPossible = true;
                                }
                            }
                        }

                        if (setList.Count == 0)
                        {
                            rec.IsHeaderCell = true;
                            rec.ShowWorkTimer = m.IsTimeBased ? true : false;
                            //rec.HeaderImage = iconOrange;
                            rec.HeaderTitle = lbl3text;
                            if (!string.IsNullOrEmpty(m.LocalVideo))
                                rec.VideoUrl = m.LocalVideo;
                        }
                        if (!rec.IsFirstWorkSet)
                        {
                            if (m.RecoModel.HistorySet != null)
                            {
                                var workcount = m.RecoModel.HistorySet.Where(x => x.IsWarmups == false).ToList();
                                if (workcount.Count() > j)
                                {
                                    var r = workcount[j];
                                    var WeightText = m.IsBodyweight ? "body" : $"{string.Format("{0:0.#}", Math.Round((isKg ? r.Weight.Kg : r.Weight.Lb), 2))} {(isKg ? "kg" : "lbs")}";
                                    if(m.IsAssisted)
                                        WeightText = string.Format("{0:0.##} {1}", Math.Round(isKg ? _userBodyWeight - r.Weight.Kg : new MultiUnityWeight(_userBodyWeight, WeightUnities.kg).Lb - (r.Weight.Lb), 2), isKg ? "kg" : "lbs");

                                    if (m.Id == 16508)
                                    {
                                        WeightText = string.Format("{0:0.##}", m.RecoModel.Speed);// rec.IsWarmups ? "brisk" : "fast";
                                    }
                                    else if (m.BodyPartId == 12)
                                    {
                                        WeightText = string.Format("{0:0.##}", m.RecoModel.Speed);// rec.IsWarmups ? "brisk" : "cooldown";
                                    }
                                    if (m.Id >= 16897 && m.Id <= 16907 || m.Id == 14279 || m.Id >= 21508 && m.Id <= 21514)
                                    {
                                        var band = Utility.HelperClass.GetBandsColor((double)Math.Round((isKg ? r.Weight.Kg : r.Weight.Lb), 2), isKg);
                                        if (!string.IsNullOrEmpty(band))
                                            WeightText = band.ToLowerInvariant();
                                    }
                                    if (!r.IsWarmups)
                                    {
                                        rec.LastTimeSet = $"Last time: {(m.BodyPartId == 12 ? TimeSpan.FromSeconds(r.Reps).ToString(@"mm\:ss") : r.Reps.ToString())} x {WeightText }";
                                    }
                                }
                            }
                        }
                        setList.Add(rec);
                    }

                    for (int j = 0; j < m.RecoModel.NbPauses; j++)
                    {
                        var AssistedWeight = new MultiUnityWeight(_userBodyWeight - m.RecoModel.Weight.Kg, WeightUnities.kg);
                        if (AssistedWeight.Kg < 0)
                            AssistedWeight = new MultiUnityWeight(0, WeightUnities.kg);
                        var rec = new WorkoutLogSerieModelRef()
                        {
                            Id = m.Id,
                            Weight = m.IsAssisted ? AssistedWeight : m.RecoModel.isPlateAvailable || m.RecoModel.isPulleyAvailable || m.RecoModel.isDumbbellAvailable || m.RecoModel.isBandsAvailable? m.RecoModel.Weight : m.RecoModel.Weight.Kg < 1 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)1 * (m.RecoModel.Increments == null ? 1 : m.RecoModel.Increments.Kg), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg), "kg") : m.RecoModel.Weight,
                            IsPlateAvailable = m.RecoModel.isPlateAvailable,
                            IsDumbbellAvailable = m.RecoModel.isDumbbellAvailable,
                            IsBandsAvailable = m.RecoModel.isBandsAvailable,
                            IsPulleyAvailable = m.RecoModel.isPulleyAvailable,
                            IsWarmups = false,
                            Reps = m.RecoModel.NbRepsPauses,
                            PreviousReps = m.RecoModel.NbRepsPauses,
                            SetNo = $"{setList.Where(x=>x.IsWarmups == false).Count() + 1}",
                            ExerciseName = m.Label,
                            EquipmentId = m.EquipmentId,
                            IsFlexibility = m.IsFlexibility,
                            Increments = m.RecoModel.Increments,
                            SetTitle = "",
                            IsTimeBased = m.IsTimeBased,
                            IsUnilateral = m.IsUnilateral,
                            IsBodyweight = m.IsBodyweight,
                            IsNormalset = m.IsNormalSets,
                            IsFirstSide = m.IsFirstSide,
                            BodypartId = m.BodyPartId,
                            LastTimeSet = "",
                            IsOneHanded = m.IsOneHanded,
                            IsAssisted = m.IsAssisted,
                            Speed = m.RecoModel.Speed,
                            ShowWorkTimer = m.IsTimeBased ? true : false,

                        };
                        if (!isMarkFirstSet && j == 0)
                        {
                            rec.IsFirstWorkSet = true;
                            rec.SetTitle = "1st work set";
                            rec.IsMaxChallenge = IsMaxChallenge;
                        }
                        else if (m.RecoModel.IsNormalSets == false && m.RecoModel.IsPyramid == false && m.RecoModel.IsReversePyramid == false && rec.IsBackOffSet == false)
                        {
                            rec.SetTitle = "Rest-pause set";
                            rec.HeaderImage = "orange.png";
                        }

                        if (!isMarkFirstSet && j == 0 && m.RecoModel.FirstWorkSetWeight != null)
                        {

                            // var worksets = string.Format("{0} {1}", Math.Round(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : m.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                            // var days = 0;
                            // if (m.RecoModel.LastLogDate != null)
                            //     days = (DateTime.Now - ((DateTime)m.RecoModel.LastLogDate).ToLocalTime()).Days;
                            // var dayString = days == 0 ? "Today" : days == 1 ? "1 day ago" : $"{days} days ago";
                            // if (m.RecoModel.IsBodyweight)
                            //     worksets = "body";
                            //
                            // var lastOneRM = m.RecoModel.FirstWorkSetWeight.Kg;//
                            // lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : m.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg + (m.IsWeighted ? _userBodyWeight : 0), m.RecoModel.FirstWorkSetReps);
                            //
                            //
                            //
                            //
                            //
                            // decimal weight = 0;
                            //
                            // if (m.IsBodyweight)
                            //     weight = isKg ? m.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(m.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture);
                            // else
                            // {
                            //     weight = Convert.ToDecimal(rec.WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            // }
                            //
                            //
                            // var newWeight = Math.Round(new MultiUnityWeight(weight, isKg ? "kg" : "lb").Kg + (m.IsWeighted ? _userBodyWeight : 0), 2);
                            // if (m.IsBodyweight)
                            //     newWeight = new MultiUnityWeight(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : Convert.ToDecimal(m.RecoModel.FirstWorkSetWeight.Lb, CultureInfo.InvariantCulture), isKg ? "kg" : "lb").Kg;
                            //
                            // var currentRM = TruncateDecimal(ComputeOneRM(newWeight, m.RecoModel.Reps), 2);
                            // lastOneRM = Math.Round(isKg ? new MultiUnityWeight(lastOneRM, "kg").Kg : new MultiUnityWeight(lastOneRM, "kg").Lb, 1);
                            // currentRM = Math.Round(isKg ? new MultiUnityWeight(currentRM, "kg").Kg : new MultiUnityWeight(currentRM, "kg").Lb, 1);
                            //
                            // if (currentRM != 0)
                            // {
                            //     var percentage = (currentRM - lastOneRM) * 100 / lastOneRM;
                            //     rec.LastTimeSet = string.Format("Last time: {0} x {1}", m.RecoModel.FirstWorkSetReps, worksets);
                            //     rec.SetTitle = string.Format("Today: {0}{1:0.0}%", percentage >= 0 ? "+" : "", percentage);
                            // }
                            SetCoachTipTitle(rec, m);
                        }
                        if (m.RecoModel.IsReversePyramid)
                        {
                            ////TODO: Reverse Pyramid

                            rec.Reps = j == 0 ? m.RecoModel.Reps : (int)Math.Ceiling(setList.First(x => x.IsWarmups == false).Reps + (setList.First(x => x.IsWarmups == false).Reps * 0.3)); //before was 0.4

                            if (j > 0)
                            {
                                var first = setList.First(x => x.IsWarmups == false);
                                decimal weight = RecoComputation.RoundToNearestIncrementPyramid(rec.Weight.Kg - (rec.Weight.Kg * ((decimal)j * (m.RecoModel.Increments == null ? (decimal)0.075 : (decimal)0.075))), m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                if (m.IsAssisted)
                                {
                                    weight = SaveSetPage.RoundDownToNearestIncrement(
                                        rec.Weight.Kg + (rec.Weight.Kg * ((decimal)j *  (decimal)0.075)),
                                        m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg,
                                        m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg, true);
                                    weight =  RecoComputation.RoundToNearestIncrement(weight, m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                }

                                if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                                {
                                    if (weight >= setList.First(x => x.IsWarmups == false).Weight.Kg  && !m.IsAssisted)
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(setList.First(x => x.IsWarmups == false).Weight.Kg - (m.RecoModel.Increments != null ? m.RecoModel.Increments.Kg : (rec.Weight.Kg * ((decimal)j * (decimal)0.1))), m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                    }
                                    rec.Weight = new MultiUnityWeight(weight, "kg");
                                    if (rec.Reps < 1)
                                        rec.Reps = 1;
                                }
                                else
                                {
                                    var inc = rec.Increments != null ? rec.Increments.Lb : (decimal)5;
                                    if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "kg").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb) >= SaveSetPage.RoundDownToNearestIncrement(setList.First(x => x.IsWarmups == false).Weight.Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb)  && !m.IsAssisted)
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(setList.First(x => x.IsWarmups == false).Weight.Kg - (m.RecoModel.Increments != null ? m.RecoModel.Increments.Kg : (rec.Weight.Kg * ((decimal)j * (decimal)0.1))), m.RecoModel.Increments == null ? (decimal)2 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                        if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "kg").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb) == SaveSetPage.RoundDownToNearestIncrement(setList.First(x => x.IsWarmups == false).Weight.Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb))
                                        {
                                            rec.Reps = setList.Last().Reps;
                                            isLowerWeightNotPossible = true;
                                        }
                                    }
                                    rec.Weight = new MultiUnityWeight(SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "kg").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb), "lb");
                                }
                                if (weight == 0)
                                {
                                    //rec.Reps = setList.First(x => x.IsWarmups == false).Reps;
                                    weight = rec.Increments != null ? rec.Increments.Kg : (decimal)2;

                                    rec.Weight = new MultiUnityWeight(weight, "kg");
                                }
                                if (first.WeightDouble == rec.WeightDouble)
                                {
                                    rec.Reps = first.Reps;
                                    isLowerWeightNotPossible = true;
                                }

                                if (rec.Reps < 1)
                                    rec.Reps = 1;
                            }

                            ////TODO: Reverse Pyramid

                        }
                        else
                        {
                            if (j > 0 && m.RecoModel.IsPyramid)
                            {
                                rec.Reps = setList.Last().Reps + j + 1;
                                decimal weight = RecoComputation.RoundToNearestIncrementPyramid(rec.Weight.Kg - (rec.Weight.Kg * ((decimal)j * (m.RecoModel.Increments == null ? (decimal)0.1 : (decimal)0.1))), m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                                {
                                    if (weight >= setList.Last().Weight.Kg)
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(setList.Last().Weight.Kg - (m.RecoModel.Increments != null ? m.RecoModel.Increments.Kg : (rec.Weight.Kg * ((decimal)j * (decimal)0.1))), m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                        if (weight == setList.Last().Weight.Kg)
                                        {
                                            rec.Reps = setList.Last().Reps;
                                            isLowerWeightNotPossible = true;
                                        }
                                    }
                                    rec.Weight = new MultiUnityWeight(weight, "kg");
                                }
                                else
                                {
                                    var inc = rec.Increments != null ? rec.Increments.Lb : (decimal)5;
                                    if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "kg").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb) >= SaveSetPage.RoundDownToNearestIncrement(setList.Last().Weight.Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb))
                                    {
                                        weight = RecoComputation.RoundToNearestIncrementPyramid(setList.Last().Weight.Kg - (m.RecoModel.Increments != null ? m.RecoModel.Increments.Kg : (rec.Weight.Kg * ((decimal)j * (decimal)0.1))), m.RecoModel.Increments == null ? (decimal)2 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                        if (SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "kg").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb) == SaveSetPage.RoundDownToNearestIncrement(setList.Last().Weight.Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb))
                                        {
                                            rec.Reps = setList.Last().Reps;
                                            isLowerWeightNotPossible = true;
                                        }
                                    }
                                    rec.Weight = new MultiUnityWeight(SaveSetPage.RoundDownToNearestIncrement(new MultiUnityWeight(weight, "kg").Lb, inc, m.RecoModel.Min?.Lb, m.RecoModel.Max?.Lb), "lb");
                                }
                                if (weight <= 0)
                                {
                                    rec.Reps = setList.Last().Reps;
                                    weight = rec.Increments != null ? rec.Increments.Kg : (decimal)1; ; ;
                                    if (setList.Last().Weight.Kg > (decimal)1.15)
                                        rec.Reps = setList.Last().Reps + j + 1;
                                    rec.Weight = new MultiUnityWeight(weight, "kg");
                                }
                            }
                        }
                        if (setList.Count == 0)
                        {
                            rec.IsHeaderCell = true;
                            rec.ShowWorkTimer = m.IsTimeBased ? true : false;
                            //rec.HeaderImage = iconOrange;
                            rec.HeaderTitle = lbl3text;
                            if (!string.IsNullOrEmpty(m.LocalVideo) && !m.RecoModel.IsReversePyramid)
                                rec.VideoUrl = m.LocalVideo;
                        }
                        if (!rec.IsFirstWorkSet)
                        {
                            if (m.RecoModel.HistorySet != null)
                            {
                                var workcount = m.RecoModel.HistorySet.Where(x => x.IsWarmups == false).ToList();
                                if (workcount.Count() > j + 1)
                                {
                                    var r = workcount[j + 1];
                                    var WeightText = m.IsBodyweight ? "body" : $"{string.Format("{0:0.#}", Math.Round((isKg ? r.Weight.Kg : r.Weight.Lb), 2))} {(isKg ? "kg" : "lbs")}";
                                    if (m.IsAssisted)
                                        WeightText = string.Format("{0:0.##} {1}", Math.Round(isKg ? _userBodyWeight - r.Weight.Kg : new MultiUnityWeight(_userBodyWeight, WeightUnities.kg).Lb - (r.Weight.Lb), 2), isKg ? "kg" : "lbs");


                                    if (m.Id == 16508)
                                    {
                                        WeightText = string.Format("{0:0.##}", m.RecoModel.Speed); //rec.IsWarmups ? "brisk" : "fast";
                                    }
                                    else if (m.BodyPartId == 12)
                                    {
                                        WeightText = string.Format("{0:0.##}", m.RecoModel.Speed);// rec.IsWarmups ? "brisk" : "cooldown";

                                    }
                                    if (m.Id >= 16897 && m.Id <= 16907 || m.Id == 14279 || m.Id >= 21508 && m.Id <= 21514)
                                    {
                                        var band = Utility.HelperClass.GetBandsColor((double)Math.Round((isKg ? r.Weight.Kg : r.Weight.Lb), 2), isKg);
                                        if (!string.IsNullOrEmpty(band))
                                            WeightText = band.ToLowerInvariant();
                                    }
                                    if (!r.IsWarmups)
                                    {
                                        rec.LastTimeSet = $"Last time: {(m.BodyPartId == 12 ? TimeSpan.FromSeconds(r.Reps).ToString(@"mm\:ss") : r.Reps.ToString())} x {WeightText}";
                                    }
                                }
                            }
                        }
                        if (m.RecoModel.IsReversePyramid)
                            setList.Insert(setList.Count(x => x.IsWarmups), rec);
                        else
                            setList.Add(rec);
                    }
                    if (m.RecoModel.IsReversePyramid && setList.Count(x => x.IsWarmups) == 0)
                    {
                        if (!string.IsNullOrEmpty(m.LocalVideo) && setList.FirstOrDefault() != null)
                            setList.First().VideoUrl = m.LocalVideo;
                        setList.Last().IsHeaderCell = false;
                        setList.First().IsHeaderCell = true;
                        //setList.First().HeaderImage = iconOrange;
                        setList.First().HeaderTitle = lbl3text;
                    }
                    var worksetcount = (setList.Count - m.RecoModel.WarmUpsList.Count);
                    if (worksetcount > 2 && !m.IsBodyweight)
                    {
                        if (m.RecoModel.BackOffSetWeight != null && !m.RecoModel.IsPyramid)
                        {
                            decimal wei = Convert.ToDecimal(setList.Last().WeightDouble.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            var inc = isKg ? m.RecoModel.Increments == null ? (decimal)2.0 : m.RecoModel.Increments.Kg : m.RecoModel.Increments == null ? (decimal)5.0 : Math.Round(m.RecoModel.Increments.Lb);
                            wei = m.IsWeighted ? wei + (isKg ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb) : wei;
                            m.RecoModel.BackOffSetWeight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)wei - wei * (decimal)0.3, inc, isKg ? m.RecoModel.Min?.Kg : m.RecoModel.Min?.Lb, isKg ? m.RecoModel.Max?.Kg : m.RecoModel.Max?.Lb) - (m.IsWeighted ? (isKg ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb) : 0), isKg ? "kg" : "lb");
                            if (m.RecoModel.isPlateAvailable)
                            {
                                m.RecoModel.BackOffSetWeight = RecoComputation.GetPlatesWeight(keyVal, isKg ? m.RecoModel.Weight.Kg - wei * (decimal)0.3 : m.RecoModel.Weight.Lb - wei * (decimal)0.3, isKg ? 20 : 45, isKg);
                            }
                            if (m.RecoModel.isPulleyAvailable)
                            {
                                m.RecoModel.BackOffSetWeight = RecoComputation.GetPlatesWeight(keyVal, isKg ? m.RecoModel.Weight.Kg - wei * (decimal)0.3 : m.RecoModel.Weight.Lb - wei * (decimal)0.3, 0, isKg);
                            }
                            if (m.RecoModel.isDumbbellAvailable)
                            {
                                m.RecoModel.BackOffSetWeight = RecoComputation.GetDumbbellWeight(keyVal, isKg ? m.RecoModel.Weight.Kg - wei * (decimal)0.3 : m.RecoModel.Weight.Lb - wei * (decimal)0.3, isKg);
                            }
                            if (m.RecoModel.isBandsAvailable)
                            {
                                m.RecoModel.BackOffSetWeight = RecoComputation.GetBandsWeight(keyVal, isKg ? m.RecoModel.Weight.Kg - wei * (decimal)0.3 : m.RecoModel.Weight.Lb - wei * (decimal)0.3, isKg);
                            }

                            if (m.IsWeighted)
                            {
                                m.RecoModel.BackOffSetWeight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(isKg ? m.RecoModel.BackOffSetWeight.Kg : m.RecoModel.BackOffSetWeight.Lb, inc, isKg ? m.RecoModel.Min?.Kg : m.RecoModel.Min?.Lb, isKg ? m.RecoModel.Max?.Kg : m.RecoModel.Max?.Lb), isKg ? "kg" : "lb");
                            }
                            if (m.IsAssisted)
                            {
                                /*Computations: userbodyweight - last set weight 195-40= 145 applied 30 percent = 145-30% = 100 
                            195-100 = 100
                            */
                                wei =
                                    _userBodyWeight - (isKg? wei : new MultiUnityWeight(wei, "lb").Kg);
                                wei = _userBodyWeight - ( (decimal)wei - wei * (decimal)0.3);
                                if (wei<0)
                                    wei = 0;
                                wei = isKg? wei : new MultiUnityWeight(wei, "kg").Lb;
                                m.RecoModel.BackOffSetWeight = new MultiUnityWeight(RecoComputation.RoundToNearestIncrement(wei, inc, isKg ? m.RecoModel.Min?.Kg : m.RecoModel.Min?.Lb, isKg ? m.RecoModel.Max?.Kg : m.RecoModel.Max?.Lb) - (m.IsWeighted ? (isKg ? _userBodyWeight : new MultiUnityWeight(_userBodyWeight, "kg").Lb) : 0), isKg ? "kg" : "lb");
                            }
                            setList.Last().Weight = m.RecoModel.BackOffSetWeight.Kg < 2 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)2 * inc, inc, isKg ? m.RecoModel.Min?.Kg : m.RecoModel.Min?.Lb, isKg ? m.RecoModel.Max?.Kg : m.RecoModel.Max?.Lb), isKg ? "kg" : "lb") : m.RecoModel.BackOffSetWeight;

                            if (m.RecoModel.BackOffSetWeight.Kg <= 0 && m.IsWeighted)
                                //if (m.IsWeighted)
                                //    m.RecoModel.BackOffSetWeight = new MultiUnityWeight(0, "kg");
                                setList.Last().Weight = new MultiUnityWeight(0, "kg");
                            
                            if (Math.Abs(m.RecoModel.Weight.Kg - m.RecoModel.BackOffSetWeight.Kg) > 0)
                            {
                                var ob = ((Math.Abs(m.RecoModel.Weight.Kg - m.RecoModel.BackOffSetWeight.Kg) / (m.RecoModel.Weight.Kg == 0 ? 2 : m.RecoModel.Weight.Kg)) > (decimal)0.3 ? (decimal)0.7 : Math.Abs(m.RecoModel.Weight.Kg - m.RecoModel.BackOffSetWeight.Kg) / (m.RecoModel.Weight.Kg == 0 ? 2 : m.RecoModel.Weight.Kg) * (decimal)0.7/ (decimal)0.3);
                                if (m.IsWeighted)
                                {
                                    var weightChanged = (m.RecoModel.Weight.Kg + _userBodyWeight - m.RecoModel.BackOffSetWeight.Kg);
                                    if (weightChanged < 0)
                                        weightChanged = 0;
                                    if ((Math.Abs((m.RecoModel.Weight.Kg + _userBodyWeight) - (m.RecoModel.BackOffSetWeight.Kg + _userBodyWeight)) / (m.RecoModel.Weight.Kg + _userBodyWeight)) > (decimal)0.3)
                                    {
                                        ob = (decimal)0.7;
                                    }
                                    else
                                    {
                                        ob = Math.Abs((m.RecoModel.Weight.Kg + _userBodyWeight) - (m.RecoModel.BackOffSetWeight.Kg + _userBodyWeight)) / (m.RecoModel.Weight.Kg + _userBodyWeight) * (decimal)0.7/(decimal)0.3;
                                        
                                    }
                                    var newWeight = weightChanged - (weightChanged * (decimal)0.3);
                                    if (newWeight - _userBodyWeight > 0)
                                        ob = (decimal)0.7;
                                    else
                                    {
                                        var percentChange = m.RecoModel.Weight.Kg / (m.RecoModel.Weight.Kg + _userBodyWeight);
                                        if (percentChange > (decimal)0.3)
                                            percentChange = (decimal)0.3;
                                        ob = percentChange * (decimal)0.7 / (decimal)0.3;
                                    }
                                        
                                }
                                

                                setList.Last().Reps = (int)setList.Last().Reps + (int)Math.Ceiling(setList.Last().Reps * ob);
                            }
                            else
                            {
                                setList.Last().Reps = (int)(setList.Last().Reps + Math.Ceiling(setList.Last().Reps * 0.2));
                            }
                            
                            setList.Last().IsBackOffSet = true;
                            setList[setList.Count - 2].IsNextBackOffSet = true;
                        }
                    }
                    if (worksetcount > 3)
                    {
                        setList[setList.Count - 2].SetTitle = "Almost done—keep it up!";
                        setList.Last().SetTitle = "Last set—you can do this!";
                    }
                    else if (worksetcount > 2)
                    {
                        setList.Last().SetTitle = "Last set—you can do this!";
                    }
                    if (setList.First().IsWarmups)
                    {
                        var warmString = setList.Where(l => l.IsWarmups).ToList().Count < 2 ? "warm-up" : "warm-ups";
                        setList.First().SetTitle = $"{setList.Where(l => l.IsWarmups).ToList().Count} {warmString}, {setList.Where(l => !l.IsWarmups).ToList().Count} work sets";
                    }
                    var selected = setList.Where(x => x.IsNext == true).FirstOrDefault();

                    if (selected == null && setList.Count > 0)
                    {
                        setList.First().IsNext = true;
                        MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NewSet, SetModel = setList.First(), Label = m.Label }, "SendWatchMessage");
                    }
                    else
                    {
                        //Get index and set
                    }
                    if (setList.Count > 0)
                    {
                        setList.Last().IsLastSet = true;
                        if (m.IsFirstSide)
                            setList.Last().IsFirstSide = true;
                    }
                    SetIndex(setList);
                    foreach (var item in setList)
                    {
                        m.Add(item);
                    }
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                    {
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[m.Id] = new ObservableCollection<WorkoutLogSerieModelRef>(setList);
                    }
                    else
                    {
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, new ObservableCollection<WorkoutLogSerieModelRef>(setList));
                    }
                    //lblResult4.Text = string.Format("Do {0} {1} for {2} sets of {3} reps ({4} rest)", WeightRecommandation, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", reco.Series, reco.Reps, restTime);

                    if (m.RecoModel.IsDeload)
                    {
                        LocalDBManager.Instance.SetDBSetting("RecoDeload", "true");
                    }
                    else
                        LocalDBManager.Instance.SetDBSetting("RecoDeload", "false");
                    Device.BeginInvokeOnMainThread(async() =>
                    {
                        if (setList.Count > 0)
                        {
                            if (Device.RuntimePlatform.Equals(Device.iOS))
                                ExerciseListView.ScrollTo(setList.First(), ScrollToPosition.Start, true);
                            await Task.Delay(300);
                            int position = 0;
                            foreach (var itemGood in exerciseItems)
                            {
                                if (itemGood == m)
                                    break;
                                position += 1;
                                position += itemGood.Count;
                            }
                            ExerciseListView.ItemPosition = position;
                            ExerciseListView.IsScrolled = !ExerciseListView.IsScrolled;
                        }
                    });
                    if (ShowWarmups && !Config.ShowWarmups && m.RecoModel.WarmUpsList.Count > 0)
                    {
                        ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                        {
                            Title = $"Warm up",
                            Message = "Repeat a few times—slow and steady.",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = AppResources.GotIt,
                            CancelText = AppResources.RemindMe,

                        };
                        var x = await UserDialogs.Instance.ConfirmAsync(ShowRIRPopUp);
                        if (x)
                        {
                            Config.ShowWarmups = true;
                        }
                    }
                    else if (ShowWarmups && m.RecoModel.WarmUpsList.Count == 0)
                    {
                        AlertConfig challengeConfig = new AlertConfig()
                        {
                            Title = m.IsBodyweight ? "Test your starting reps" : "Test your starting weight",
                            Message = $"Repeat as many times as you can with good form. Stop when it gets hard. Enter how many you did.",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Got it",
                        };
                        await UserDialogs.Instance.AlertAsync(challengeConfig);
                    }
                    //if (isLowerWeightNotPossible && (m.IsPyramid || m.IsReversePyramid) && !m.IsWeighted)
                    //{
                    //    var msg = "";
                    //    if (m.RecoModel.Min != null)
                    //    {
                    //        var incr = string.Format("{0:0.#}", isKg ? Math.Round(m.RecoModel.Min.Kg, 2) : Math.Round(m.RecoModel.Min.Lb, 2));

                    //        msg = $"You have a min weight of {(isKg ? incr + " kg" : incr + " lbs")}, so you cannot do {(m.IsReversePyramid ? "pyramid" : "reverse pyramid")} sets. Edit weights or try rest-pause sets today.";
                    //    }
                    //    else
                    //    {
                    //        msg = $"Your weights cannot go below {(isKg ? "2 kg" : "5 lbs")}, so you cannot do {(m.IsReversePyramid ? "pyramid" : "reverse pyramid")} sets. Edit weights or try rest-pause sets today.";
                    //    }
                    //    ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                    //    {
                    //        Title = $"Cannot lower weights",
                    //        Message = msg,
                    //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //        OkText = "Try rest-pause",
                    //        CancelText = "Edit weights",

                    //    };
                    //    var x = await UserDialogs.Instance.ConfirmAsync(ShowRIRPopUp);
                    //    if (!x)
                    //    {
                    //        CurrentLog.Instance.AutoEnableIncrements = true;
                    //        CurrentLog.Instance.WorkoutTemplateCurrentExercise = GetExerciseModel(m);
                    //        if (!string.IsNullOrEmpty(m.VideoUrl) || m.IsSystemExercise || CurrentLog.Instance.CurrentWorkoutTemplate.UserId == "89c52f09-240c-40a8-96df-9e8e152b7d63")
                    //            await PagesFactory.PushAsync<ExerciseSettingsPage>();
                    //        else
                    //            await PagesFactory.PushAsync<ExerciseCustomSettingsPage>();
                    //        IsSettingsChanged = true;
                    //        return;
                    //    }
                    //    else
                    //    {
                    //        m.RecoModel.IsReversePyramid = false;
                    //        m.RecoModel.IsPyramid = false;
                    //        m.IsPyramid = m.IsReversePyramid = false;
                    //        if (m.RecoModel.Series == 0)
                    //        {
                    //            m.RecoModel.Series = 1;
                    //            m.RecoModel.NbPauses = m.RecoModel.NbPauses - 1;
                    //            m.RecoModel.NbRepsPauses = m.RecoModel.Reps / 3;
                    //        }
                    //        else
                    //        {
                    //            var left = m.RecoModel.Series - 1;
                    //            m.RecoModel.Series = 1;
                    //            m.RecoModel.NbPauses = left;
                    //            m.RecoModel.NbRepsPauses = m.RecoModel.Reps / 3;
                    //        }
                    //        RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                    //        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + exId + setStyle, DateTime.Now.AddDays(1).ToString());
                    //        m.Clear();
                    //        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                    //            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                    //        FetchReco(m);
                    //        return;
                    //    }
                    //}
                    //Reps outside your range
                    if (m.RecoModel.Reps < m.RecoModel.MinReps || m.RecoModel.Reps > m.RecoModel.MaxReps)
                    {
                        if (!CurrentLog.Instance.IsRepsOutsidePopup && !m.IsBodyweight)
                        {
                            setList.Where(x => x.IsFirstWorkSet == true).FirstOrDefault().HeaderImage = "orange.png";
                        }
                    }
                    var firstWorkSet = setList.Where(x => x.IsFirstWorkSet == true).FirstOrDefault();
                    var lastWorkSet = setList.Where(x => x.IsLastSet == true && x.IsBackOffSet == true).FirstOrDefault();

                    if (firstWorkSet != null && (m.RecoModel.IsLightSession || firstWorkSet.IsMaxChallenge || m.RecoModel.IsMaxChallenge || m.RecoModel.IsDeload || isAddMoreSet))
                    {
                        firstWorkSet.HeaderImage = "orange.png";
                    }
                    if (firstWorkSet.SetTitle.Contains("1st work set") && m.RecoModel.IsDeload)
                    {
                        firstWorkSet.SetTitle = "Deload";
                    }
                    if (lastWorkSet != null)
                    {
                        lastWorkSet.HeaderImage = "orange.png";
                        lastWorkSet.SetTitle = "Back-off set";
                    }
                    if (m.IsFirstSide)
                    {
                        if (!m.IsPopup)
                        {
                            AlertConfig ShowExplainRIRPopUp = new AlertConfig()
                            {
                                Title = "Do all sets for side 1",
                                
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = "OK",

                            };
                            UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
                            m.IsPopup = true;
                        }
                    }
                }
            }
        }

        private void SetIndex(List<WorkoutLogSerieModel> setList)
        {
            var setCount = 0;
            for (var i = 0; i < setList.Count; i++)
            {
                if (setList[i].IsWarmups)
                    ((WorkoutLogSerieModelRef)setList[i]).SetNo = $"W";
                else
                {
                    ((WorkoutLogSerieModelRef)setList[i]).SetNo = $"{setCount + 1}";
                    setCount++;
                }
            }
        }
        private void SetIndex(List<WorkoutLogSerieModelRef> setList)
        {
            var setCount = 0;
            for (var i = 0; i < setList.Count; i++)
            {
                if (setList[i].IsWarmups)
                    ((WorkoutLogSerieModelRef)setList[i]).SetNo = $"W";
                else
                {
                    ((WorkoutLogSerieModelRef)setList[i]).SetNo = $"{setCount + 1}";
                    setCount++;
                }
            }
        }
        private async void Load1RM(long id)
        {
            //var _rm = await DrMuscleRestClient.Instance.GetOneRMForExerciseWithoutLoader(
            //    new GetOneRMforExerciseModel()
            //    {
            //        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
            //        Massunit = LocalDBManager.Instance.GetDBSetting("massunit").Value,
            //        ExerciseId = id
            //    });

        }
        private void OnBindingContextChanged(object sender, EventArgs e)
        {
            base.OnBindingContextChanged();
            try
            {

            ((ViewCell)sender).Height = 115;
                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;

                //var btnVideo = (DrMuscleButton)((StackLayout)((StackLayout)((StackLayout)((Frame)((Grid)((ViewCell)sender).View).Children[2]).Children[0]).Children[0]).Children[5]).Children[5];
                //if (string.IsNullOrEmpty(m.VideoUrl))
                //    btnVideo.IsVisible = false;
                //else
                //    btnVideo.IsVisible = true;
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                        ((ViewCell)sender).ForceUpdateSize();
                }
            }
            catch (Exception ex)
            {

            }
            //Image swapImage = (Image)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[0]).Children[2];
            //if (m.IsSwapTarget)
            //{
            //    swapImage.IsVisible = true;
            //}
            //else
            //{
            //    swapImage.IsVisible = false;
            //}
        }
        private async void OnDeload(object sender)
        {
            try
            {
                if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("DailyReset")?.Value))
                    LocalDBManager.Instance.SetDBSetting("DailyReset", "0");
                if (int.Parse(LocalDBManager.Instance.GetDBSetting("DailyReset")?.Value) > 0)
                {
                    CurrentLog.Instance.IsFreePlanPopup = true;
                    ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                    {
                        Message = "Upgrading will unlock deload.",
                        Title = "You discovered a premium feature!",
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = "Upgrade",
                        CancelText = "Maybe later",
                        OnAction = async (bool ok) =>
                        {
                            if (ok)
                            {
                                PagesFactory.PushAsync<SubscriptionPage>();
                            }
                            else
                            {

                            }
                        }
                    };
                    await Task.Delay(100);
                    UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
                    return;
                }
                ExerciseWorkSetsModel m = ((ExerciseWorkSetsModel)sender);

                //ConfirmConfig supersetConfig = new ConfirmConfig()
                //{
                //    Title = "Deload?",
                //    Message = "2 work sets and 5-10% less weight. Helps to recover.",
                //    OkText = "Deload",
                //    CancelText = AppResources.Cancel,
                //    OnAction = async (bool ok) =>
                //    {
                //        string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                //        string exId = $"{m.Id}";
                //        if (ok)
                //        {
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");
                //            m.RecoModel = null;
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                //            {
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                //                m.Clear();
                //                try
                //                {

                //                }
                //                catch (Exception ex)
                //                {

                //                }
                //            }
                //            FetchReco(m, null);
                //        }
                //        else
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                //    }
                //};
                //UserDialogs.Instance.Confirm(supersetConfig);

                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                string exId = $"{m.Id}";
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") == null || LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "")
                {
                    ConfirmConfig supersetConfig = new ConfirmConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = "Deload",
                        Message = m.IsBodyweight ? $"2 work sets and 15-20% fewer reps. Helps you recover. Deload?" : "2 work sets and 5-10% less weight. Helps you recover. Deload?",
                        OkText = "Deload",
                        CancelText = AppResources.Cancel,
                    };
                    var res = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
                    if (res)
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");
                    else
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"");
                }
                else
                {
                    m.RecoModel.IsDeload = false;
                    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"");
                }

                LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                {
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                    m.Clear();
                }
                try
                {

                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                        contextMenuStack.Children[2].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        contextMenuStack.Children[2].BackgroundColor = Color.FromHex("#ECFF92");
                }

                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#ECFF92");
                }

                }
                catch (Exception ex)
                {

                }
                //OnCancelClicked(sender, e);
                FetchReco(m, null);

            }
            catch (Exception ex)
            {

            }
        }

        private async void OnChallenge(object sender)
        {
            try
            {

                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)(sender);

                //ConfirmConfig supersetConfig = new ConfirmConfig()
                //{
                //    Title = "Feeling strong?",
                //    Message = "We'll shoot for 10% more reps. Be safe: stop before your form breaks down.",
                //    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    OkText = AppResources.Challenge,
                //    CancelText = AppResources.Cancel,
                //    OnAction = async (bool ok) =>
                //    {
                //        string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                //        string exId = $"{m.Id}";
                //        if (ok)
                //        {
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                //            m.RecoModel = null;
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                //            {
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                //                m.Clear();
                //            }
                //            FetchReco(m, null);
                //        }
                //        else
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                //    }
                //};
                //UserDialogs.Instance.Confirm(supersetConfig);

                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                string exId = $"{m.Id}";
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") == null || LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "")
                {
                    ConfirmConfig supersetConfig = new ConfirmConfig()
                    {
                        Title = $"Ready for a new record?",
                        Message = "Do as many reps as you can on your first work set. Stop before your form breaks down.",
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = AppResources.Challenge,
                        CancelText = AppResources.Cancel,

                    };
                    var res = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
                    if (res)
                    {
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                    }
                    else
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                }
                else
                    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");

                LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"");
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                {
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                    m.Clear();
                }
                try
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                    {
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                            contextMenuStack.Children[2].BackgroundColor = Color.FromHex("#72DF40");
                        else
                            contextMenuStack.Children[2].BackgroundColor = Color.FromHex("#ECFF92");
                    }

                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                    {
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                            contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#72DF40");
                        else
                            contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#ECFF92");
                    }
                }
                catch (Exception ex)
                {

                }
                
                FetchReco(m, null);

            }
            catch (Exception ex)
            {

            }
        }
        //
        private async void OnExerciseVideo(object sender)
        {
            //if (contextMenuStack != null)
            //    HideContextButton();
            CurrentLog.Instance.VideoExercise = GetExerciseModel((ExerciseWorkSetsModel)sender);
            if (Device.RuntimePlatform.Equals(Device.iOS))
                DependencyService.Get<IOrientationService>().Landscape();
            await PagesFactory.PushAsync<ExerciseVideoPage>(true);
            //OnCancelClicked(sender, e);
        }
        private async void OnVideo(object sender, System.EventArgs e)
        {
            //if (contextMenuStack != null)
            //    HideContextButton();
            CurrentLog.Instance.VideoExercise = GetExerciseModel(((ExerciseWorkSetsModel)((Button)sender).CommandParameter));
            if (Device.RuntimePlatform.Equals(Device.iOS))
                DependencyService.Get<IOrientationService>().Landscape();
            await PagesFactory.PushAsync<ExerciseVideoPage>(true);
            OnCancelClicked(sender, e);
        }
        //
        private async void OnContextVideo(object sender, System.EventArgs e)
        {
            //if (contextMenuStack != null)
            //    HideContextButton();
            CurrentLog.Instance.VideoExercise = GetExerciseModel(((ExerciseWorkSetsModel)((Button)sender).CommandParameter));
            if (Device.RuntimePlatform.Equals(Device.iOS))
                DependencyService.Get<IOrientationService>().Landscape();
            await PagesFactory.PushAsync<ExerciseVideoPage>(true);
        }
        private async void OnSwap(object sender, System.EventArgs e)
        {
            SwapExerciseContext context = new SwapExerciseContext();
            context.WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
            context.SourceExerciseId = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter).Id;
            context.SourceBodyPartId = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter).BodyPartId;
            ExerciseWorkSetsModel model = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter);
            context.Label = model.Label;
            context.IsBodyweight = model.IsBodyweight;
            context.IsSystemExercise = model.IsSystemExercise;
            context.IsEasy = model.IsEasy;
            context.VideoUrl = model.VideoUrl;
            context.BodyPartId = model.BodyPartId;
            CurrentLog.Instance.SwapContext = context;
            await PagesFactory.PushAsync<ChooseYourCustomExercisePage>();
            OnCancelClicked(sender, e);

        }

        private async void OnRestore(object sender, System.EventArgs e)
        {
            ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;
            SwapExerciseContext sec = ((App)Application.Current).SwapExericesContexts.Swaps.First(s => s.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && s.TargetExerciseId == m.Id);
            ((App)Application.Current).SwapExericesContexts.Swaps.Remove(sec);
            ((App)Application.Current).SwapExericesContexts.SaveContexts();
            OnCancelClicked(sender, e);
            await UpdateExerciseList();
        }

        public async void ResetExercisesAction(ExerciceModel model)
        {
            BooleanModel result = await DrMuscleRestClient.Instance.ResetExercise(model);
            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
        }

        async void onInjuryRehab(object sender)
        {
            ExerciseWorkSetsModel m = ((ExerciseWorkSetsModel)sender);

            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting($"InjuryWeight{m.Id}")?.Value))
            {
                LocalDBManager.Instance.SetDBSetting($"InjuryWeight{m.Id}", "");
                LocalDBManager.Instance.SetDBSetting($"InjuryReps{m.Id}", "");
                _injuryRehabWithExerciseId = 0;
                m.IsNextExercise = false;

                m.RecoModel = null;
                m.Clear();
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                try
                {
                    ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                    ((App)Application.Current).WorkoutLogContext.SaveContexts();
                }
                catch (Exception ex)
                {

                }

                CellHeaderTapped(new Label() { BindingContext = m }, null);
                return;
            }
                var config = new ConfirmConfig()
            {
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                Message = "Recalibrate for injury recovery. Medical supervision highly recommended.",
                Title = "Injury rehab",
                CancelText = "Learn more",
                OkText = AppResources.GotIt
            };
            var response = await UserDialogs.Instance.ConfirmAsync(config);
            if (response)
            {
                //set up flow
                _injuryRehabRunning = true;
                _injuryRehabWithExerciseId = m.Id;
                m.IsNextExercise = false;

                m.RecoModel = null;
                m.Clear();
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                try
                {
                    ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                    ((App)Application.Current).WorkoutLogContext.SaveContexts();
                }
                catch (Exception ex)
                {

                }

                CellHeaderTapped(new Label() { BindingContext = m }, null);
            }
            else
            {
                Device.OpenUri(new Uri("https://dr-muscle.com/bodybuilding-injuries/"));
            }
        }

        public async void OpenNotes(object sender)
        {
            //var mi = ((Button)sender);

            ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)sender;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = GetExerciseModel(m);
            if (m.IsSystemExercise)
                await PagesFactory.PushAsync<ExerciseSettingsPage>();
            else
                await PagesFactory.PushAsync<ExerciseCustomSettingsPage>();
            IsSettingsChanged = true;
            //OnCancelClicked(sender, e);
            //ConfirmConfig p = new ConfirmConfig()
            //{
            //    Title = AppResources.ResetExercise, 
            //    Message = AppResources.AreYouSureYouWantToResetThisExerciseAndDeleteAllItsHistoryThisCannotBeUndone,// string.Format("Are you sure you want to reset this exercise and delete all its history? This cannot be undone.", m.Label),
            //    OkText = AppResources.Reset,
            //    CancelText = AppResources.Cancel,
            //    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
            //};
            //p.OnAction = (obj) =>
            //{
            //    if (obj)
            //    {
            //        ResetExercisesAction(m);
            //        OnCancelClicked(sender, e);
            //    }
            //};
            //UserDialogs.Instance.Confirm(p);
        }

        public async void OnReset(object sender)
        {
           // var mi = ((Button)sender);

            ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)sender;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = GetExerciseModel(m);
            if (m.IsSystemExercise)
                await PagesFactory.PushAsync<ExerciseSettingsPage>();
            else
                await PagesFactory.PushAsync<ExerciseCustomSettingsPage>();
            IsSettingsChanged = true;
            //OnCancelClicked(sender, e);
            //ConfirmConfig p = new ConfirmConfig()
            //{
            //    Title = AppResources.ResetExercise, 
            //    Message = AppResources.AreYouSureYouWantToResetThisExerciseAndDeleteAllItsHistoryThisCannotBeUndone,// string.Format("Are you sure you want to reset this exercise and delete all its history? This cannot be undone.", m.Label),
            //    OkText = AppResources.Reset,
            //    CancelText = AppResources.Cancel,
            //    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
            //};
            //p.OnAction = (obj) =>
            //{
            //    if (obj)
            //    {
            //        ResetExercisesAction(m);
            //        OnCancelClicked(sender, e);
            //    }
            //};
            //UserDialogs.Instance.Confirm(p);
        }

        void HideContextButton()
        {
            try
            {
                
                StackLayout s1 = (StackLayout)contextMenuStack.Parent;
                s1.Children[1].IsVisible = true;
                s1.Children[2].IsVisible = true;

                ExerciseWorkSetsModel m = ((ExerciseWorkSetsModel)((Button)contextMenuStack.Children[5]).CommandParameter);
                contextMenuStack.Children[0].IsVisible = false;
                contextMenuStack.Children[1].IsVisible = false;
                contextMenuStack.Children[2].IsVisible = false;
                contextMenuStack.Children[3].IsVisible = false;
                contextMenuStack.Children[4].IsVisible = false;
                contextMenuStack.Children[5].IsVisible = !string.IsNullOrEmpty(m.VideoUrl); ;
                contextMenuStack.Children[6].IsVisible = true;
                contextMenuStack = null;
            }
            catch (Exception ex)
            {

            }
        }

        void OnCancelClicked(object sender, System.EventArgs e)
        {
            //StackLayout s = ((StackLayout)((Button)sender).Parent);
            //StackLayout s1 = (StackLayout)s.Parent;
            //s1.Children[1].IsVisible = true;
            //s1.Children[2].IsVisible = true;
            //s.Children[1].IsVisible = false;
            //s.Children[2].IsVisible = false;
            //s.Children[3].IsVisible = false;
            //s.Children[4].IsVisible = false;
            //s.Children[5].IsVisible = false;
            //s.Children[6].IsVisible = s.Children[0].IsVisible;
            //s.Children[0].IsVisible = false;
            //s.Children[7].IsVisible = true;

            StackLayout s = ((StackLayout)((Button)sender).Parent);
            ExerciseWorkSetsModel m = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter);

            StackLayout s1 = (StackLayout)s.Parent;
            s1.Children[1].IsVisible = true;
            s1.Children[2].IsVisible = true;

            s.Children[0].IsVisible = false;
            s.Children[1].IsVisible = false;
            s.Children[2].IsVisible = false;
            s.Children[3].IsVisible = false;
            s.Children[4].IsVisible = false;
            s.Children[5].IsVisible = !string.IsNullOrEmpty(m.VideoUrl);
            s.Children[6].IsVisible = true;
        }

        async void openHistory(object sender)
        {
            ExerciseWorkSetsModel m = ((ExerciseWorkSetsModel)sender);
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = GetExerciseModel(m);
            CurrentLog.Instance.ShowCurentExercise = true;
            await PagesFactory.PushAsync<History.HistoryPage>();

        }

        void OnContextMenuClicked(object sender, System.EventArgs e)
        {
            if (contextMenuStack != null)
                HideContextButton();
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            ExerciseWorkSetsModel m = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter);

            //StackLayout ss = (StackLayout)s.Parent;


            var noteItem = new MenuItem() { Text = "Notes", Command = new Command(OpenNotes), CommandParameter = m };
            var deloadItem = new MenuItem() { Text = "Deload", Command = new Command(OnDeload), CommandParameter = m, IsDestructive = true };
            var challengeItem = new MenuItem() { Text = "Challenge", Command = new Command(OnChallenge), CommandParameter = m };
            var injuryRehabItem = new MenuItem() { Text = "Injury rehab", Command = new Command(onInjuryRehab), CommandParameter = m };
            var historyItem = new MenuItem() { Text = "History", Command = new Command(openHistory), CommandParameter = m , IsDestructive = true};
            var videoItem = new MenuItem() { Text = "Video instructions", Command = new Command(OnExerciseVideo), CommandParameter = m };
            var settingsItem = new MenuItem() { Text = "Exercise settings", Command = new Command(OnReset), CommandParameter = m };

            string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
            string exId = $"{m.Id}";


            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
            {
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                    deloadItem.Text = "✓ Deload";
                else
                    deloadItem.Text = "Deload";
            }

            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
            {
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                    challengeItem.Text = "✓ Challenge";
                else
                    challengeItem.Text = "Challenge";
            }
            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting($"InjuryWeight{m.Id}")?.Value))
            {
                injuryRehabItem.Text = "✓ Injury rehab";
            }

            if (!string.IsNullOrEmpty(m.VideoUrl))
            {
                ((ContextMenuButton)s.Children[1]).ItemsContainerHeight = 349;//did -17
                ((ContextMenuButton)s.Children[1]).Items = new[] { noteItem, deloadItem, challengeItem, injuryRehabItem, historyItem, videoItem, settingsItem };
            }
            else
            {

                ((ContextMenuButton)s.Children[1]).ItemsContainerHeight = 301; //did -17
                ((ContextMenuButton)s.Children[1]).Items = new[] { noteItem, deloadItem, challengeItem, injuryRehabItem, historyItem, settingsItem };
            }
            ((ContextMenuButton)s.Children[1]).ContextMenuButton_Clicked(sender, e);
            return;


            if (m.IsNextExercise && m.Count > 0)
            {
                StackLayout s1 = (StackLayout)s.Parent;
                s1.Children[1].IsVisible = false;
                s1.Children[2].IsVisible = false;

                //string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                //string exId = $"{m.Id}";

                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                        s.Children[2].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        s.Children[2].BackgroundColor = Color.FromHex("#ECFF92");
                }

                //LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        s.Children[3].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        s.Children[3].BackgroundColor = Color.FromHex("#ECFF92");
                }
            }
            
            s.Children[0].IsVisible = false;
            //s.Children[2].IsVisible = m.IsSystemExercise;
            s.Children[1].IsVisible = false;
            s.Children[2].IsVisible = m.IsNextExercise;
            s.Children[3].IsVisible = m.IsNextExercise;
            s.Children[4].IsVisible = true;
            s.Children[5].IsVisible = false;
            s.Children[6].IsVisible = false;
            contextMenuStack = s;
        }

        private async Task UpdateExerciseList()
        {
            var exercises = new ObservableCollection<ExerciseWorkSetsModel>();
            exerciseItems = new ObservableCollection<ExerciseWorkSetsModel>();
            var exerList = new List<ExerciceModel>();
            try
            {

                string jsonFileName = "Exercises.json";
                ExerciceModel exerciseList = new ExerciceModel();
                var assembly = typeof(AllExercisePage).GetTypeInfo().Assembly;
                Stream stream = assembly.GetManifestResourceStream($"{assembly.GetName().Name}.{jsonFileName}");

                using (var reader = new System.IO.StreamReader(stream))
                {
                    var jsonString = reader.ReadToEnd();

                    //Converting JSON Array Objects into generic list    
                    var list = JsonConvert.DeserializeObject<List<DBExerciseModel>>(jsonString);
                    exerList = new List<ExerciceModel>();
                    foreach (var item in list)
                    {
                        exerList.Add(new ExerciceModel()
                        {
                            Id = item.Id,
                            Label = item.Label,
                            BodyPartId = item.BodyPartId,
                            EquipmentId = item.EquipmentId,
                            IsBodyweight = item.IsBodyweight,
                            IsEasy = item.IsEasy,
                            IsMedium = item.IsMedium,
                            IsPlate = item.EquipmentId == 3,
                            IsSystemExercise = true,
                            VideoUrl = string.IsNullOrEmpty(item.LocalVideo) ?  item.VideoUrl : "",
                            IsTimeBased = item.IsTimeBased,
                            IsUnilateral = item.IsUnilateral,
                            LocalVideo = item.LocalVideo,
                            IsFlexibility = item.IsFlexibility,
                            IsWeighted = item.IsWeighted,
                            IsOneHanded = item.IsOneHanded,
                            IsAssisted = item.IsAssisted
                        });
                    }
                    exerList = exerList.OrderBy(x => x.Label).ToList();
                }

            }
            catch (Exception ex)
            {

            }
            try
            {
                
                var ee = CurrentLog.Instance.CurrentExercise;
                LblWorkoutName.Text = ee.Label;
                var localVideo = "";
                try
                {
                    var localex = exerList.FirstOrDefault(x => x.Id == ee.Id);
                    if (localex != null)
                        localVideo = localex.LocalVideo;
                }
                catch (Exception ex)
                {

                }

                ExerciseWorkSetsModel e = new ExerciseWorkSetsModel()
                    {
                        Id = ee.Id,
                        IsBodyweight = ee.IsBodyweight,
                        IsEasy = ee.IsEasy,
                        IsNextExercise = ee.IsNextExercise,
                        IsSwapTarget = ee.IsSwapTarget,
                        IsFinished = ee.IsFinished,
                        IsSystemExercise = ee.IsSystemExercise,
                        IsNormalSets = ee.IsNormalSets,
                        IsUnilateral = ee.IsUnilateral,
                        IsTimeBased = ee.IsTimeBased,
                        IsMedium = ee.IsMedium,
                        BodyPartId = ee.BodyPartId,
                        Label = $"{ee.Label}",
                        VideoUrl =  ee.VideoUrl,
                        WorkoutGroupId = ee.WorkoutGroupId,
                        RepsMaxValue = ee.RepsMaxValue,
                        RepsMinValue = ee.RepsMinValue,
                        Timer = ee.Timer,
                        IsSelected = false,
                        CountNo = $"1/1",
                        IsPlate = ee.IsPlate,
                        EquipmentId = ee.EquipmentId,
                    LocalVideo = localVideo,
                    IsFlexibility = ee.IsFlexibility,
                    IsWeighted = ee.IsWeighted,
                    IsOneHanded = ee.IsOneHanded,
IsAssisted = ee.IsAssisted
                };
                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                LocalDBManager.Instance.SetDBReco("RReps" + ee.Id + setStyle + "challenge", $"");
                var weights = LocalDBManager.Instance.GetDBSetting($"SetupWeight{e.Id}")?.Value;
                if (!string.IsNullOrEmpty(weights))
                    LocalDBManager.Instance.SetDBReco("RReps" + e.Id + LocalDBManager.Instance.GetDBSetting("SetStyle").Value + "challenge", $"max");
                exercises.Add(e);
            //try
            //{
            //    long targetExerciseId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == e.Id).TargetExerciseId;
            //    GetExerciseRequest req = new GetExerciseRequest();
            //    req.ExerciseId = targetExerciseId;
            //    ExerciceModel emm = await DrMuscleRestClient.Instance.GetExercise(req);

            //    var seriParent = JsonConvert.SerializeObject(emm);
            //    ExerciseWorkSetsModel em = JsonConvert.DeserializeObject<ExerciseWorkSetsModel>(seriParent);

            //    if ((Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == em.Id) != null)
            //    {
            //        em.IsFinished = true;
            //        em.IsNextExercise = false;
            //    }
            //    em.IsSwapTarget = true;
            //    exercises.Add(em);
            //}
            //catch (Exception ex)
            //{
            //    SwapExerciseContext context = ((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == e.Id);
            //    if (!string.IsNullOrEmpty(context.Label))
            //    {
            //        ExerciseWorkSetsModel model = new ExerciseWorkSetsModel()
            //        {
            //            Id = (long)context.TargetExerciseId,
            //            Label = context.Label,
            //            IsBodyweight = context.IsBodyweight,
            //            IsSwapTarget = true,
            //            IsSystemExercise = context.IsSystemExercise,
            //            VideoUrl = context.VideoUrl,
            //            IsEasy = context.IsEasy,
            //            BodyPartId = context.BodyPartId,
            //            CountNo = $"1 of 1"

            //        };
            //        if ((Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == model.Id) != null)
            //        {
            //            model.IsFinished = true;
            //            model.IsNextExercise = false;
            //        }
            //        exercises.Add(model);

            //    }

            //}



            //var exModel = exercises.Where(x => x.IsFinished == false).FirstOrDefault();
            //if (exModel != null)
            //{
            //    exModel.IsNextExercise = true;
            //    ResetButtons();
            //}
            //else
            //    ChangeButtonsEmphasis();

            exerciseItems = exercises;
                //try
                //{
                //    if (CurrentLog.Instance.WorkoutsByExercise == null)
                //        CurrentLog.Instance.WorkoutsByExercise = new Dictionary<long, List<ExerciceModel>>();
                //    if (CurrentLog.Instance.WorkoutsByExercise.ContainsKey(CurrentLog.Instance.CurrentWorkoutTemplate.Id))
                //        CurrentLog.Instance.WorkoutsByExercise[CurrentLog.Instance.CurrentWorkoutTemplate.Id] = exercises.ToList();
                //    else
                //        CurrentLog.Instance.WorkoutsByExercise.Add(CurrentLog.Instance.CurrentWorkoutTemplate.Id, exercises.ToList());

                //}
                //catch (Exception ex)
                //{

                //}


                var nextExer = exerciseItems.First();
                nextExer.IsNextExercise = true;
                if (nextExer != null)
                {
                    NewExerciseLogResponseModel newExercise = await DrMuscleRestClient.Instance.IsNewExerciseWithSessionInfo(new ExerciceModel() { Id = nextExer.Id });
                    if (newExercise != null)
                    {
                        if (!newExercise.IsNewExercise)
                        {
                            // await FetchReco(nextExer);
                            try
                            {
                                DateTime? lastLogDate = newExercise.LastLogDate;
                                int? sessionDays = null;


                                string WeightRecommandation;
                                RecommendationModel reco = null;

                                //Todo: clean up on 2019 01 18
                                if (LocalDBManager.Instance.GetDBSetting("SetStyle") == null)
                                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                                if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
                                    LocalDBManager.Instance.SetDBSetting("QuickMode", "false");
                                var bodyPartname = "";


                                switch (nextExer.BodyPartId)
                                {
                                    case 1:

                                        break;
                                    case 2:
                                        bodyPartname = "Shoulders";
                                        break;
                                    case 3:
                                        bodyPartname = "Chest";
                                        break;
                                    case 4:
                                        bodyPartname = "Back";
                                        break;
                                    case 5:
                                        bodyPartname = "Biceps";
                                        break;
                                    case 6:
                                        bodyPartname = "Triceps";
                                        break;
                                    case 7:
                                        bodyPartname = "Abs";
                                        break;
                                    case 8:
                                        bodyPartname = "Legs";
                                        break;
                                    case 9:
                                        bodyPartname = "Calves";
                                        break;
                                    case 10:
                                        bodyPartname = "Neck";
                                        break;
                                    case 11:
                                        bodyPartname = "Forearm";
                                        break;
                                    default:
                                        //
                                        break;
                                }
                                if (CurrentLog.Instance.ExerciseLog == null)
                                {
                                    CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                                    CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(nextExer);
                                }
                                if (lastLogDate != null)
                                {
                                    var days = (int)(DateTime.Now - (DateTime)lastLogDate).TotalDays;
                                    if (days >= 5 && days <= 9)
                                        sessionDays = days;
                                    if (days > 9)
                                    {
                                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                                        {
                                            Title = "Light session?",
                                            Message = string.IsNullOrEmpty(bodyPartname) == false ? $"The last time you trained {bodyPartname.ToLower()} was {days} {AppResources.DaysAgoDoALightSessionToRecover}" : string.Format("{0} {1} {2} {3} {4}", "The last time you trained", CurrentLog.Instance.ExerciseLog.Exercice.Label, AppResources.was, days, AppResources.DaysAgoDoALightSessionToRecover),
                                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                            OkText = AppResources.LightSession,
                                            CancelText = AppResources.Cancel,
                                        };
                                        try
                                        {
                                            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                                            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                                        }
                                        catch (Exception ex)
                                        {

                                        }
                                        //var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                                        //if (isConfirm)
                                        //{
                                            if (days > 50)
                                                days = 50;
                                            sessionDays = days;
                                            //App.WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
                                            App.BodypartId = (int)CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId;
                                            App.Days = days;
                                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(nextExer.Id))
                                            {
                                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(nextExer.Id);
                                                nextExer.RecoModel = null;
                                            }
                                            //reco.Weight = new MultiUnityWeight(reco.Weight.Kg - ((reco.Weight.Kg * (decimal)1.5 * days) / 100), "kg");
                                            //reco.WarmUpWeightSet1 = new MultiUnityWeight(reco.WarmUpWeightSet1.Kg - ((reco.WarmUpWeightSet1.Kg * (decimal)1.5 * days) / 100), "kg");
                                            //reco.WarmUpWeightSet2 = new MultiUnityWeight(reco.WarmUpWeightSet2.Kg - ((reco.WarmUpWeightSet2.Kg * (decimal)1.5 * days) / 100), "kg");
                                        //}
                                        //else
                                        //{
                                        //    sessionDays = null;
                                        //    App.Days = 0;
                                        //    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(nextExer.Id))
                                        //    {
                                        //        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(nextExer.Id);
                                        //        nextExer.RecoModel = null;
                                        //    }
                                        //}
                                    }
                                }


                                await FetchReco(nextExer, sessionDays);
                            }
                            catch (Exception ex)
                            {
                                await FetchReco(nextExer, null);
                            }

                        }
                        else
                        {
                            RunExercise(nextExer);
                        }
                    }
                }
                //if (exerciseItems.Where(x => x.IsFinishWorkoutExe == true).FirstOrDefault() == null)
                //    exerciseItems.Add(new ExerciseWorkSetsModel() { IsFinishWorkoutExe = true });
                ExerciseListView.ItemsSource = exerciseItems;

            }
            catch (Exception e)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = AppResources.PleaseCheckInternetConnection,
                    Title = AppResources.ConnectionError
                });
                //await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
            }
        }

        public ExerciseWorkSetsModel GetExerciseWorkSetModel(ExerciceModel ee)
        {
            return new ExerciseWorkSetsModel()
            {
                Id = ee.Id,
                IsBodyweight = ee.IsBodyweight,
                IsEasy = ee.IsEasy,
                IsNextExercise = ee.IsNextExercise,
                IsSwapTarget = ee.IsSwapTarget,
                IsFinished = ee.IsFinished,
                IsSystemExercise = ee.IsSystemExercise,
                IsNormalSets = ee.IsNormalSets,
                IsUnilateral = ee.IsUnilateral,
                IsTimeBased = ee.IsTimeBased,
                IsMedium = ee.IsMedium,
                BodyPartId = ee.BodyPartId,
                Label = ee.Label,
                VideoUrl = ee.VideoUrl,
                WorkoutGroupId = ee.WorkoutGroupId,
                RepsMaxValue = ee.RepsMaxValue,
                RepsMinValue = ee.RepsMinValue,
                Timer = ee.Timer,
                IsPlate = ee.IsPlate,
                IsSelected = false,
                EquipmentId = ee.EquipmentId,
                LocalVideo = ee.LocalVideo,
                IsFlexibility = ee.IsFlexibility,
                IsWeighted = ee.IsWeighted,
                IsOneHanded = ee.IsOneHanded,
                IsAssisted = ee.IsAssisted
            };
        }

        public ExerciceModel GetExerciseModel(ExerciseWorkSetsModel ee)
        {
            return new ExerciceModel()
            {
                Id = ee.Id,
                IsBodyweight = ee.IsBodyweight,
                IsEasy = ee.IsEasy,
                IsNextExercise = ee.IsNextExercise,
                IsSwapTarget = ee.IsSwapTarget,
                IsFinished = ee.IsFinished,
                IsSystemExercise = ee.IsSystemExercise,
                IsNormalSets = ee.IsNormalSets,
                IsUnilateral = ee.IsUnilateral,
                IsTimeBased = ee.IsTimeBased,
                IsMedium = ee.IsMedium,
                BodyPartId = ee.BodyPartId,
                Label = ee.Label,
                VideoUrl = ee.VideoUrl,
                WorkoutGroupId = ee.WorkoutGroupId,
                RepsMaxValue = ee.RepsMaxValue,
                RepsMinValue = ee.RepsMinValue,
                Timer = ee.Timer,
                IsPlate = ee.IsPlate,
                EquipmentId = ee.EquipmentId,
                LocalVideo = ee.LocalVideo,
                IsFlexibility = ee.IsFlexibility,
                IsWeighted = ee.IsWeighted,
                IsOneHanded = ee.IsOneHanded,
                IsAssisted = ee.IsAssisted
            };
        }

        private async void ListTapped(object sender, EventArgs args)
        {
            if (contextMenuStack != null)
                HideContextButton();
        }


        private async void NewTapped(object sender, EventArgs args)
        {
            if (Config.AddExercisesPopUp == false)
            {
                if (App.IsAddExercisesPopUp)
                {
                    AddExercises();
                    return;
                }
                App.IsAddExercisesPopUp = true;
                ConfirmConfig ShowAddExePopUp = new ConfirmConfig()
                {
                    Message = "Add exercises to and reorder any workout on the fly.",
                    Title = "Add exercises",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = AppResources.GotIt,
                    CancelText = AppResources.RemindMe,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            Config.AddExercisesPopUp = true;
                            AddExercises();
                        }
                        else
                        {
                            Config.AddExercisesPopUp = false;
                            AddExercises();
                        }
                    }
                };
                await Task.Delay(100);
                UserDialogs.Instance.Confirm(ShowAddExePopUp);
            }
            else
            {
                CurrentLog.Instance.IsAddingExerciseLocally = true;
                await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
            }
        }

        private async void AddExercises()
        {
            CurrentLog.Instance.IsAddingExerciseLocally = true;
            await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
        }

        private void ResetButtons()
        {
            //SaveWorkoutButton.TextColor = Color.White;
            //SaveWorkoutButton.BackgroundColor = Color.Transparent;
            //SaveWorkoutButton.FontAttributes = FontAttributes.None;
        }

        private void ChangeButtonsEmphasis()
        {
            //SaveWorkoutButton.TextColor = Color.Black;
            //SaveWorkoutButton.BackgroundColor = Color.White;
            //SaveWorkoutButton.FontAttributes = FontAttributes.Bold;
        }
        async Task InjuryRehabSetup(ExerciseWorkSetsModel m)
        {
            if (m.Id == 0)
                return;
            CurrentLog.Instance.EndExerciseActivityPage = this.GetType();
            CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
            CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);

            string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
            LocalDBManager.Instance.SetDBReco("RReps" + m.Id + setStyle + "Deload", $"");

            try
            {



                if (m.IsBodyweight)
                {
                    decimal weight1 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);

                    KenkoAskForInjuryRehabReps(weight1, m.Label, m);
                    return;
                }
                NormalExercisePopup exPopup = new NormalExercisePopup(CurrentLog.Instance.ExerciseLog.Exercice.LocalVideo, string.Format("{0}", CurrentLog.Instance.ExerciseLog.Exercice.Label), m.IsAssisted ? "How much assistance for 5 easy reps?" : m.IsTimeBased ? m.EquipmentId == 4 && !m.IsOneHanded ? "How many seconds can you do easily without pain? Enter the weight for 1 hand." : "How many seconds can you do easily without pain?" : m.EquipmentId == 4 || m.EquipmentId == 5 ? m.BodyPartId == 14 && m.IsUnilateral ? "How much can you lift without pain 5 times? Enter the weight for 1 side." : m.IsOneHanded ? "How much can you lift without pain 5 times?" : "How much can you lift without pain 5 times? Enter the weight for 1 hand." : "How much can you lift without pain 5 times?", "Enter estimate", m, false);
                exPopup._kenkoPage = this;
                await PopupNavigation.Instance.PushAsync(exPopup);

            }
            catch (Exception ex)
            {

            }
        }

        protected async void KenkoAskForInjuryRehabReps(decimal weight1, string exerciseName, ExerciseWorkSetsModel m)
        {
            try
            {

                string desc = "";
                if (m.Label.ToLower().Contains("bands"))
                    desc = $"How many {(m.IsTimeBased ? "seconds" : "")} can you do easily without pain? Aim for {(LocalDBManager.Instance.GetDBSetting("repsminimum").Value == "0" ? "5" : LocalDBManager.Instance.GetDBSetting("repsminimum").Value)}+ reps. Choose your band accordingly.";
                else
                    desc = $"How many{(m.IsTimeBased ? " seconds" : "")} can you do easily without pain?";
                var popup = new NormalExercisePopup(CurrentLog.Instance.ExerciseLog.Exercice.LocalVideo, $"{m.Label}", $"How many{(m.IsTimeBased ? " seconds" : "")} can you do easily without pain?", "Enter estimate", m, true);
                popup._kenkoPage = this;
                PopupNavigation.Instance.PushAsync(popup);

            }
            catch
            {
            }
        }

        //New Exercise Setup
        protected async Task RunExercise(ExerciseWorkSetsModel m)
        {
            CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
            CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);

            try
            {

                //if (!string.IsNullOrEmpty(CurrentLog.Instance.ExerciseLog.Exercice.LocalVideo))
                //{
                //    m.Add(new WorkoutLogSerieModelRef()
                //    {
                //        IsHeaderCell = true,
                //        Weight = new MultiUnityWeight(0, "kg"),
                //        Reps = 0,
                //        VideoUrl = CurrentLog.Instance.ExerciseLog.Exercice.LocalVideo,
                //        IsSetupNotCompleted = true
                //    });
                //    await Task.Delay(1500);
                //}
                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                LocalDBManager.Instance.SetDBReco("RReps" + m.Id + setStyle + "Deload", $"");
                if (m.IsBodyweight && LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                {
                    decimal weight1 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                    if (m.Id == 16508)
                    {
                        SetUpCompletePopup(weight1, m.Label, m, 29, true);
                        return;
                    }
                    KenkoAskForReps(weight1, m.Label, m);
                    
                    return;
                }
                NormalExercisePopup exPopup = new NormalExercisePopup(CurrentLog.Instance.ExerciseLog.Exercice.LocalVideo, string.Format("{0}", CurrentLog.Instance.ExerciseLog.Exercice.Label), m.IsAssisted ? "How much assistance for 5 easy reps?" : m.IsTimeBased ? m.EquipmentId == 4 && !m.IsOneHanded ?  "How many seconds can you do easily? Enter the weight for 1 hand." : "How many seconds can you do easily?" : m.EquipmentId == 4 || m.EquipmentId == 5 ? m.BodyPartId == 14 && m.IsUnilateral ? "How much can you lift easily 5 times? Enter the weight for 1 side." : !m.IsOneHanded ? "How much can you lift easily 5 times? Enter the weight for 1 hand." : "How much can you lift easily 5 times?" : "How much can you lift easily 5 times?", "Enter estimate to test", m, false);
                exPopup._kenkoPage = this;
                await PopupNavigation.Instance.PushAsync(exPopup);
                return;
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = string.Format("{0} {1}", CurrentLog.Instance.ExerciseLog.Exercice.Label, AppResources.Setup),
                    //Message = m.IsBodyweight ? string.Format("{0} ({1} {2})?", AppResources.WhatsYourBodyWeight, AppResources._in,
                    //LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg") :
                    //m.IsEasy ?
                    //string.Format("{0} {1} {2}",
                    //AppResources.HowMuchCanYou, m.Label, AppResources.VeryVeryVeryEasily6TimesIwillImproveOnYourGuessAfterYourFirstWorkout) :
                    Message = m.IsTimeBased ? m.EquipmentId == 4 ? "How many seconds can you do easily? Enter the weight for 1 hand. I'll improve on your guess as you train." : "How many seconds can you do easily? I'll improve on your guess as you train." : m.EquipmentId == 4 ? "How much can you lift easily 5 times? Enter the weight for 1 hand. I'll improve on your guess as you train." : "How much can you lift easily 5 times? I'll improve on your guess as you train.",
                    Placeholder = "Enter estimate to test",
                    OkText = AppResources.Continue,
                    MaxLength = 4,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = async (weightResponse) =>
                    {
                        if (weightResponse.Ok)
                        {
                            if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                            {
                                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                                {
                                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                    Message = m.IsTimeBased ? "Please enter valid seconds." : "Please enter valid reps.",
                                    Title = AppResources.Error
                                });
                                //await UserDialogs.Instance.AlertAsync(m.IsTimeBased ? "Please enter valid seconds." : "Please enter valid reps.", AppResources.Error);
                                return;
                            }
                            var weightText = weightResponse.Value.Replace(",", ".");
                            decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                            if (m.IsBodyweight)
                            {
                                LocalDBManager.Instance.SetDBSetting("BodyWeight", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString());

                                await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                                {
                                    BodyWeight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                                });
                                KenkoAskForReps(new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg, m.Label, m);
                                return;
                            }
                            SetUpCompletePopup(new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg, m.Label, m);
                        }
                        else
                        {
                            m.Clear();
                            m.IsNextExercise = false;
                        }
                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);

            }
            catch (Exception ex)
            {
                //try
                //{
                //    if (!CurrentLog.Instance.WorkoutLogSeriesByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                //    {
                //        await PagesFactory.PushAsync<ExerciseChartPage>();
                //    }
                //    else
                //    {
                //        if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                //            LocalDBManager.Instance.SetDBSetting("timer_remaining", CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id).ToString());
                //        await PagesFactory.PushAsync<SaveSetPage>();
                //    }
                //}
                //catch (Exception e)
                //{
                //    var properties = new Dictionary<string, string>
                //    {
                //        { "DrMusclePage_RunExercise", $"{e.StackTrace}" }
                //    };
                //    Crashes.TrackError(e, properties);
                //    await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
                //}

            }
        }

        protected async void KenkoAskForReps(decimal weight1, string exerciseName, ExerciseWorkSetsModel m)
        {
            if (m.Id==16508)
            {
                SetUpCompletePopup(weight1, exerciseName, m, 29, true);
                return;
            }
            string desc = "";
            if (m.Label.ToLower().Contains("bands"))
                desc = $"How many{(m.IsTimeBased ? " seconds" : "")} can you do easily? Aim for {(LocalDBManager.Instance.GetDBSetting("repsminimum").Value == "0" ? "5" : LocalDBManager.Instance.GetDBSetting("repsminimum").Value)}+ reps. Choose your band accordingly.";
            else
                desc = $"How many{(m.IsTimeBased ? " seconds" : "")} can you do easily?";
            var popup = new NormalExercisePopup(CurrentLog.Instance.ExerciseLog.Exercice.LocalVideo, $"{m.Label}", $"How many{(m.IsTimeBased ? " seconds" : "")} can you do easily?", "Enter estimate to test", m, true);
            popup._kenkoPage = this;
            PopupNavigation.Instance.PushAsync(popup);

            return; 
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"{m.Label}",
                Message = $"How many{(m.IsTimeBased ? " seconds" : "")} can you do easily?",
                Placeholder = "Enter how many here",
                OkText = AppResources.Continue,
                MaxLength = 4,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (weightResponse.Ok)
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                        {
                            await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = m.IsTimeBased ? "Please enter valid seconds." : "Please enter valid reps.",
                                Title = AppResources.Error
                            });
                            //await UserDialogs.Instance.AlertAsync(m.IsTimeBased ? "Please enter valid seconds." : "Please enter valid reps.", AppResources.Error);
                            return;
                        }
                        int reps = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        SetUpCompletePopup(weight1, exerciseName, m, reps, true);
                    }
                    else
                    {
                        m.IsNextExercise = false;
                        m.Clear();
                    }
                }
            };
            if (m.Label.ToLower().Contains("bands"))
                firsttimeExercisePopup.Message = $"How many{(m.IsTimeBased ? " seconds" : "")} can you do easily? Aim for {(LocalDBManager.Instance.GetDBSetting("repsminimum").Value == "0" ? "5" : LocalDBManager.Instance.GetDBSetting("repsminimum").Value)}+ reps. Choose your band accordingly.";
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        public async void CancelClick(ExerciseWorkSetsModel m)
        {
            _injuryRehabRunning = false;
            LocalDBManager.Instance.SetDBSetting($"InjuryWeight{m.Id}", "");

        }

        public async void FinishSetup(ExerciseWorkSetsModel m, string userWeight, bool isBodyweight)
        {
            if (isBodyweight)
            {

                decimal weight1 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);

                int reps = Convert.ToInt32(userWeight, CultureInfo.InvariantCulture);
                if (_injuryRehabRunning && _injuryRehabWithExerciseId == m.Id)
                {
                    LocalDBManager.Instance.SetDBSetting($"InjuryReps{m.Id}", Convert.ToString(reps).ReplaceWithDot());
                    LocalDBManager.Instance.SetDBSetting($"InjuryWeight{m.Id}", Convert.ToString(weight1).ReplaceWithDot());
                    FetchReco(m, null);
                }
                else
                    SetUpCompletePopup(weight1, m.Label, m, reps, true);

            }
            else
            {
                var weightText = userWeight.ReplaceWithDot();
                decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                if (_injuryRehabRunning && _injuryRehabWithExerciseId == m.Id)
                {
                    LocalDBManager.Instance.SetDBSetting($"InjuryWeight{m.Id}", Convert.ToString(weight1).ReplaceWithDot());
                    FetchReco(m, null);
                }
                else
                    SetUpCompletePopup(new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg, m.Label, m);

            }
        }

        protected async void SetUpCompletePopup(decimal weight1, string exerciseName, ExerciseWorkSetsModel exe, int reps = 2, bool IsBodyweight = false)
        {

            NewExerciceLogModel model = new NewExerciceLogModel();
            model.ExerciseId = (int)CurrentLog.Instance.ExerciseLog.Exercice.Id;
            model.Username = LocalDBManager.Instance.GetDBSetting("email").Value;
            model.RIR = 1;
            if (IsBodyweight)
            {
                LocalDBManager.Instance.SetDBSetting($"SetupReps{model.ExerciseId}", Convert.ToString(reps).ReplaceWithDot());
                if (exe.IsEasy)
                    reps = reps + 2;
                else if (exe.IsMedium)
                    reps = reps + 1;
                else
                    reps = reps - 1;
                reps = 2;
                model.Weight1 = new MultiUnityWeight(weight1, "kg");
                model.Reps1 = reps.ToString();
                model.Weight2 = new MultiUnityWeight(weight1, "kg");
                model.Reps2 = (reps - 1).ToString();
                model.Weight3 = new MultiUnityWeight(weight1, "kg");
                model.Reps3 = (reps - 2).ToString();
                model.Weight4 = new MultiUnityWeight(weight1, "kg");
                model.Reps4 = (reps - 3).ToString();
                LocalDBManager.Instance.SetDBSetting($"SetupWeight{model.ExerciseId}", Convert.ToString(weight1).ReplaceWithDot());
            }
            else
            {
                decimal registenceWeight = 0;
                if (exe.IsAssisted)
                {
                    registenceWeight = weight1;
                    var assistedWeight = _userBodyWeight - weight1;
                    if (assistedWeight <= 0)
                        assistedWeight = 0;
                    weight1 = assistedWeight;
                }
                decimal weight2 = weight1 - (2 * weight1 / 100);
                decimal weight3 = weight2 - (2 * weight2 / 100);
                decimal weight4 = weight3 - (2 * weight3 / 100);
                model.Weight1 = new MultiUnityWeight(weight1, "kg");
                model.Reps1 = reps.ToString();
                model.Weight2 = new MultiUnityWeight(weight2, "kg");
                model.Reps2 = reps.ToString();
                model.Weight3 = new MultiUnityWeight(weight3, "kg");
                model.Reps3 = reps.ToString();
                model.Weight4 = new MultiUnityWeight(weight4, "kg");
                model.Reps4 = reps.ToString();
                
                LocalDBManager.Instance.SetDBSetting($"SetupWeight{model.ExerciseId}", Convert.ToString(weight1).ReplaceWithDot());
            }

            await DrMuscleRestClient.Instance.AddNewExerciseLogWithMoreSet(model);
            string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
            if (exe.BodyPartId == 12)
                LocalDBManager.Instance.SetDBReco("RReps" + model.ExerciseId + setStyle + "challenge", $"");
            else
                LocalDBManager.Instance.SetDBReco("RReps" + model.ExerciseId + setStyle + "challenge", $"max");
            exe.Clear();
            FetchReco(exe);
            //ConfirmConfig confirmExercise = new ConfirmConfig()
            //{
            //    Title = AppResources.SetupComplete,
            //    Message = string.Format("{0} {1}", exerciseName, AppResources.SetupCompleteExerciseNow),
            //    OkText = string.Format("{0}", exerciseName),
            //    CancelText = AppResources.Cancel,
            //    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed),
            //    OnAction = async (bool obj) => {
            //        if (obj)
            //        {
            //            //await PagesFactory.PushAsync<ExerciseChartPage>();

            //        }
            //    }
            //};

            //UserDialogs.Instance.Confirm(confirmExercise);
        }

    }

}