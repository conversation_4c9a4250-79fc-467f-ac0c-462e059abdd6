﻿using DrMuscle;
using DrMuscle.Dependencies;
using DrMuscle.Helpers;
using DrMuscle.Screens.Exercises;
using DrMuscleWebApiSharedModel;
using System;
using Xamarin.Forms;
using DrMuscle.Layout;
using DrMuscle.Message;
using Acr.UserDialogs;
using System.Text.RegularExpressions;
using System.Globalization;
using DrMuscle.Localize;
using DrMuscle.Resx;
using System.Collections.ObjectModel;
using DrMuscle.Constants;
using Rg.Plugins.Popup.Services;
using DrMuscle.Views;
using System.Collections.Generic;
using Plugin.Connectivity;
using DrMuscle.Screens.Workouts;
using System.Linq;
using DrMuscle.Screens.User.OnBoarding;
using System.Threading.Tasks;
using DrMuscle.Services;
using System.Threading;
using DrMuscle.Screens.Subscription;
using System.Net.Http;
using DrMuscle.Utility;

namespace DrMuscle.Screens.User
{
    public partial class SettingsPage : DrMusclePage, IActiveAware
    {
        public event EventHandler IsActiveChanged;
        public ObservableCollection<PlateModel> platesItems = new ObservableCollection<PlateModel>();
        public ObservableCollection<PlateModel> platesItems1 = new ObservableCollection<PlateModel>();
        public ObservableCollection<PlateModel> platesItems2 = new ObservableCollection<PlateModel>();


        public ObservableCollection<DumbellModel> dumbbellItems = new ObservableCollection<DumbellModel>();
        public ObservableCollection<DumbellModel> dumbbellItems1 = new ObservableCollection<DumbellModel>();
        public ObservableCollection<DumbellModel> dumbbellItems2 = new ObservableCollection<DumbellModel>();

        public ObservableCollection<PulleyModel> pulleyItems = new ObservableCollection<PulleyModel>();
        public ObservableCollection<PulleyModel> pulleyItems1 = new ObservableCollection<PulleyModel>();
        public ObservableCollection<PulleyModel> pulleyItems2 = new ObservableCollection<PulleyModel>();

        Dictionary<string, int> maxWorkoutDurations = new Dictionary<string, int>();
        MultiUnityWeight _increments;
        bool IsOnAppeatingFinished = false;
        bool IsEquipmentEdited = false;
        bool IsGymEnabled = false, IsHomeEnabled = false, IsOtherEnabled = false;
        bool _isActive;
        string learnMoreProgram = "";
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    IsActiveChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public SettingsPage()
        {
            InitializeComponent();
            Title = "Settings";

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                MessagingCenter.Subscribe<Message.LoadSettingsMessage>(this, "LoadSettingsMessage", (obj) =>
                {
                    OnAppearing();
                });    
            }
            
            KgSwitch.Toggled += (sender, e) =>
            {
                if (KgSwitch.IsToggled)
                {
                    LbsSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("massunit", "kg");
                    if (IsOnAppeatingFinished)
                        UpdateMassUnit("kg");
                }
                else
                {
                    LbsSwitch.IsToggled = true;
                }
            };

            LbsSwitch.Toggled += (sender, e) =>
            {
                if (LbsSwitch.IsToggled)
                {
                    KgSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("massunit", "lb");
                    if (IsOnAppeatingFinished)
                        UpdateMassUnit("lb");
                }
                else
                {
                    KgSwitch.IsToggled = true;
                }
            };

            BackOffSetSwitch.Toggled += (sender, e) =>
            {
                LocalDBManager.Instance.SetDBSetting("BackOffSet", BackOffSetSwitch.IsToggled == true ? "true" : "false");
                if (IsOnAppeatingFinished)
                    UpdateBackOffSet();
            };
             
            Switch1By1Side.Toggled += (sender, e) =>
            {
                if (IsOnAppeatingFinished)
                {
                    LocalDBManager.Instance.SetDBSetting("1By1Side", BackOffSetSwitch.IsToggled == true ? "true" : "false");
                    Update1By1Side();
                    if (Switch1By1Side.IsToggled)
                        Lbl1By1sides.Text = "For bilateral exercises like concentration curls, you will do all sets for arm 1, then all sets for arm 2";
                    else
                        Lbl1By1sides.Text = "For bilateral exercises like concentration curls, you will do arm 1, arm 2, rest, arm 1... and so on";
                }
            };


            StrengthSwitch.Toggled += (sender, e) =>
            {
                LocalDBManager.Instance.SetDBSetting("StrengthPhase", StrengthSwitch.IsToggled == true ? "true" : "false");
                if (IsOnAppeatingFinished)
                    UpdateStrengthPhase();
                LocalDBManager.Instance.ResetReco();
            };

            SaveMobilityRep.Clicked += (sender, e) => {
                var txt = MobilityRepsEntry.Text;
                if (txt == "0")
                    txt = "";
                LocalDBManager.Instance.SetDBSetting("MobilityRep", txt);
                if (IsOnAppeatingFinished)
                    SetUserMobilityLevel("");
            };

            //
            //UnitEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
            //MinEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
            //MaxEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
            BodyweightEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;

            BuildMuscle.Toggled += (sender, e) =>
            {
                if (BuildMuscle.IsToggled)
                {
                    BuildMuscleBurnFat.IsToggled = false;
                    FatBurning.IsToggled = false;
                    Custom.IsToggled = false;
                    CustomRepsStack.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");
                    LocalDBManager.Instance.SetDBSetting("repsminimum", "5");
                    LocalDBManager.Instance.SetDBSetting("repsmaximum", "12");
                    LocalDBManager.Instance.SetDBSetting("reprangeType", "1");
                    if (IsOnAppeatingFinished)
                    {
                        NotifyGloabalSettingsChanged();
                        DrMuscleRestClient.Instance.SetRepsMinimum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 5 });
                        DrMuscleRestClient.Instance.SetRepsMaximum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 12 });
                        DrMuscleRestClient.Instance.SetRepsRangeType(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 1 });
                    }
                }
            };

            BuildMuscleBurnFat.Toggled += (sender, e) =>
            {
                if (BuildMuscleBurnFat.IsToggled)
                {
                    BuildMuscle.IsToggled = false;
                    FatBurning.IsToggled = false;
                    Custom.IsToggled = false;
                    CustomRepsStack.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscleBurnFat");
                    LocalDBManager.Instance.SetDBSetting("repsminimum", "8");
                    LocalDBManager.Instance.SetDBSetting("repsmaximum", "15");
                    LocalDBManager.Instance.SetDBSetting("reprangeType", "2");
                    if (IsOnAppeatingFinished)
                    {
                        NotifyGloabalSettingsChanged();
                        DrMuscleRestClient.Instance.SetRepsMinimum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 8 });
                        DrMuscleRestClient.Instance.SetRepsMaximum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 15 });
                        DrMuscleRestClient.Instance.SetRepsRangeType(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 2 });
                    }
                }
            };

            FatBurning.Toggled += (sender, e) =>
            {
                if (FatBurning.IsToggled)
                {
                    BuildMuscleBurnFat.IsToggled = false;
                    BuildMuscle.IsToggled = false;
                    Custom.IsToggled = false;
                    CustomRepsStack.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("reprangeType", "3");
                    LocalDBManager.Instance.SetDBSetting("reprange", "FatBurning");
                    LocalDBManager.Instance.SetDBSetting("repsminimum", "12");
                    LocalDBManager.Instance.SetDBSetting("repsmaximum", "20");
                    if (IsOnAppeatingFinished)
                    {
                        NotifyGloabalSettingsChanged();
                        DrMuscleRestClient.Instance.SetRepsMinimum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 12 });
                        DrMuscleRestClient.Instance.SetRepsMaximum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 20 });
                        DrMuscleRestClient.Instance.SetRepsRangeType(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 3 });
                    }
                }
            };

            Custom.Toggled += (sender, e) =>
            {
                if (Custom.IsToggled)
                {
                    
                    BuildMuscleBurnFat.IsToggled = false;
                    FatBurning.IsToggled = false;
                    BuildMuscle.IsToggled = false;
                    LoadCustomReps();
                    LocalDBManager.Instance.SetDBSetting("reprangeType", "4");
                    LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                    if (IsOnAppeatingFinished)
                    {
                        NotifyGloabalSettingsChanged();
                        DrMuscleRestClient.Instance.SetRepsRangeType(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 4 });
                    }
                        
                }
                else
                {
                    CustomRepsStack.IsVisible = false;
                    if (!BuildMuscleBurnFat.IsToggled
                    && !BuildMuscle.IsToggled && !FatBurning.IsToggled)
                        BuildMuscle.IsToggled = true;
                }
            };

            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");


            if (LocalDBManager.Instance.GetDBSetting("Reminder5th") == null)
                LocalDBManager.Instance.SetDBSetting("Reminder5th", "true");
            ReminderSwitch.Toggled +=  async (sender, e) =>
            {
                if (IsOnAppeatingFinished)
                {
                    if (ReminderSwitch.IsToggled)
                    {
                        CheckNotificationPermission();
                        LocalDBManager.Instance.SetDBSetting("Reminder5th", "true");
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("Reminder5th", "false");
                    }

                    SetUserReminder();
                }
            };

            
            //flexibility
            flexiblitySwitch.Toggled += async (sender, e) =>
            {


                if (flexiblitySwitch.IsToggled)
                {
                    LblFlexibilityLvl.IsVisible = true;
                    FlexiStack.IsVisible = true;
                    LblFlexibilityExplainer.IsVisible = false;
                }
                else
                {
                    LblFlexibilityLvl.IsVisible = false;
                    FlexiStack.IsVisible = false;
                    LblFlexibilityExplainer.IsVisible = true;
                }
                if (IsOnAppeatingFinished)
                 {
                    LocalDBManager.Instance.SetDBSetting("IsMobility", flexiblitySwitch.IsToggled ? "true" : "false");
                    if (flexiblitySwitch.IsToggled)
                    {
                        SetMobilityLevel();
                    }
                    SetUserMobility();
                }
            };

            emailReminderSwitch.Toggled += async (sender, e) =>
            {
                if (IsOnAppeatingFinished)
                {

                    if (emailReminderSwitch.IsToggled)
                    {
                        CheckNotificationPermission();
                        StackEmailReminder.IsVisible = true;
                    }
                    else
                    {
                        StackEmailReminder.IsVisible = false;
                        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1122);
                    }
                    SetUserWorkoutReminderEmail();
                }
            };

            ExerciseQuickSwitch.Toggled += async (sender, e) =>
            {
                if (IsOnAppeatingFinished)
                {
                    LocalDBManager.Instance.SetDBSetting("IsExerciseQuickMode", ExerciseQuickSwitch.IsToggled ? "true" : "false");

                    SetUserExerciseQuickMode();
                }

            };

            

            CardioSwitch.Toggled += (sender, e) =>
            {
                if (CardioSwitch.IsToggled)
                {
                    LocalDBManager.Instance.SetDBSetting("Cardio", "true");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("Cardio", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserCardio();
            };
            //Set Language
            EnglishSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {
                if (EnglishSwitch.IsToggled)
                {
                    SwedishSwitch.IsToggled = false;
                    FrenchSwitch.IsToggled = false;
                    GermanSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "en");
                    setLocale();
                }
            };

            SwedishSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {
                if (SwedishSwitch.IsToggled)
                {
                    EnglishSwitch.IsToggled = false;
                    FrenchSwitch.IsToggled = false;
                    GermanSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "sv");
                    LocalDBManager.Instance.SetDBSetting("AppLanguageName", "Swedish");
                    setLocale();
                }
                else if (GermanSwitch.IsToggled)
                {
                    GermanSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "de-CH");
                    setLocale();
                }
                else if (FrenchSwitch.IsToggled)
                {
                    FrenchSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "fr-FR");
                    setLocale();
                }
                else
                {
                    EnglishSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "en");
                    setLocale();

                }
            };


            FrenchSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {
                if (FrenchSwitch.IsToggled)
                {
                    SwedishSwitch.IsToggled = false;
                    EnglishSwitch.IsToggled = false;
                    GermanSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "fr-FR");
                    setLocale();
                }
                else if (GermanSwitch.IsToggled)
                {
                    GermanSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "de-CH");
                    setLocale();
                }
                else if (SwedishSwitch.IsToggled)
                {
                    SwedishSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "sv");
                    setLocale();
                }
                else
                {
                    EnglishSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "en");
                    setLocale();
                }
            };

            GermanSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {

                if (GermanSwitch.IsToggled)
                {
                    EnglishSwitch.IsToggled = false;
                    SwedishSwitch.IsToggled = false;
                    FrenchSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "de-CH");
                    setLocale();
                }
                else if (FrenchSwitch.IsToggled)
                {
                    SwedishSwitch.IsToggled = false;
                    EnglishSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "fr-FR");
                    setLocale();
                }
                else if (SwedishSwitch.IsToggled)
                {
                    SwedishSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "sv");
                    setLocale();
                }
                else
                {
                    EnglishSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("AppLanguage", "en");
                    setLocale();
                }
            };
            // Contrôle du label reps minimum 

            RepsMinimumLess.Clicked += (sender, e) =>
            {

                int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
                int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

                if (currentRepsMinimum > 5)
                    currentRepsMinimum = currentRepsMinimum - 1;
                else
                {
                    UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = AppResources.LessThan5Reps,
                        Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                        OkText = AppResources.Ok
                    });
                }
                RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                LocalDBManager.Instance.SetDBSetting("repsminimum", RepsMinimumLabel.Text);
            };

            RepsMinimumMore.Clicked += (sender, e) =>
            {

                int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
                int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

                if (currentRepsMinimum >= currentRepsMaximum)
                    UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Message = AppResources.PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther,
                        OkText = AppResources.Ok
                    });

                else
                {
                    if (currentRepsMinimum < 30)
                        currentRepsMinimum = currentRepsMinimum + 1;
                    else
                    {
                        UserDialogs.Instance.AlertAsync(new AlertConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Title = AppResources.MoreThan30Reps,
                            Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                            OkText = AppResources.Ok
                        });

                    }
                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                    LocalDBManager.Instance.SetDBSetting("repsminimum", RepsMinimumLabel.Text);
                }
            };

            //CustomizedButton.Clicked += async (sender, e) =>
            //{
            //    if (LocalDBManager.Instance.GetDBSetting("PlatesKg") == null || LocalDBManager.Instance.GetDBSetting("PlatesLb") == null)
            //    {
            //        var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
            //        LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);

            //        var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True";
            //        LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
            //    }

            //    await PagesFactory.PushAsync<DrMuscle.Layout.EquipmentPage>();
            //};
            // Contrôle du label reps maximum 

            RepsMaximumLess.Clicked += (sender, e) =>
            {

                int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
                int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

                if (currentRepsMaximum <= currentRepsMinimum)
                    UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = AppResources.LessThan5Reps,
                        Message = AppResources.PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther,
                        OkText = AppResources.Ok
                    });
                else
                {
                    if (currentRepsMaximum > 5)
                        currentRepsMaximum = currentRepsMaximum - 1;
                    else
                    {
                        UserDialogs.Instance.AlertAsync(new AlertConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Title = AppResources.LessThan5Reps,
                            Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                            OkText = AppResources.Ok
                        });


                    }
                    RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                    LocalDBManager.Instance.SetDBSetting("repsmaximum", RepsMaximumLabel.Text);
                }
            };

            RepsMaximumMore.Clicked += (sender, e) =>
            {

                int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
                int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

                if (currentRepsMaximum < 30)
                    currentRepsMaximum = currentRepsMaximum + 1;
                else
                {
                    UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = AppResources.MoreThan30Reps,
                        Message = AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                        OkText = AppResources.Ok
                    });
                }
                RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                LocalDBManager.Instance.SetDBSetting("repsmaximum", RepsMaximumLabel.Text);
            };

            // Bouton save custom reps

            SaveCustomRepsButton.Clicked += async (sender, e) =>
            {
                try
                {
                    int RepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
                    int RepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetRepsMinimum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = RepsMinimum });
                    await DrMuscleRestClient.Instance.SetRepsMaximum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = RepsMaximum });

                    //UserDialogs.Instance.AlertAsync("Your custom reps have been saved.", "All set to crush it!");
                }
                catch (Exception)
                {

                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = AppResources.ConnectionError,
                        Message = AppResources.PleaseCheckInternetConnection,
                        OkText = AppResources.Ok
                    });

                }
            };
            SaveSetCountButton.Clicked += SaveSetCountButton_Clicked;
            SaveWarmupButton.Clicked += async (sender, e) =>
            {
                try
                {
                    if (!string.IsNullOrEmpty(WarmupEntry.Text))
                    {
                        var count = int.Parse(WarmupEntry.Text.ReplaceWithDot());
                        if (count < 0 || count > 99)
                        {


                            await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = "Warmup set from 0 to 99",
                                OkText = AppResources.Ok
                            });
                            return;
                        }
                        NotifyGloabalSettingsChanged();
                        await DrMuscleRestClient.Instance.SetCustomWarmups(new UserInfosModel()
                        {
                            WarmupsValue = int.Parse(WarmupEntry.Text.ReplaceWithDot())
                        });
                        LocalDBManager.Instance.SetDBSetting("warmups", WarmupEntry.Text.ReplaceWithDot());
                    }
                    else
                    {
                        NotifyGloabalSettingsChanged();
                        await DrMuscleRestClient.Instance.SetCustomWarmups(new UserInfosModel()
                        {
                            WarmupsValue = null
                        });
                        LocalDBManager.Instance.SetDBSetting("warmups", null);
                    }
                    LocalDBManager.Instance.ResetReco();
                }
                catch (Exception)
                {

                }
            };

            SaveBodyweightButton.Clicked += async (sender, e) =>
            {
                try
                {
                    MultiUnityWeight bodyweight = null;
                    if (string.IsNullOrWhiteSpace(BodyweightEntry.Text) || Convert.ToDecimal(BodyweightEntry.Text, CultureInfo.InvariantCulture) < 1)
                    {
                        await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Title = AppResources.Error,
                            Message = "Please enter your body weight.",
                            OkText = AppResources.Ok
                        });
                        return;
                    }
                    if (!string.IsNullOrEmpty(BodyweightEntry.Text))
                    {

                        var text = BodyweightEntry.Text.Replace(",", ".");
                        var result = Convert.ToDecimal(text, System.Globalization.CultureInfo.InvariantCulture);
                        if (string.IsNullOrEmpty(result.ToString()) || result.ToString().Equals("0"))
                        {

                        }
                        else
                        {
                            bodyweight = new MultiUnityWeight((decimal)result, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                            LocalDBManager.Instance.SetDBSetting("BodyWeight", bodyweight.Kg.ToString().Replace(",", "."));
                            NotifyGloabalSettingsChanged();
                            await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                            {
                                BodyWeight = new MultiUnityWeight(bodyweight.Kg, "kg")
                            });
                            if (Device.RuntimePlatform.Equals(Device.iOS))
                            {
                                IHealthData _healthService = DependencyService.Get<IHealthData>();
                                await _healthService.GetWeightPermissionAsync(async (r) =>
                                {
                                    var a = r;
                                    if (r)
                                    {
                                        _healthService.SetWeight(LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? (double)Math.Round(bodyweight.Kg, 2) : (double)Math.Round(bodyweight.Lb, 2));
                                    }
                                });
                            }
                            Xamarin.Forms.MessagingCenter.Send<BodyweightUpdateMessage>(new BodyweightUpdateMessage() { }, "BodyweightUpdateMessage");
                        }
                    }
                }
                catch (Exception ex)
                {

                }
            };
            
            //Set style

            //NormalSetsSwitch.Toggled += (sender, e) =>
            //{
            //    if (NormalSetsSwitch.IsToggled)
            //    {
            //        RestPauseSetsSwitch.IsToggled = false;
            //        LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
            //        UpdateSetSetyle(true);
            //        //Todo: save setting in DB?
            //        //DrMuscleRestClient.Instance.SetRepsMinimum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 12 });
            //    }
            //    else
            //    {
            //        RestPauseSetsSwitch.IsToggled = true;
            //        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            //        UpdateSetSetyle(false);
            //    }

            //};

            //RestPauseSetsSwitch.Toggled += (sender, e) =>
            //{
            //    if (RestPauseSetsSwitch.IsToggled)
            //    {
            //        NormalSetsSwitch.IsToggled = false;
            //        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            //        UpdateSetSetyle(false);
            //        //Todo: save setting in DB?
            //        //DrMuscleRestClient.Instance.SetRepsMinimum(new DrMuscleWebApiSharedModel.SingleIntegerModel() { IntValue = 12 });
            //    }
            //    else
            //    {
            //        NormalSetsSwitch.IsToggled = true;
            //        LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
            //        UpdateSetSetyle(true);
            //    }
            //};

            //QuickModewitch.Toggled += async (sender, e) =>
            //{

            //    var isToggled = QuickModewitch.IsToggled;
            //    if (QuickModewitch.IsToggled)
            //    {
            //        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null && LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
            //        {
            //            isToggled = LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value == "true" ? true : false;
            //        }
            //        else
            //            LocalDBManager.Instance.SetDBSetting("QuickMode", "true");
            //    }
            //    else
            //    {
            //        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null && LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
            //        {
            //            isToggled = LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value == "true" ? true : false;
            //        }
            //        else
            //            LocalDBManager.Instance.SetDBSetting("QuickMode", "false");
            //    }
            //    await DrMuscleRestClient.Instance.SetUserQuickMode(new UserInfosModel()
            //    {
            //        IsQuickMode = isToggled
            //    });
            //    LocalDBManager.Instance.ResetReco();
            //};
            //Background image

            MaleSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {
                if (MaleSwitch.IsToggled)
                {
                    FemaleSwitch.IsToggled = false;
                    NoImageSwitch.IsToggled = false;
                    MuscleLogoSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Background2.png");
                }
                else if (FemaleSwitch.IsToggled)
                    FemaleSwitch.IsToggled = true;
                else if (MuscleLogoSwitch.IsToggled)
                    MuscleLogoSwitch.IsToggled = true;
                else
                    NoImageSwitch.IsToggled = true;
              //  MessagingCenter.Send(this, "BackgroundImageUpdated");
            };

            FemaleSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {
                if (FemaleSwitch.IsToggled)
                {
                    MaleSwitch.IsToggled = false;
                    NoImageSwitch.IsToggled = false;
                    MuscleLogoSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "BackgroundFemale.png");
                }
                else if (MaleSwitch.IsToggled)
                    MaleSwitch.IsToggled = true;
                else if (MuscleLogoSwitch.IsToggled)
                    MuscleLogoSwitch.IsToggled = true;
                else
                    NoImageSwitch.IsToggled = true;
               // MessagingCenter.Send(this, "BackgroundImageUpdated");
            };

            MuscleLogoSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {
                if (MuscleLogoSwitch.IsToggled)
                {
                    MaleSwitch.IsToggled = false;
                    FemaleSwitch.IsToggled = false;
                    NoImageSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "DrMuscleLogo.png");
                }
                else if (MaleSwitch.IsToggled)
                    MaleSwitch.IsToggled = true;
                else if (FemaleSwitch.IsToggled)
                    FemaleSwitch.IsToggled = true;
                else
                    NoImageSwitch.IsToggled = true;
                //MessagingCenter.Send(this, "BackgroundImageUpdated");

            };

            NoImageSwitch.Toggled += (object sender, ToggledEventArgs e) =>
            {
                if (NoImageSwitch.IsToggled)
                {
                    MaleSwitch.IsToggled = false;
                    FemaleSwitch.IsToggled = false;
                    MuscleLogoSwitch.IsToggled = false;
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Backgroundblack.png");
                }
                else if (MaleSwitch.IsToggled)
                {
                    MaleSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Background2.png");

                }
                else if (FemaleSwitch.IsToggled)
                {
                    FemaleSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "BackgroundFemale.png");

                }
                else
                {
                    MuscleLogoSwitch.IsToggled = true;
                    LocalDBManager.Instance.SetDBSetting("BackgroundImage", "DrMuscleLogo.png");
                }

               // MessagingCenter.Send(this, "BackgroundImageUpdated");
            };

            var tapLinkGestureRecognizerReps = new TapGestureRecognizer();
            tapLinkGestureRecognizerReps.Tapped += (s, e) =>
            {
                UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Title = AppResources.AllRepsBuildMuscle,
                    Message = AppResources.LowRepsBuildMoreStrengthHighRepsAreEasierOnYourJoints,
                    OkText = AppResources.Ok
                });


            };
            LearnMoreAboutRepFocusLink.GestureRecognizers.Add(tapLinkGestureRecognizerReps);

            //var tapLinkGestureRecognizeProgram = new TapGestureRecognizer();
            //tapLinkGestureRecognizeProgram.Tapped += (s, e) =>
            //{
            //    if (string.IsNullOrEmpty(learnMoreProgram))
            //        return;
            //    if (learnMoreProgram.Equals("FAQ"))
            //    {
            //        PagesFactory.PushAsync<FAQPage>();
            //        return;
            //    }
            //    Device.OpenUri(new Uri(learnMoreProgram));
            //};
            //LearnMoreAbouProgramLink.GestureRecognizers.Add(tapLinkGestureRecognizeProgram);

            RefreshLocalized();

            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
                OnAppearing();
            });
           // WorkoutReminderButton.Clicked += Reminder_Click;

        }

        private async void CheckNotificationPermission()
        {
            if (!DependencyService.Get<INotificationsInterface>().registeredForNotifications())
            {
                ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                {
                    Title = "Enable notifcations",
                    Message = "Notifcations off. Enable to get reminders.",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Enable",
                    CancelText = AppResources.Cancel,
                };
                var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                if (isConfirm)
                {
                    DependencyService.Get<IAppSettingsHelper>().OpenAppSettings();
                }
            }
        }

        private async void SaveSetCountButton_Clicked(object sender, EventArgs e)
        {
            try
            {
                SetEntry.Unfocus();
                if (!string.IsNullOrEmpty(SetEntry.Text))
                {
                    var count = int.Parse(SetEntry.Text.ReplaceWithDot());
                    if (count < 2 || count > 99)
                    {


                        await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Message = "At least 2 work sets",
                            OkText = AppResources.Ok
                        });
                        return;
                    }
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserSetCount(new UserInfosModel()
                    {
                        SetCount = count
                    });
                    
                    LocalDBManager.Instance.SetDBSetting("WorkSetCount", SetEntry.Text.ReplaceWithDot());
                    LocalDBManager.Instance.SetDBSetting("QuickMode", "null");
                }
                else
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Message = "At least 2 work sets",
                        OkText = AppResources.Ok
                    });

                    //await DrMuscleRestClient.Instance.SetUserSetCount(new UserInfosModel()
                    //{
                    //    SetCount = null
                    //});
                    //LocalDBManager.Instance.SetDBSetting("WorkSetCount", null);
                }
                LocalDBManager.Instance.ResetReco();
            }
            catch (Exception)
            {

            }
        }

        private void RefreshLocalized()
        {

            LblRepRange.Text = AppResources.REPRANGE;
            LblYourProgressFaster.Text = "You progress faster when you change reps often. Choose a preferred range. Your reps will vary automatically in that range. Actual reps also depend on your equipment increments.";// AppResources.YouProgressFasterWhenYouChangeRepsOftenChooseARangeYourRepsWillChangeAutomaticallyEveryWorkout;
            LearnMoreAboutRepFocusLink.Text = AppResources.LearnMore;

            Lbl512Reps.Text = AppResources.FiveToTwelveReps;
            Lbl815Reps.Text = AppResources.EightToFifteenReps;
            Lbl1220Reps.Text = AppResources.TwelveToTwentyReps;
            LblCustom.Text = AppResources.Custom;
            LblMin.Text = AppResources.Min;
            LblMax.Text = AppResources.Max;
            SaveCustomRepsButton.Text = AppResources.SaveCustomReps;
            LblSetStyle.Text = AppResources.Sets.ToUpper();
            //LblRestPauseSets.Text = AppResources.RestPauseSetsAreHarderButMakeYourWorkouts59Faster;
            //LblNormalSets.Text = AppResources.NormalSets;
            //LblRestPasue.Text = AppResources.RestPauseSets;
            LblUnits.Text = AppResources.UNITS;
            //Increments.Text = AppResources.Increments;
            LblBackgroundImage.Text = AppResources.BACKGROUNDIMAGE;
            LblMale.Text = AppResources.Male;
            LblFemale.Text = AppResources.Female;
            LblDrMuscle.Text = AppResources.DrMuslce;
            LblNoImage.Text = AppResources.NoImage;
            LblLanguage.Text = AppResources.LANGUAGE;
            //SaveIncrementsButton.Text = AppResources.SaveIncrements;
            LblKg.Text = AppResources.Kg;
            LblLbs.Text = AppResources.Lbs;
            //UnitEntry.Placeholder = AppResources.TapToSet;
            //LblMaxIncrements.Text = AppResources.MaxWeight;
            //LblMinIncrements.Text = AppResources.MinWeight;
            //LblQuickMode.Text = AppResources.TwoWorkSetsPerExercise;
            //Lbl30minMode.Text = AppResources.ThirtyMinMode;
            SaveWarmupButton.Text = "Save warm-up sets";
            LblHowManyWarmups.Text = AppResources.WarmUpSets;
            WarmupEntry.Placeholder = AppResources.TapToSet;
            //LblCustomWarmUp.Text = AppResources.UseCustomWarmUps;
        }


        private async void ChangingWorkout()
        {
            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = AppResources.ConnectionError,
                        Message = AppResources.PleaseCheckInternetConnection,
                        OkText = AppResources.Ok
                    });
                    return;
                }
                try
                {
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                            LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                        }
                    }
                }
                catch (Exception ex)
                {

                }
                try
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp == 999)
                    {
                        foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                        {
                            LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exerciceModel.Id}", "false");
                        }
                        await PagesFactory.PopToRootAsync();
                        return;
                    }
                }
                catch (Exception ex)
                {

                }
                bool isSystem = false;
                NotifyGloabalSettingsChanged();
                BooleanModel successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkoutV2(new SaveWorkoutModel() { WorkoutId = 12645 } );
                try
                {
                    if (successWorkoutLog.Result)
                    {
                        Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage(), "UpdatedWorkoutMessage");
                    }
                    LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
                    LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());
                    isSystem = CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise;
                }
                catch (Exception ex)
                {

                }

                var nextworkoutName = CurrentLog.Instance.CurrentWorkoutTemplate.Label;
                CurrentLog.Instance.CurrentWorkoutTemplate = null;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
                string fname = LocalDBManager.Instance.GetDBSetting("firstname").Value;

                try
                {
                    AlertConfig p = new AlertConfig()
                    {
                        Title = $"{AppResources.GotIt} {fname}!",
                        Message = $"Your next workout will be Bodyweight 1.",
                        OkText = AppResources.Ok,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    };
                    p.OnAction = async () =>
                    {
                        await PagesFactory.PopToRootAsync();
                    };
                    UserDialogs.Instance.Alert(p);

                }

                catch (Exception ex)
                {
                    await PagesFactory.PopToRootAsync();
                }

            }
            catch (Exception ex)
            {

            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            if (App.IsSidemenuOpen)
                return;
            IsOnAppeatingFinished = false;
            DependencyService.Get<IFirebase>().SetScreenName("settings_page");
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            //MessagingCenter.Send<GlobalSettingsChangeMessage>(new GlobalSettingsChangeMessage() { IsDisappear = true }, "GlobalSettingsChangeMessage");
            try
            {
                List<string> bodyParts = new List<string>();
                bodyParts.Add("None");
                bodyParts.Add("Abs");
                bodyParts.Add("Biceps");
                bodyParts.Add("Calves");
                bodyParts.Add("Chest");
                bodyParts.Add("Glutes");
                bodyParts.Add("Legs");
                bodyParts.Add("Shoulders");
                bodyParts.Add("Traps");
                bodyParts.Add("Triceps");
                bodyParts.Add("Upper back");
                


                PickerBodyPart.ItemsSource = bodyParts;

                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority") == null)
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value) || LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Balanced")
                {
                    PickerBodyPart.SelectedIndex = 0;
                    LblBodypart.Text = "Adds 1 exercise for your priority part";
                }
                else
                {
                    PickerBodyPart.SelectedItem = LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value.Trim();
                    LblBodypart.Text = string.Format("Adds 1 {0} exercise", LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value.Trim().ToLower());
                }
            }
            catch (Exception ex)
            {

            }

            try
            {
                
                
                maxWorkoutDurations = new Dictionary<string, int>();
                maxWorkoutDurations.Add("30 min",1);
                maxWorkoutDurations.Add("45 min",2);
                maxWorkoutDurations.Add("1 hour",3);
                maxWorkoutDurations.Add("Unlimited",0);


                PickerWorkoutDuration.ItemsSource = maxWorkoutDurations.Select(x=>x.Key).ToList();
                
              
                if (LocalDBManager.Instance.GetDBSetting("MaxWorkoutDuration") == null)
                    LocalDBManager.Instance.SetDBSetting("MaxWorkoutDuration", "0");
                if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("MaxWorkoutDuration")?.Value) || LocalDBManager.Instance.GetDBSetting("MaxWorkoutDuration")?.Value == "0")
                {
                    PickerWorkoutDuration.SelectedIndex = 3;
                    LblMaxWorkoutDuration.Text = "No change to workout.";
                }
                else
                {
                    var item = maxWorkoutDurations
                        .First(x => x.Value == Int32.Parse(LocalDBManager.Instance.GetDBSetting("MaxWorkoutDuration")?.Value));
                    PickerWorkoutDuration.SelectedItem = item.Key;
                    var isRestPause = LocalDBManager.Instance.GetDBSetting("SetStyle")?.Value == "RestPause";
                    switch (item.Value)
                    {
                        case 0:
                            LblMaxWorkoutDuration.Text = "No change to workout.";
                            break;
                        case 1:
                            LblMaxWorkoutDuration.Text = isRestPause ? "1 warm-up set, max 2 work sets." : "No warm-up sets, max 2 work sets.";
                            break;
                        case 2:
                            LblMaxWorkoutDuration.Text = isRestPause ? "1 warm-up set, max 3 work sets." : "1 warm-up set, max 2 work sets.";
                            break;
                        case 3:
                            LblMaxWorkoutDuration.Text =  isRestPause ? "1 warm-up set, max 4 work sets." : "1 warm-up set, max 3 work sets.";
                            break;
                    }
                }
            }
            catch (Exception ex)
            {

            }

            
            if (LocalDBManager.Instance.GetDBSetting("reprangeType") == null)
                LocalDBManager.Instance.SetDBSetting("reprangeType", "4");
            KgSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
            LbsSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "lb";
            BuildMuscle.IsToggled = LocalDBManager.Instance.GetDBSetting("reprangeType")?.Value == "1";
            BuildMuscleBurnFat.IsToggled = LocalDBManager.Instance.GetDBSetting("reprangeType")?.Value == "2";
            FatBurning.IsToggled = LocalDBManager.Instance.GetDBSetting("reprangeType")?.Value == "3";
            Custom.IsToggled = LocalDBManager.Instance.GetDBSetting("reprangeType")?.Value == "4";

            if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            {
                //weight1 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                //grams = Math.Round(weight1 * (decimal)2.4);

                var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                var weight1 = new MultiUnityWeight(value, "kg");
                BodyweightEntry.Text = string.Format("{0:0.00}", LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? weight1.Kg : weight1.Lb);
            }

            //if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null)
            //{
            //    if (LocalDBManager.Instance.GetDBSetting("workout_increments").Value != null)
            //    {
            //        var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
            //        var unit = new MultiUnityWeight(value, "kg");
            //        UnitEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{unit.Kg}" : $"{unit.Lb}";
            //    }
            //}
            //else
            //    UnitEntry.Text = "";

            //if (LocalDBManager.Instance.GetDBSetting("workout_max") != null)
            //{
            //    if (LocalDBManager.Instance.GetDBSetting("workout_max").Value != null)
            //    {
            //        var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_max").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
            //        var unit = new MultiUnityWeight(value, "kg");
            //        MaxEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{unit.Kg}" : $"{unit.Lb}";
            //    }
            //}
            //else
            //    MaxEntry.Text = "";
            //if (LocalDBManager.Instance.GetDBSetting("workout_min") != null)
            //{
            //    if (LocalDBManager.Instance.GetDBSetting("workout_min").Value != null)
            //    {
            //        var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_min").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
            //        var unit = new MultiUnityWeight(value, "kg");
            //        MinEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{unit.Kg}" : $"{unit.Lb}";
            //    }
            //}
            //else
            //    MinEntry.Text = "";

            if (LocalDBManager.Instance.GetDBSetting("warmups") != null)
            {
                WarmupEntry.Text = Convert.ToString(LocalDBManager.Instance.GetDBSetting("warmups").Value);
            }
            else
                WarmupEntry.Text = "";

            if (LocalDBManager.Instance.GetDBSetting("WorkSetCount") != null)
            {
                SetEntry.Text = Convert.ToString(LocalDBManager.Instance.GetDBSetting("WorkSetCount").Value);
            }
            else
                SetEntry.Text = "";

            if (LocalDBManager.Instance.GetDBSetting("AppLanguage") == null)
                LocalDBManager.Instance.SetDBSetting("AppLanguage", "en");
            if (LocalDBManager.Instance.GetDBSetting("StrengthPhase") == null)
                LocalDBManager.Instance.SetDBSetting("StrengthPhase", "true");
            EnglishSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("AppLanguage")?.Value == "en";
            SwedishSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("AppLanguage")?.Value == "sv";
            FrenchSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("AppLanguage")?.Value == "fr-FR";
            GermanSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("AppLanguage")?.Value == "de-CH";


           // LblEmail.Text = $"Currently {LocalDBManager.Instance.GetDBSetting("email")?.Value}";

            //Todo: clean up on 2019 01 18
            if (LocalDBManager.Instance.GetDBSetting("SetStyle") == null)
                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
                LocalDBManager.Instance.SetDBSetting("QuickMode", "false");

            if (LocalDBManager.Instance.GetDBSetting("BackOffSet") == null)
                LocalDBManager.Instance.SetDBSetting("BackOffSet", "true");
            if (LocalDBManager.Instance.GetDBSetting("1By1Side") == null)
                LocalDBManager.Instance.SetDBSetting("1By1Side", "true");

            
            Switch1By1Side.IsToggled = LocalDBManager.Instance.GetDBSetting("1By1Side")?.Value == "true";
            if (Switch1By1Side.IsToggled)
                Lbl1By1sides.Text = "For bilateral exercises like concentration curls, you will do all sets for arm 1, then all sets for arm 2";
            else
                Lbl1By1sides.Text = "For bilateral exercises like concentration curls, you will do arm 1, arm 2, rest, arm 1... and so on";
            BackOffSetSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("BackOffSet")?.Value == "true";

            StrengthSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("StrengthPhase")?.Value == "true";

            if (LocalDBManager.Instance.GetDBSetting("Cardio") == null)
                LocalDBManager.Instance.SetDBSetting("Cardio", "false");
            CardioSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("Cardio")?.Value == "true";

            try
            {
                List<string> setstyles = new List<string>();
                setstyles.Add("Rest-pause");
                setstyles.Add("Normal");
                setstyles.Add("Pyramid");
                setstyles.Add("Reverse pyramid");
                SetStylePicker.ItemsSource = setstyles;

                if (LocalDBManager.Instance.GetDBSetting("IsRPyramid") != null && LocalDBManager.Instance.GetDBSetting("IsRPyramid")?.Value == "true")
                {
                    // RPyramid();
                    LblRestPauseSets.Text = "Reps decrease from set to set";
                    SetStylePicker.SelectedItem = setstyles[2];
                }                      
                else if (LocalDBManager.Instance.GetDBSetting("SetStyle")?.Value == "Normal")
                {
                    if (LocalDBManager.Instance.GetDBSetting("IsPyramid") != null && LocalDBManager.Instance.GetDBSetting("IsPyramid")?.Value == "true")
                    {
                        //Pyramid();
                        LblRestPauseSets.Text = "Reps increase from set to set";
                        SetStylePicker.SelectedItem = setstyles[3];
                    }
                    else
                    {
                        SetStylePicker.SelectedItem = setstyles[1];
                        //Normal();
                        LblRestPauseSets.Text = "Takes more time, but builds more strength";
                    }
                }
                else if (LocalDBManager.Instance.GetDBSetting("SetStyle")?.Value == "RestPause")
                {
                    //RestPause();
                    LblRestPauseSets.Text = "Harder, but makes your workouts ~59% faster";
                    SetStylePicker.SelectedItem = setstyles[0];
                }
            }
            catch (Exception ex)
            {

            }
            
            //Workout Days
            if (LocalDBManager.Instance.GetDBSetting("RecommendedReminder")?.Value == "true")
            {
                RecommendedReminderMode();
            }
            else
            {
                if (!string.IsNullOrEmpty( LocalDBManager.Instance.GetDBSetting("ReminderDays")?.Value))
                {
                    if (LocalDBManager.Instance.GetDBSetting("ReminderDays").Value.Contains("1"))
                    {
                        CustomReminderModeUI();
                    }
                    else
                        NoReminderMode();
                }
                else
                {
                    NoReminderMode();
                }
            }

            //NormalSetsSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("SetStyle").Value == "Normal";
            //RestPauseSetsSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("SetStyle").Value == "RestPause";
            if (LocalDBManager.Instance.GetDBSetting("QuickMode")?.Value == "true" )
            {
                Quick();
                if (string.IsNullOrEmpty(SetEntry.Text))
                {
                    SetEntry.Text = "2";
                    LocalDBManager.Instance.SetDBSetting("WorkSetCount", "2");
                    LblCustomWorkset.Text = $"Workouts last about {15 * 2}-{20 * 2} min";
                }
            }
            else if (LocalDBManager.Instance.GetDBSetting("QuickMode")?.Value == "null")
            {
                Quick();
                if (string.IsNullOrEmpty(SetEntry.Text))
                {
                    LocalDBManager.Instance.SetDBSetting("WorkSetCount", "3");
                    SetEntry.Text = "3";
                    LblCustomWorkset.Text = $"Workouts last about {15 * 3}-{20 * 3} min";
                }
            }
            else
                NormalMode();
            if (Custom.IsToggled)
            {
                LoadCustomReps();
            }
            else
                CustomRepsStack.IsVisible = false;

            if (LocalDBManager.Instance.GetDBSetting("BackgroundImage") != null)
            {
                if (LocalDBManager.Instance.GetDBSetting("BackgroundImage").Value == "Background2.png")
                {
                    MaleSwitch.IsToggled = true;
                }
                else if (LocalDBManager.Instance.GetDBSetting("BackgroundImage").Value == "BackgroundFemale.png")
                {
                    FemaleSwitch.IsToggled = true;
                }
                else if (LocalDBManager.Instance.GetDBSetting("BackgroundImage").Value == "DrMuscleLogo.png")
                {
                    MuscleLogoSwitch.IsToggled = true;
                }
                else
                {
                    NoImageSwitch.IsToggled = true;
                }
            }

            if (LocalDBManager.Instance.GetDBSetting("GymEquipment") == null || LocalDBManager.Instance.GetDBSetting("HomeEquipment") == null || LocalDBManager.Instance.GetDBSetting("OtherEquipment") == null)
                LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");

            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");


            //Home
            if (LocalDBManager.Instance.GetDBSetting("HomeMainEquipment") == null)
                LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("HomeChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("HomeChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("HomeDumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("HomePlate") == null)
                LocalDBManager.Instance.SetDBSetting("HomePlate", "true");

            if (LocalDBManager.Instance.GetDBSetting("HomePully") == null)
                LocalDBManager.Instance.SetDBSetting("HomePully", "true");

            //Other
            if (LocalDBManager.Instance.GetDBSetting("OtherMainEquipment") == null)
                LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("OtherChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("OtherChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("OtherDumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("OtherPlate") == null)
                LocalDBManager.Instance.SetDBSetting("OtherPlate", "true");

            if (LocalDBManager.Instance.GetDBSetting("OtherPully") == null)
                LocalDBManager.Instance.SetDBSetting("OtherPully", "true");


            GymEquipmentUnselected();
            HomeEquipmentUnSelected();
            OtherEquipmentUnselected();

            
            IsGymEnabled = IsHomeEnabled = IsOtherEnabled = false;
            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
            {
                GymEquipment();
                IsGymEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true";
            }
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
            {
                HomeEquipment();
                IsHomeEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true";
            }
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
            {
                OtherEquipment();
                IsOtherEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true";
            }

            //emailReminderSwitch
            if (LocalDBManager.Instance.GetDBSetting("IsEmailReminder") == null)
                LocalDBManager.Instance.SetDBSetting("IsEmailReminder", "true");
            emailReminderSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("IsEmailReminder").Value == "true";
            StackEmailReminder.IsVisible = emailReminderSwitch.IsToggled;

            hoursBeforeEntry.Text = LocalDBManager.Instance.GetDBSetting("ReminderHours")?.Value;
            if (LocalDBManager.Instance.GetDBSetting("Reminder5th") == null)
                LocalDBManager.Instance.SetDBSetting("Reminder5th", "true");
            ReminderSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("Reminder5th")?.Value == "true";

            if (LocalDBManager.Instance.GetDBSetting("PlatesKg") == null || LocalDBManager.Instance.GetDBSetting("PlatesLb") == null)
            {
                var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", kgString);

                var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", lbString);
            }
            if (LocalDBManager.Instance.GetDBSetting("HomePlatesKg") == null || LocalDBManager.Instance.GetDBSetting("HomePlatesLb") == null)
            {
                var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", kgString);

                var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", lbString);
            }

            if (LocalDBManager.Instance.GetDBSetting("MobilityRep") == null)
                LocalDBManager.Instance.SetDBSetting("MobilityRep", "10");

            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("MobilityRep")?.Value))
            {
                MobilityRepsEntry.Text = "";
            }
            else
            {
                MobilityRepsEntry.Text = LocalDBManager.Instance.GetDBSetting("MobilityRep")?.Value;
            }
            if (LocalDBManager.Instance.GetDBSetting("IsMobility")?.Value == "true")
            {
                flexiblitySwitch.IsToggled = true;
                LblFlexibilityExplainer.IsVisible = false;
            }
            else
            {
                flexiblitySwitch.IsToggled = false;
                LblFlexibilityExplainer.IsVisible = true;
            }
                SetMobilityLevel();

            if (App.IsMealPlan || App.IsV1User)
            {
                string source = Device.RuntimePlatform.Equals(Device.Android) ? "Google" : "Apple";
                if (App.IsWebsite)
                    source = "our website";
                if (App.IsGoogle)
                    source = "Google";
                if (App.IsApple)
                    source = "Apple";
                lblDeleteDesc.Text = $"Deletes everything except your subscription via {source}. Cancel before you continue.";
            }
            else
            {
                lblDeleteDesc.Text = "Deletes everything.";
            }
            //LocalDBManager.Instance.SetDBSetting("IsExerciseQuickMode", uim.IsExerciseQuickMode == null ? null : uim.IsExerciseQuickMode == false ? "false" : "true");

            ExerciseQuickSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("IsExerciseQuickMode")?.Value == "true" ? true : false;

            if (LocalDBManager.Instance.GetDBSetting("DumbbellKg") == null || LocalDBManager.Instance.GetDBSetting("DumbbellLb") == null)
            {
                var kgString = "50_2_True|47.5_2_True|45_2_True|42.5_2_True|40_2_True|37.5_2_True|35_2_True|32.5_2_True|30_2_True|27.5_2_True|25_2_True|22.5_2_True|20_2_True|17.5_2_True|15_2_True|12.5_2_True|10_2_True|7.5_2_True|5_2_True|2.5_2_True|1_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", kgString);

                var lbString = "90_2_True|85_2_True|80_2_True|75_2_True|70_2_True|65_2_True|60_2_True|55_2_True|50_2_True|45_2_True|40_2_True|35_2_True|30_2_True|25_2_True|20_2_True|15_2_True|12_2_True|10_2_True|8_2_True|5_2_True|3_2_True|2_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", lbString);
            }

            if (LocalDBManager.Instance.GetDBSetting("PulleyKg") == null || LocalDBManager.Instance.GetDBSetting("PulleyLb") == null)
            {
                var kgString = "5_20_True|1.5_2_True";
                var lbString = "10_20_True|5_2_True|2.5_2_True";

                LocalDBManager.Instance.SetDBSetting("PulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", kgString);

                LocalDBManager.Instance.SetDBSetting("PulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", lbString);
            }

            if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
            {
                GeneratePlatesArray("PlatesKg");
                GeneratePlatesArray("HomePlatesKg");
                GeneratePlatesArray("OtherPlatesKg");

                GenerateDumbbellArray("DumbbellKg");
                GenerateDumbbellArray("HomeDumbbellKg");
                GenerateDumbbellArray("OtherDumbbellKg");

                GeneratePulleyArray("PulleyKg");
                GeneratePulleyArray("HomePulleyKg");
                GeneratePulleyArray("OtherPulleyKg");
            }
            else
            {
                GeneratePlatesArray("PlatesLb");
                GeneratePlatesArray("HomePlatesLb");
                GeneratePlatesArray("OtherPlatesLb");

                GenerateDumbbellArray("DumbbellLb");
                GenerateDumbbellArray("HomeDumbbellLb");
                GenerateDumbbellArray("OtherDumbbellLb");

                GeneratePulleyArray("PulleyLb");
                GeneratePulleyArray("HomePulleyLb");
                GeneratePulleyArray("OtherPulleyLb");
            }

           
            //if (CurrentLog.Instance.IsFromExperienceLifter)
            //{
            //    CurrentLog.Instance.IsFromExperienceLifter = false;
            //    CurrentLog.Instance.IsSettingsVisited = true;
            //    ContinueOnboarding();
            //    return;
            //}

            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                    return;
                var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;

                var features = "";
                var learnMore = "";
                if (workouts != null && workouts.GetUserProgramInfoResponseModel != null)
                {
                    var upi = workouts.GetUserProgramInfoResponseModel;
                    var profileCount = (LocalDBManager.Instance.GetDBSetting("Equipment")?.Value == "true" ? 1 : 0) + (LocalDBManager.Instance.GetDBSetting("HomeMainEquipment")?.Value == "true" ? 1 : 0) + (LocalDBManager.Instance.GetDBSetting("OtherMainEquipment")?.Value == "true" ? 1 : 0);
                    var profileName = "";
                    if (profileCount > 1)
                    {

                        if (IsGymEnabled)
                            profileName = "Gym";
                        if (IsHomeEnabled)
                            profileName = "Home";
                        if (IsOtherEnabled)
                            profileName = "Custom";
                    }
                    if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null)
                    {
                        //lblProgram.Text = $"Program: {upi.RecommendedProgram.Label}";
                        //lblProgramName.Text = $"{upi.RecommendedProgram.Label}";
                        var remainWorkout = Math.Abs(upi.RecommendedProgram.RequiredWorkoutToLevelUp - upi.RecommendedProgram.RemainingToLevelUp ?? 0);

                        lblLevel.Text = $"Next program in: {upi.RecommendedProgram.RemainingToLevelUp.ToString()} {(upi.RecommendedProgram.RemainingToLevelUp>1 ? "workouts" : "workout")}";
                        lblWorkout.Text = $"Next workout: {upi.NextWorkoutTemplate.Label}";
                        if (upi.RecommendedProgram.RemainingToLevelUp > 0)
                        {
                            lblWorkoutDoneCount.IsVisible = true;
                            lblLevel.IsVisible = true;
                        }
                        else
                        {
                            lblWorkoutDoneCount.IsVisible = false;
                            lblLevel.IsVisible = false;
                        }
                        if (profileCount>1)
                            lblWorkoutDoneCount.Text = $"Workouts done in program: {remainWorkout}";
                        else
                            lblWorkoutDoneCount.Text = $"Workouts done: {remainWorkout}";
                        LocalDBManager.Instance.SetDBSetting("remain", upi.RecommendedProgram.RemainingToLevelUp.ToString());
                    }
                    else
                    {
                        LblProgramDesc.Text = $"{AppResources.YourProgramNotSetUp}";
                        lblWorkout.Text = $"{AppResources.TodaysWorkoutNotSetUp}";
                        if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
                        {
                            try
                            {
                                long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId")?.Value);
                                long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId")?.Value);
                                upi = new GetUserProgramInfoResponseModel()
                                {
                                    NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel")?.Value },
                                    RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout")?.Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout")?.Value) },
                                };
                                //lblProgramName.Text = $"{upi.RecommendedProgram.Label}";
                                lblWorkout.Text = $"Next workout: {upi.NextWorkoutTemplate.Label}";
                                //lblLevel.Text = $"{upi.RecommendedProgram.RemainingToLevelUp} workouts before level up";
                                lblLevel.IsVisible = true;
                                lblWorkoutDoneCount.IsVisible = true;
                                //lblLevel.Text = $"Level up (new exercises): {(upi.RecommendedProgram.RequiredWorkoutToLevelUp - upi.RecommendedProgram.RemainingToLevelUp).ToString()}/{upi.RecommendedProgram.RequiredWorkoutToLevelUp.ToString()} workouts";
                                var remainWorkout = Math.Abs(upi.RecommendedProgram.RequiredWorkoutToLevelUp - upi.RecommendedProgram.RemainingToLevelUp ?? 0);

                                lblLevel.Text = $"Next program in: {upi.RecommendedProgram.RemainingToLevelUp.ToString()} {(upi.RecommendedProgram.RemainingToLevelUp > 1 ? "workouts" : "workout")}";
                                if (profileCount > 1)
                                    lblWorkoutDoneCount.Text = $"Workouts done in program: {remainWorkout}";
                                else
                                    lblWorkoutDoneCount.Text = $"Workouts done: {remainWorkout}";
                            }
                            catch (Exception ex)
                            {

                            }

                        }

                    }
                    
                    if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null && upi.NextWorkoutTemplate.IsSystemExercise)
                    {
                       
                        try
                        {
                            var workoutLogAverage = ((App)Application.Current).UserWorkoutContexts.workouts;

                            if (!string.IsNullOrEmpty(upi.RecommendedProgram.Label))
                                features = $"You're on the {upi.RecommendedProgram.Label} program. ";
                            
                            if (upi.NextWorkoutTemplate.IsSystemExercise)
                            {
                                try
                                {

                                    if (workoutLogAverage != null && workoutLogAverage.HistoryExerciseModel != null)
                                    {
                                        features += $"That's based on your past experience";
                                        if (workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted > 0)
                                        {
                                            var w = workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted <= 1 ? "workout" : "workouts";
                                            features += $" and {workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted} {w} recorded.";
                                        }

                                    }

                                }
                                catch (Exception)
                                {

                                }
                            }
                            if (profileCount > 1)
                                features = $"{profileName} equipment profile loaded. On that profile, you're on the {upi.RecommendedProgram.Label} program.";
                            learnMoreProgram = learnMore;
                            LblProgramDesc.IsVisible = true;
                            LblProgramDesc.Text = features;
                        }
                        catch (Exception)
                        {

                        }
                        //if (lblProgramName.Text.Contains("Powerlifting"))
                        //{
                        //    LblProgramDesc.IsVisible = false;
                        //    LearnMoreAbouProgramLink.IsVisible = false;
                        //}
                    }
                    else if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null && !upi.NextWorkoutTemplate.IsSystemExercise)
                    {
                        if (profileCount>1)
                            features = $"{profileName} equipment profile loaded. On that profile, you're on the {upi.RecommendedProgram.Label} program. This is a custom program.";
                        else
                        features = $"You're on the {upi.RecommendedProgram.Label} program. This is a custom program.";
                        
                        learnMoreProgram = "FAQ";

                        LblProgramDesc.IsVisible = true;
                        LblProgramDesc.Text = features;

                    }
                    else
                    {
                        lblLevel.IsVisible = false;
                        LblProgramDesc.IsVisible = false;
                    }
                }

                else
                {
                    LblProgramDesc.Text = $"{AppResources.YourProgramNotSetUp}";
                    if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
                        LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
                    {
                        try
                        {
                            long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
                            long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);
                            var upi = new GetUserProgramInfoResponseModel()
                            {
                                NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value },
                                RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
                            };
                            lblLevel.IsVisible = true;
                            LblProgramDesc.Text = $"{AppResources.Program}: {upi.RecommendedProgram.Label}";
                            lblWorkout.Text = $"Next workout: {upi.NextWorkoutTemplate.Label}";
                            lblLevel.Text = $"Level up (new exercises): {upi.RecommendedProgram.RemainingToLevelUp} workouts";

                            if (!string.IsNullOrEmpty(upi.RecommendedProgram.Label))
                                features = $"You're on the {upi.RecommendedProgram.Label} program.";

                            if (upi.NextWorkoutTemplate.IsSystemExercise)
                            {
                                LblProgramDesc.IsVisible = true;
                                LblProgramDesc.Text = features;
                            }
                        }
                        catch (Exception ex)
                        {

                        }

                    }
                    else
                    {

                        lblWorkout.Text = $"{AppResources.TodaysWorkoutNotSetUp}";
                        lblLevel.IsVisible = false;
                        LblProgramDesc.IsVisible = true;
                        lblLevel.Text = $"";
                        LblProgramDesc.Text = $"{AppResources.YourProgramNotSetUp}";
                    }

                }

            }
            catch (Exception ex)
            {

            }

            IsOnAppeatingFinished = true;
            if (CurrentLog.Instance.IsFromExperienceLifter)
                WorkLifterPopup();
            //if (Config.ShowSettingsPopup == false)
            //{
            //    if (App.IsSettingsPopup)
            //        return;
            //    App.IsSettingsPopup = true;
            //    ConfirmConfig ShowPopUp = new ConfirmConfig()
            //    {
            //        Title = "Settings",
            //        Message = "Customize your reps, sets, body part priority, cardio, and more.",
            //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //        OkText = AppResources.GotIt,
            //        CancelText = AppResources.RemindMe,
            //        OnAction = async (bool ok) =>
            //        {
            //            if (ok)
            //            {
            //                Config.ShowSettingsPopup = true;
            //            }
            //            else
            //            {
            //                Config.ShowSettingsPopup = false;
            //            }
            //        }
            //    };
            //    //await Task.Delay(100);
            //    UserDialogs.Instance.Confirm(ShowPopUp);

            //}
        }

        async void NotifyGloabalSettingsChanged()
        {
            MessagingCenter.Send<GlobalSettingsChangeMessage>(new GlobalSettingsChangeMessage() { IsDisappear = true }, "GlobalSettingsChangeMessage");
        }
        async void WorkLifterPopup()
        {
            // var s = setList.Where(x => x.IsFirstWorkSet).FirstOrDefault();
            var min = LocalDBManager.Instance.GetDBSetting("repsminimum")?.Value;
            var max = LocalDBManager.Instance.GetDBSetting("repsmaximum")?.Value;
            var text = "";
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("Demoreprange")?.Value == "BuildMuscle")
                {
                    text = "build muscle";
                }
                else if (LocalDBManager.Instance.GetDBSetting("Demoreprange")?.Value == "BuildMuscleBurnFat")
                {
                    text = "build muscle and burn fat";
                }
                else if (LocalDBManager.Instance.GetDBSetting("Demoreprange")?.Value == "FatBurning")
                {
                    text = "burn fat";
                }
            }
            catch (Exception ex)
            {

            }
            await UserDialogs.Instance.AlertAsync(new AlertConfig()
            {
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                Message = $"Because your goal is to {text}.",
                Title = $"2-6 work sets of {min}-{max} reps",
                OkText = AppResources.GotIt
            });

            //CurrentLog.Instance.WalkThroughCustomTipsPopup = true;
            //await PagesFactory.PushAsync<LearnPage>();
            PagesFactory.PopAsync();
            if (Device.RuntimePlatform.Equals(Device.Android))
                MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage(), "SignupFinishMessage");

            CurrentLog.Instance.IsFromExperienceLifter = false;
            Config.ShowSettingsPopup = false;

        }
        async void ChangeWorkoutClicked(object sender, EventArgs e)
        {
            if (IsFromWorkoutPage())
            {
                await Navigation.PopToRootAsync(false);
                await PagesFactory.PushAsync<SettingsPage>();
            }
            await PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
        }
        async void ChangeEquipmentClicked(object sender, EventArgs e)
        {
            await PagesFactory.PushAsync<EquipmentSettingsPage>();
        }

        private bool IsFromWorkoutPage()
        {
            var isMePage = false;
            foreach (var item in Navigation.NavigationStack)
            {
                if (item is ChooseDrMuscleOrCustomPage || item is KenkoChooseYourWorkoutExercisePage)
                {
                    isMePage = true;
                    break;
                }
            }
            return isMePage;
        }

        private async void ContinueOnboarding()
        {
            await UserDialogs.Instance.AlertAsync(new AlertConfig()
            {
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                Title = $"Settings",
                Message = "Customize your reps, sets, body part priority, cardio, and more.",
                OkText = AppResources.GotIt
            });

            //await UserDialogs.Instance.AlertAsync(, , "Got it");
            //long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
            //long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);

            //var upi = new GetUserProgramInfoResponseModel()
            //{
            //    NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value },
            //    RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
            //};
            //if (upi != null)
            //{
            //    WorkoutTemplateModel nextWorkout = upi.NextWorkoutTemplate;
            //    if (upi.NextWorkoutTemplate.Exercises == null || upi.NextWorkoutTemplate.Exercises.Count() == 0)
            //    {
            //        try
            //        {
            //            nextWorkout = await DrMuscleRestClient.Instance.GetUserCustomizedCurrentWorkout(workoutTemplateId);
            //            //nextWorkout = w.Workouts.First(ww => ww.Id == upi.NextWorkoutTemplate.Id);
            //        }
            //        catch (Exception ex)
            //        {
            //            
            //            return;
            //        }

            //    }
            //    if (nextWorkout != null)
            //    {
            //        CurrentLog.Instance.CurrentWorkoutTemplate = nextWorkout;
            //        CurrentLog.Instance.WorkoutTemplateCurrentExercise = nextWorkout.Exercises.First();
            //        CurrentLog.Instance.WorkoutStarted = true;
            //        if (Device.RuntimePlatform.Equals(Device.Android))
            //        {
            //            await PagesFactory.PopToRootThenPushAsync<KenkoDemoWorkoutExercisePage>(true);
            //            App.IsDemoProgress = false;
            //            App.IsWelcomeBack = true;
            //            LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
            //            CurrentLog.Instance.Exercise1RM.Clear();
            //            await PopupNavigation.Instance.PushAsync(new ReminderPopup());
            //            Device.BeginInvokeOnMainThread(async () =>
            //            {
            //                await PagesFactory.PopToRootAsync(true);
            //            });
            //        }
            //        else
            //        {

            //            App.IsDemoProgress = false;
            //            App.IsWelcomeBack = true;
            //            LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
            //            CurrentLog.Instance.Exercise1RM.Clear();
            //            await PopupNavigation.Instance.PushAsync(new ReminderPopup());
            //            await PagesFactory.PopToRootMoveAsync(true);
            //            await PagesFactory.PushMoveAsync<KenkoDemoWorkoutExercisePage>();
            //        }

            //    }
            //    else
            //    {
            //        await PagesFactory.PopToRootAsync(true);
            //    }
            //}

        }

        protected override async void OnDisappearing()
        {
            base.OnDisappearing();
            MessagingCenter.Send<GlobalSettingsChangeMessage>(new GlobalSettingsChangeMessage() { IsDisappear = false }, "GlobalSettingsChangeMessage");
            if (IsEquipmentEdited)
            {
                await SetUserEquipmentSettingsWithLoader();
                IsEquipmentEdited = false;
                Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage(), "UpdatedWorkoutMessage");
            }

            if (_increments != null)
                LocalDBManager.Instance.SetDBSetting("workout_increments", _increments.Kg.ToString().Replace(",", "."));

        }

        //private void RestPause()
        //{
        //    LblRestPauseSets.Text = AppResources.RestPauseSetsAreHarderButMakeYourWorkouts59Faster;
        //    BtnNormal.BackgroundColor = Color.Transparent;
        //    NormalGradient.BackgroundColor = GetTransparentGradient();
            
        //    RestPauseGradient.BackgroundColor = GetBlueGradient();
        //    BtnNormal.TextColor = Color.FromHex("#0C2432");
        //    BtnRestPause.TextColor = Color.White;

        //    BtnRPyramid.TextColor = Color.FromHex("#0C2432"); ;
        //    RPyramidGradient.BackgroundColor = GetTransparentGradient();

        //    BtnPyramid.TextColor = Color.FromHex("#0C2432");
        //    PyramidGradient.BackgroundColor = GetTransparentGradient();
            
        //    BtnPyramid.BackgroundColor = Color.Transparent;
        //}

        public async void BtnRestPauseClicked(object sender, EventArgs args)
        {
            UpdateSetSetyle(false);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
            //RestPause();


        }
        //private void Normal()
        //{
        //    LblRestPauseSets.Text = "Normal sets take more time, but build more strength";
        //    BtnRestPause.BackgroundColor = Color.Transparent;
        //    //BtnKg.BackgroundColor = Color.FromHex("#5CD196");
        //    BtnNormal.TextColor = Color.White;
        //    BtnRestPause.TextColor = Color.FromHex("#0C2432");
        //    NormalGradient.BackgroundColor = GetBlueGradient();
        //    RestPauseGradient.BackgroundColor = GetTransparentGradient();

        //    BtnRPyramid.TextColor = Color.FromHex("#0C2432"); ;
        //    RPyramidGradient.BackgroundColor = GetTransparentGradient();

        //    BtnPyramid.TextColor = Color.FromHex("#0C2432");
        //    PyramidGradient.BackgroundColor = GetTransparentGradient();
        //    BtnPyramid.BackgroundColor = Color.Transparent;
        //}
        public async void BtnNormalClicked(object sender, EventArgs args)
        {

            UpdateSetSetyle(true);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");

            //Normal();

        }

        //private void RPyramid()
        //{
        //    LblRestPauseSets.Text = "Pyramid sets decrease reps from set to set";

        //    BtnRestPause.BackgroundColor = Color.Transparent;
        //    BtnRestPause.TextColor = Color.FromHex("#0C2432");
        //    BtnNormal.TextColor = Color.FromHex("#0C2432");
        //    NormalGradient.BackgroundColor = GetTransparentGradient();
        //    RestPauseGradient.BackgroundColor = GetTransparentGradient();

        //    BtnPyramid.TextColor = Color.FromHex("#0C2432"); ;
        //    PyramidGradient.BackgroundColor = GetTransparentGradient();

        //    BtnRPyramid.TextColor = Color.White; ;
        //    RPyramidGradient.BackgroundColor = GetBlueGradient();

        //}
        void BtnRPyramid_Clicked(System.Object sender, System.EventArgs e)
        {

            UpdatePyramidSetStyle(true);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
            //RPyramid();
        }

        //private void Pyramid()
        //{
        //    LblRestPauseSets.Text = "Reverse pyramid sets increase reps from set to set";

        //    BtnRestPause.BackgroundColor = Color.Transparent;
        //    BtnRestPause.TextColor = Color.FromHex("#0C2432");
        //    BtnNormal.TextColor = Color.FromHex("#0C2432");
        //    NormalGradient.BackgroundColor = GetTransparentGradient();
        //    RestPauseGradient.BackgroundColor = GetTransparentGradient();

        //    BtnRPyramid.TextColor = Color.FromHex("#0C2432"); ;
        //    RPyramidGradient.BackgroundColor = GetTransparentGradient();


        //    BtnPyramid.TextColor = Color.White; ;
        //    PyramidGradient.BackgroundColor = GetBlueGradient();

        //}
        void BtnPyramid_Clicked(System.Object sender, System.EventArgs e)
        {

            UpdateSetSetyle(null);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "true");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
            //Pyramid();
        }


        /// <summary>
        /// Workout Mode options
        /// </summary>

        private void Turbo()
        {
            LblWorkoutType.Text = "Overrides other set settings";
            StackWorkset.IsVisible = false;
            SaveSetCountButton.IsVisible = false;
            BtnQuick.BackgroundColor = Color.Transparent;
            QuickGradient.BackgroundColor = GetTransparentGradient();
            
            BtnQuick.TextColor = Color.FromHex("#0C2432");
            

            BtnNormalMode.TextColor = Color.FromHex("#0C2432");
            NormalModeGradient.BackgroundColor = GetTransparentGradient();
            BtnNormalMode.BackgroundColor = Color.Transparent;
        }


        private async void SetupWorkoutMode(bool? isToggled)
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserQuickMode(new UserInfosModel()
            {
                IsQuickMode = isToggled
            });
            
            LocalDBManager.Instance.ResetReco();
        }

        private async void SetupUserRecommended(bool? isenabled)
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserRecommendedReminder(new UserInfosModel()
            {
                IsRecommendedReminder = isenabled
            });
            
        }

        public async void BtnTurboClicked(object sender, EventArgs args)
        {

            SetupWorkoutMode(true);
            //LocalDBManager.Instance.SetDBSetting("QuickMode", "true");
            Turbo();


        }
        private void Quick()
        {

            LblWorkoutType.IsVisible = false;
                //LblCustomWorkset.Text = "Workouts last about [15-20 min x nb of sets; e.g. 30-40 min for 2 sets, 45-60 min for 3 sets, etc.]";
            StackWorkset.IsVisible = true;
            SaveSetCountButton.IsVisible = true;
            
            BtnQuick.TextColor = Color.White;
            
            QuickGradient.BackgroundColor = GetBlueGradient();
            

            BtnNormalMode.TextColor = Color.FromHex("#0C2432");
            NormalModeGradient.BackgroundColor = GetTransparentGradient();
            BtnNormalMode.BackgroundColor = Color.Transparent;
        }
        public async void BtnQuickClicked(object sender, EventArgs args)
        {

            SetupWorkoutMode(null);
            if (string.IsNullOrEmpty(SetEntry.Text))
            {
                SetEntry.Text = "3";
                LocalDBManager.Instance.SetDBSetting("WorkSetCount", "3");
                SaveSetCountButton_Clicked(SaveSetCountButton, EventArgs.Empty);
                LblCustomWorkset.Text = $"Workouts last about {15 * 3}-{20 * 3} min";
            }
            LocalDBManager.Instance.SetDBSetting("QuickMode", "null");

            Quick();

        }
        private void NormalMode()
        {
            //LblWorkoutType.Text = "Sets change every workout based on your progress—workouts last 30-90 min";
            LblWorkoutType.Text = "Change every workout based on your progress";
            LblWorkoutType.IsVisible = true;
            StackWorkset.IsVisible = false;
            SaveSetCountButton.IsVisible = false;
            
            
            BtnQuick.TextColor = Color.FromHex("#0C2432");
            QuickGradient.BackgroundColor = GetTransparentGradient();

            BtnNormalMode.TextColor = Color.White; ;
            NormalModeGradient.BackgroundColor = GetBlueGradient();

        }
        void BtnNormalMode_Clicked(System.Object sender, System.EventArgs e)
        {

            SetupWorkoutMode(false);

            LocalDBManager.Instance.SetDBSetting("QuickMode", "false");

            NormalMode();
        }

        void BtnRecommended_Clicked(System.Object sender, System.EventArgs e)
        {

            SetupUserRecommended(true);

            LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "true");
            RecommendedReminderMode();
            IAlarmAndNotificationService alarmAndNotificationService = new AlarmAndNotificationService();
            alarmAndNotificationService.CancelNotification(101);
            alarmAndNotificationService.CancelNotification(102);
            alarmAndNotificationService.CancelNotification(103);
            alarmAndNotificationService.CancelNotification(104);
            alarmAndNotificationService.CancelNotification(105);
            alarmAndNotificationService.CancelNotification(106);
            alarmAndNotificationService.CancelNotification(107);
            alarmAndNotificationService.CancelNotification(108);
        }

        private void RecommendedReminderMode()
        {
            LblReminderDesc.Text = "";

            try
            {
                var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;

                if (workouts?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.IsSystemExercise == true)
                {

                
                //If Default and Age
                int age = 40;
                bool isApproxAge = true;
                if (LocalDBManager.Instance.GetDBSetting("Age") != null && LocalDBManager.Instance.GetDBSetting("Age").Value != null)
                {
                    age = int.Parse(LocalDBManager.Instance.GetDBSetting("Age").Value);
                }
               
                if (workouts.Sets != null)
                {

                }
                else
                {
                    workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
                }

                var programName = workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label;
                var ageText = "";
                var xDays = 0;
                if ((programName.ToLower().Contains("push/pull/legs") && age < 51) || programName.ToLower().Contains("muscle split"))
                {
                    xDays = 6;
                }
                else if (programName.ToLower().Contains("split"))
                {
                    if (age < 30)
                        xDays = 4;
                    else if (age >= 30 && age <= 50)
                        xDays = 4;
                    else
                        xDays = 3;
                }
                else if (programName.ToLower().Contains("bodyweight") ||
programName.ToLower().Contains("mobility") || programName.ToLower().Contains("full-body") || programName.ToLower().Contains("bands") || programName.ToLower().Contains("powerlifting") || programName.ToLower().Contains("abs, legs & glutes"))
                {
                    if (age < 30)
                        xDays = 4;
                    else if (age >= 30 && age <= 50)
                        xDays = 3;
                    else
                        xDays = 2;
                }

                    if (xDays != 0)
                    {
                        //
                        if (xDays == 2)
                        {
                            LblReminderDesc.Text = $"Monday, and Thursday.";
                               
                        }
                        if (xDays == 3)
                        {
                            LblReminderDesc.Text = $"Monday, Wednesday, and Friday.";
                        }
                        if (xDays == 4)
                        {
                            LblReminderDesc.Text = $"Monday, Tuesday ,Thursday, and Friday.";
                        }
                        if (xDays == 5)
                        {
                            LblReminderDesc.Text = $"Monday, Tuesday, Wednesday, Thursday, and Friday.";
                        }
                        if (xDays == 6)
                        {
                            LblReminderDesc.Text = $"Monday, Tuesday, Wednesday, Friday, Saturday, and Sunday";
                        }
                    }
                }
            else
            {
                    LblReminderDesc.Text = $"Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, and Sunday";
                }

            }
            catch (Exception ex)
            {

            }

            BtnCustomReminder.TextColor = Color.FromHex("#0C2432");
            CustomReminderGradient.BackgroundColor = GetTransparentGradient();


            BtnRecommended.TextColor = Color.White; ;
            RecommendedGradient.BackgroundColor = GetBlueGradient();

        }

        public async void BtnCustomReminderClicked(object sender, EventArgs args)
        {

            SetupUserRecommended(null);
            

            var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
            var modalPage = new ReminderPopup();
            modalPage.Disappearing += (sender2, e2) =>
            {
                waitHandle.Set();
            };
            await PopupNavigation.Instance.PushAsync(modalPage);

            await Task.Run(() => waitHandle.WaitOne());

            CustomReminderMode();

        }

        private void CustomReminderModeUI()
        {
            //if (App.workoutPerDay == 0)
            //    return;
            if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderDays").Value != null)
            {
                var strDays = LocalDBManager.Instance.GetDBSetting("ReminderDays").Value;
                if (!strDays.Contains("1"))
                {
                    NoReminderMode();
                    return;
                }
            }

            BtnCustomReminder.TextColor = Color.White;
            CustomReminderGradient.BackgroundColor = GetBlueGradient();
            BtnRecommended.TextColor = Color.FromHex("#0C2432");
            RecommendedGradient.BackgroundColor = GetTransparentGradient();
            BtnRecommended.BackgroundColor = Color.Transparent;

            LblReminderDesc.Text = "";
            if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderDays").Value != null)
            {
                var strDays = LocalDBManager.Instance.GetDBSetting("ReminderDays").Value;
                if (!strDays.Contains("1"))
                {
                    NoReminderMode();
                    LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "null");
                    return;
                }
                LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "false");
                TimeSpan timePickerSpan;
                try
                {
                    timePickerSpan = TimeSpan.Parse(LocalDBManager.Instance.GetDBSetting("ReminderTime").Value);
                }
                catch (Exception ex)
                {
                    return;
                }
                if (strDays.ToCharArray().Length == 7)
                {
                    var IsSunday = strDays[0] == '1';
                    var IsMonday = strDays[1] == '1';
                    var IsTuesday = strDays[2] == '1';
                    var IsWednesday = strDays[3] == '1';
                    var IsThursday = strDays[4] == '1';
                    var IsFriday = strDays[5] == '1';
                    var IsSaturday = strDays[6] == '1';


                    IAlarmAndNotificationService alarmAndNotificationService = new AlarmAndNotificationService();
                    alarmAndNotificationService.CancelNotification(101);
                    alarmAndNotificationService.CancelNotification(102);
                    alarmAndNotificationService.CancelNotification(103);
                    alarmAndNotificationService.CancelNotification(104);
                    alarmAndNotificationService.CancelNotification(105);
                    alarmAndNotificationService.CancelNotification(106);
                    alarmAndNotificationService.CancelNotification(107);
                    alarmAndNotificationService.CancelNotification(108);

                    string days = "";
                    if (IsMonday)
                    {
                        days = "Monday";
                    }
                    if (IsTuesday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Tuesday";
                        }
                        else
                        {
                            days += ", Tuesday";
                        }
                    }
                    if (IsWednesday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Wednesday";
                        }
                        else
                        {
                            days += ", Wednesday";
                        }
                    }
                    if (IsThursday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Thursday";
                        }
                        else
                        {
                            days += ", Thursday";
                        }
                    }
                    if (IsFriday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Friday";
                        }
                        else
                        {
                            days += ", Friday";
                        }
                    }
                    if (IsSaturday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Saturday";
                        }
                        else
                        {
                            days += ", Saturday";
                        }
                    }

                    if (IsSunday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Sunday";
                        }
                        else
                        {
                            days += ", Sunday";
                        }
                    }

                    if (!string.IsNullOrEmpty(days))
                    {
                        var array = days.Split(',');
                        if (array.Count() > 0)
                        {
                            days = days.Replace($",{array.Last()}", $", and{array.Last()}");
                        }
                        days = $"{days}.";
                    }
                    LblReminderDesc.Text = days;

                   
                }
            }

        }
        private void CustomReminderMode()
        {
            //if (App.workoutPerDay == 0)
            //    return;
            if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderDays").Value != null)
            {
                var strDays = LocalDBManager.Instance.GetDBSetting("ReminderDays").Value;
                if (!strDays.Contains("1"))
                {
                    NoReminderMode();
                    return;
                }
            }
        
            BtnCustomReminder.TextColor = Color.White;
            CustomReminderGradient.BackgroundColor = GetBlueGradient();
            BtnRecommended.TextColor = Color.FromHex("#0C2432");
            RecommendedGradient.BackgroundColor = GetTransparentGradient();
            BtnRecommended.BackgroundColor = Color.Transparent;

            LblReminderDesc.Text = "";
            if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderDays").Value != null)
            {
                var strDays = LocalDBManager.Instance.GetDBSetting("ReminderDays").Value;
                if (!strDays.Contains("1"))
                    {
                    NoReminderMode();
                    LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "null");
                    return;
                }
                LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "false");
                TimeSpan timePickerSpan;
                try
                {
                    timePickerSpan = TimeSpan.Parse(LocalDBManager.Instance.GetDBSetting("ReminderTime").Value);
                }
                catch (Exception ex)
                {
                    return;
                }
                if (strDays.ToCharArray().Length == 7)
                {
                    var IsSunday = strDays[0] == '1';
                    var IsMonday = strDays[1] == '1';
                    var IsTuesday = strDays[2] == '1';
                    var IsWednesday = strDays[3] == '1';
                    var IsThursday = strDays[4] == '1';
                    var IsFriday = strDays[5] == '1';
                    var IsSaturday = strDays[6] == '1';

                   
                    IAlarmAndNotificationService alarmAndNotificationService = new AlarmAndNotificationService();
                    alarmAndNotificationService.CancelNotification(101);
                    alarmAndNotificationService.CancelNotification(102);
                    alarmAndNotificationService.CancelNotification(103);
                    alarmAndNotificationService.CancelNotification(104);
                    alarmAndNotificationService.CancelNotification(105);
                    alarmAndNotificationService.CancelNotification(106);
                    alarmAndNotificationService.CancelNotification(107);
                    alarmAndNotificationService.CancelNotification(108);

                    string days = "";
                    if (IsMonday)
                    {
                        days = "Monday";
                    }
                    if (IsTuesday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Tuesday";
                        }
                        else
                        {
                            days += ", Tuesday";
                        }
                    }
                    if (IsWednesday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Wednesday";
                        }
                        else
                        {
                            days += ", Wednesday";
                        }
                    }
                    if (IsThursday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Thursday";
                        }
                        else
                        {
                            days += ", Thursday";
                        }
                    }
                    if (IsFriday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Friday";
                        }
                        else
                        {
                            days += ", Friday";
                        }
                    }
                    if (IsSaturday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Saturday";
                        }
                        else
                        {
                            days += ", Saturday";
                        }
                    }

                    if (IsSunday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Sunday";
                        }
                        else
                        {
                            days += ", Sunday";
                        }
                    }

                    if (!string.IsNullOrEmpty(days))
                    {
                        var array = days.Split(',');
                        if (array.Count()>0)
                        {
                            days = days.Replace($",{array.Last()}", $", and{array.Last()}");
                        }
                        days = $"{days}.";
                    }
                    LblReminderDesc.Text = days;

                    alarmAndNotificationService.CancelNotification(101);
                    alarmAndNotificationService.CancelNotification(102);
                    alarmAndNotificationService.CancelNotification(103);
                    alarmAndNotificationService.CancelNotification(104);
                    alarmAndNotificationService.CancelNotification(105);
                    alarmAndNotificationService.CancelNotification(106);
                    alarmAndNotificationService.CancelNotification(107);
                    alarmAndNotificationService.CancelNotification(108);

                    var day = 0;
                    if (IsSunday)
                    {
                        if (DayOfWeek.Sunday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Sunday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Sunday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 101, NotificationInterval.Week);
                    }
                    if (IsMonday)
                    {
                        if (DayOfWeek.Monday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Monday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Monday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 102, NotificationInterval.Week);
                    }
                    if (IsTuesday)
                    {
                        if (DayOfWeek.Tuesday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Tuesday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Tuesday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 103, NotificationInterval.Week);
                    }
                    if (IsWednesday)
                    {
                        if (DayOfWeek.Wednesday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Wednesday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Wednesday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 104, NotificationInterval.Week);
                    }
                    if (IsThursday)
                    {
                        if (DayOfWeek.Thursday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Thursday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Thursday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 105, NotificationInterval.Week);
                    }
                    if (IsFriday)
                    {
                        if (DayOfWeek.Friday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Friday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Friday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 106, NotificationInterval.Week);
                    }
                    if (IsSaturday)
                    {
                        if (DayOfWeek.Saturday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Saturday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Saturday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 107, NotificationInterval.Week);
                    }
                }
            }

        }
        private void NoReminderMode()
        {
            LblReminderDesc.Text = "";
            BtnCustomReminder.TextColor = Color.FromHex("#0C2432");
            CustomReminderGradient.BackgroundColor = Color.Transparent;
            BtnRecommended.TextColor = Color.FromHex("#0C2432");
            RecommendedGradient.BackgroundColor = Color.Transparent;
            BtnRecommended.BackgroundColor = Color.Transparent;

        }

        /// <summary>
        /// Bodypart priority
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BicepsBodypart()
        {
            //BtnChest.BackgroundColor = Color.Transparent;
            //ChestGradient.BackgroundGradientStartColor = Color.Transparent;
            //ChestGradient.BackgroundGradientEndColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientStartColor = Constants.AppThemeConstants.BlueColor;
            //BicepsGradient.BackgroundGradientEndColor = Constants.AppThemeConstants.BlueColor;
            //BtnChest.TextColor = Color.FromHex("#0C2432");
            //BtnBiceps.TextColor = Color.White;

            //BtnAbs.TextColor = Color.FromHex("#0C2432");
            //AbsGradient.BackgroundGradientStartColor = Color.Transparent;
            //AbsGradient.BackgroundGradientEndColor = Color.Transparent;
            //BtnAbs.BackgroundColor = Color.Transparent;
        }
        private void BicepsBodypartUnselected()
        {
            //BtnChest.BackgroundColor = Color.Transparent;
            //ChestGradient.BackgroundGradientStartColor = Color.Transparent;
            //ChestGradient.BackgroundGradientEndColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientStartColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientEndColor = Color.Transparent;
            //BtnChest.TextColor = Color.FromHex("#0C2432");
            //BtnBiceps.TextColor = Color.FromHex("#0C2432");

            //BtnAbs.TextColor = Color.FromHex("#0C2432");
            //AbsGradient.BackgroundGradientStartColor = Color.Transparent;
            //AbsGradient.BackgroundGradientEndColor = Color.Transparent;
            //BtnAbs.BackgroundColor = Color.Transparent;
        }

        private async void SetupBodyPartPriority(string bodypart)
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserBodypartPriority(new UserInfosModel()
            {
                BodyPartPrioriy = bodypart
            });
            
        }

        public async void BtnBicepsClicked(object sender, EventArgs args)
        {

            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
            {
                if (LocalDBManager.Instance.GetDBSetting("gender") != null && LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Biceps");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                BicepsBodypart();
            }
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        BicepsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Biceps");
                        BicepsBodypart();
                    }
                }
                else
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        BicepsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                        BicepsBodypart();
                    }
                }
            }
            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);
        }
        private void ChestBodypart()
        {
            //BtnBiceps.BackgroundColor = Color.Transparent;
            //BtnChest.TextColor = Color.White;
            //BtnBiceps.TextColor = Color.FromHex("#0C2432");
            //ChestGradient.BackgroundGradientStartColor = Constants.AppThemeConstants.BlueColor;
            //ChestGradient.BackgroundGradientEndColor = Constants.AppThemeConstants.BlueColor;
            //BicepsGradient.BackgroundGradientStartColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientEndColor = Color.Transparent;

            //BtnAbs.TextColor = Color.FromHex("#0C2432");
            //AbsGradient.BackgroundGradientStartColor = Color.Transparent;
            //AbsGradient.BackgroundGradientEndColor = Color.Transparent;
            //BtnAbs.BackgroundColor = Color.Transparent;
        }

        private void ChestBodypartUnSelected()
        {
            //BtnBiceps.BackgroundColor = Color.Transparent;
            //BtnChest.TextColor = Color.FromHex("#0C2432");
            //BtnBiceps.TextColor = Color.FromHex("#0C2432");
            //ChestGradient.BackgroundGradientStartColor = Color.Transparent;
            //ChestGradient.BackgroundGradientEndColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientStartColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientEndColor = Color.Transparent;

            //BtnAbs.TextColor = Color.FromHex("#0C2432");
            //AbsGradient.BackgroundGradientStartColor = Color.Transparent;
            //AbsGradient.BackgroundGradientEndColor = Color.Transparent;
            //BtnAbs.BackgroundColor = Color.Transparent;
        }
        public async void BtnChestClicked(object sender, EventArgs args)
        {


            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
            {
                if (LocalDBManager.Instance.GetDBSetting("gender") != null && LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Chest");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Legs");
                ChestBodypart();
            }
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Chest")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        ChestBodypartUnSelected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Chest");
                        ChestBodypart();
                    }
                }
                else
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        ChestBodypartUnSelected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Legs");
                        ChestBodypart();
                    }
                }
            }

            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);



        }
        private void AbsBodypart()
        {

            //BtnBiceps.BackgroundColor = Color.Transparent;
            //BtnBiceps.TextColor = Color.FromHex("#0C2432");
            //BtnChest.TextColor = Color.FromHex("#0C2432");
            //ChestGradient.BackgroundGradientStartColor = Color.Transparent;
            //ChestGradient.BackgroundGradientEndColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientStartColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientEndColor = Color.Transparent;

            //BtnAbs.TextColor = Color.White; ;
            //AbsGradient.BackgroundGradientStartColor = Constants.AppThemeConstants.BlueColor;
            //AbsGradient.BackgroundGradientEndColor = Constants.AppThemeConstants.BlueColor;

        }
        private void AbsBodypartUnselected()
        {

            //BtnBiceps.BackgroundColor = Color.Transparent;
            //BtnBiceps.TextColor = Color.FromHex("#0C2432");
            //BtnChest.TextColor = Color.FromHex("#0C2432");
            //ChestGradient.BackgroundGradientStartColor = Color.Transparent;
            //ChestGradient.BackgroundGradientEndColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientStartColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientEndColor = Color.Transparent;

            //BtnAbs.TextColor = Color.FromHex("#0C2432");
            //AbsGradient.BackgroundGradientStartColor = Color.Transparent;
            //AbsGradient.BackgroundGradientEndColor = Color.Transparent;

        }

        void BtnAbs_Clicked(System.Object sender, System.EventArgs e)
        {
            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
            {
                if (LocalDBManager.Instance.GetDBSetting("gender") != null && LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Glutes");
                AbsBodypart();
            }
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        AbsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                        AbsBodypart();
                    }
                }
                else
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        AbsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Glutes");
                        AbsBodypart();
                    }
                }
            }
            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);

        }
        
       

        //flexibility buttons
        public async void BtnBeginnerClicked(object sender, EventArgs args)
        {
            LocalDBManager.Instance.SetDBSetting("MobilityLevel", "Beginner");
            Beginner();
            SetUserMobilityLevel("Beginner");
        }

        private void SetMobilityLevel()
        {
            if (LocalDBManager.Instance.GetDBSetting("MobilityLevel")?.Value == "Beginner")
                Beginner();
            else if (LocalDBManager.Instance.GetDBSetting("MobilityLevel")?.Value == "Intermediate")
                Intermediate();
            else if (LocalDBManager.Instance.GetDBSetting("MobilityLevel")?.Value == "Advanced")
                Advanced();
            else
                UnSelectMobilityLevel();
        }

        void UnSelectMobilityLevel()
        {
            BeginnerGradient.BackgroundColor = Color.Transparent;
            BtnBeginner.TextColor = Color.FromHex("#0C2432");
            IntermediateGradient.BackgroundColor = Color.Transparent;
            BtnIntermediate.BackgroundColor = Color.Transparent;
            BtnIntermediate.TextColor = Color.FromHex("#0C2432");
            Btnadvanced.TextColor = Color.FromHex("#0C2432");
            AdvancedGradient.BackgroundColor = Color.Transparent;
            Btnadvanced.BackgroundColor = Color.Transparent;
        }

        void Beginner()
        {
            BeginnerGradient.BackgroundColor = GetBlueGradient();
            BtnBeginner.TextColor = Color.White;
            IntermediateGradient.BackgroundColor = GetTransparentGradient();
            BtnIntermediate.BackgroundColor = Color.Transparent;
            BtnIntermediate.TextColor = Color.FromHex("#0C2432");
            Btnadvanced.TextColor = Color.FromHex("#0C2432");
            AdvancedGradient.BackgroundColor = GetTransparentGradient();
            Btnadvanced.BackgroundColor = Color.Transparent;
            var count = GetMobilityWorkoutId("Beginner");
            if (count == 2)
                LblFlexibilityLvl.Text = $"{count} beginner exercises (2-5 min)";
            else
            {
                LblFlexibilityLvl.Text = $"{count} beginner exercises (3-6 min)";
            }
        }

        private async void SetUserMobilityLevel(string level)
        {
            var userModel = new UserInfosModel()
            {
                MobilityLevel = string.IsNullOrEmpty(level) ? LocalDBManager.Instance.GetDBSetting("MobilityLevel").Value : level,
            };
            var txt = MobilityRepsEntry.Text;
            
            if (!string.IsNullOrEmpty(txt))
            {

                userModel.MobilityRep = int.Parse(txt);
                if (txt == "0")
                    userModel.MobilityRep = null;
            }
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserMobilityLevel(userModel);
         
        }

        public async void BtnIntermediateClicked(object sender, EventArgs args)
        {
            LocalDBManager.Instance.SetDBSetting("MobilityLevel", "Intermediate");
            Intermediate();
            SetUserMobilityLevel("Intermediate");
        }

        void Intermediate()
        {
            BtnBeginner.BackgroundColor = Color.Transparent;

            BtnIntermediate.TextColor = Color.White;
            BtnBeginner.TextColor = Color.FromHex("#0C2432");
            IntermediateGradient.BackgroundColor = GetBlueGradient();
            BeginnerGradient.BackgroundColor = GetTransparentGradient();
            Btnadvanced.TextColor = Color.FromHex("#0C2432");
            AdvancedGradient.BackgroundColor = GetTransparentGradient();
            Btnadvanced.BackgroundColor = Color.Transparent;
            var count = GetMobilityWorkoutId("Intermediate");
            if (count == 5)
                LblFlexibilityLvl.Text = $"{count} intermediate exercises (8-12 min)";
            else if (count == 4)
                LblFlexibilityLvl.Text = $"{count} intermediate exercises (6-10 min)";
            else if (count == 3)
                LblFlexibilityLvl.Text = $"{count} intermediate exercises (3-6 min)";

        }
        void BtnadvancedClicked(System.Object sender, System.EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("MobilityLevel", "Advanced");
            Advanced();
            SetUserMobilityLevel("Advanced");
        }
        void Advanced()
        {
            BtnBeginner.BackgroundColor = Color.Transparent;
            BtnBeginner.TextColor = Color.FromHex("#0C2432");
            BtnIntermediate.TextColor = Color.FromHex("#0C2432");
            IntermediateGradient.BackgroundColor = GetTransparentGradient();
            BeginnerGradient.BackgroundColor = GetTransparentGradient();

            Btnadvanced.TextColor = Color.White; ;
            AdvancedGradient.BackgroundColor = GetBlueGradient();
            var count = GetMobilityWorkoutId("Advanced");
            LblFlexibilityLvl.Text = $"{count} advanced exercises (13-17 min)";
            if (count == 5)
                LblFlexibilityLvl.Text = $"{count} advanced exercises (8-12 min)";
            else if (count == 4)
                LblFlexibilityLvl.Text = $"{count} advanced exercises (6-10 min)";
            else if (count == 3)
                LblFlexibilityLvl.Text = $"{count} advanced exercises (3-6 min)";
        }
        
        long GetMobilityWorkoutId(string type)
        {
            try
            {

            
            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            var label = workouts?.GetUserProgramInfoResponseModel?.RecommendedProgram.Label;
            var workoutName = workouts?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate.Label;
            if (string.IsNullOrEmpty(label))
            {
                switch (type)
                {
                    case "Beginner" :
                        return 3;
                        break;
                    case "Intermediate" :
                        return 5;
                        break;
                    case "Advanced" :
                        return 7;
                        break;
                }
            }

            switch (type)
            {
                case "Beginner":
                    if (label.Contains("Full-Body") || label.Contains("Powerlifting"))
                        return 3;
                    if (label.Contains("Push/Pull/Legs"))
                {
                    if (workoutName.Contains("Push"))
                        return 2;
                    if (workoutName.Contains("Pull"))
                        return 2;
                    if (workoutName.Contains("Legs"))
                        return 2;
                }
                    if (label.Contains("Split"))
                    {
                        if (workoutName.Contains("Lower"))
                            return 2;
                        if (workoutName.Contains("Upper"))
                            return 2;
                    }
                    return 3;
                    break;
                case "Intermediate":
                    if (label.Contains("Full-Body") || label.Contains("Powerlifting"))
                        return 5;
                    if (label.Contains("Push/Pull/Legs"))
                {
                    if (workoutName.Contains("Push"))
                        return 3;
                    if (workoutName.Contains("Pull"))
                        return 3;
                    if (workoutName.Contains("Legs"))
                        return 3;
                }
                    if (label.Contains("Split"))
                    {
                        if (workoutName.Contains("Lower"))
                            return 3;
                        if (workoutName.Contains("Upper"))
                            return 3;
                    }
                    return 5;
                    break;
                case "Advanced":
                    if (label.Contains("Full-Body") || label.Contains("Powerlifting"))
                        return 7;
                    if (label.Contains("Push/Pull/Legs"))
                {
                    if (workoutName.Contains("Push"))
                        return 3;
                    if (workoutName.Contains("Pull"))
                        return 3;
                    if (workoutName.Contains("Legs"))
                        return 4;
                }
                    if (label.Contains("Split"))
                    {
                        if (workoutName.Contains("Lower"))
                            return 4;
                        if (workoutName.Contains("Upper"))
                            return 3;
                    }
                    return 7;
                    break;
            }
            }
            catch (Exception e)
            {
            
            }
            return 5;
        }

        private async void GeneratePulleyArray(string weightType)
        {
            try {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            

            if (weightType.ToLower().Contains("home"))
                pulleyItems1.Clear();
            else if (weightType.ToLower().Contains("other"))
                pulleyItems2.Clear();
            else
                pulleyItems.Clear();
            
            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PulleyModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    if (weightType.ToLower().Contains("home"))
                        pulleyItems1.Add(model);
                    else if (weightType.ToLower().Contains("other"))
                        pulleyItems2.Add(model);
                    else
                        pulleyItems.Add(model);
                }
            }
            if (weightType.ToLower().Contains("home"))
                pulleyItems1.Add(new PulleyModel() { Key = "Tap to enter new pulley increments", Id = -1 });
            else if (weightType.ToLower().Contains("other"))
                pulleyItems2.Add(new PulleyModel() { Key = "Tap to enter new pulley increments", Id = -1 });
            else
                pulleyItems.Add(new PulleyModel() { Key = "Tap to enter new pulley increments", Id = -1 });

                }
                catch (Exception ex)
                {

                }

        }

        private async void GenerateDumbbellArray(string weightType)
        {
            try
            {

            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;


            if (weightType.ToLower().Contains("home"))
                dumbbellItems1.Clear();
            else if (weightType.ToLower().Contains("other"))
                dumbbellItems2.Clear();
            else
                dumbbellItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new DumbellModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    if (weightType.ToLower().Contains("home"))
                        dumbbellItems1.Add(model);
                    else if (weightType.ToLower().Contains("other"))
                        dumbbellItems2.Add(model);
                    else
                        dumbbellItems.Add(model);
                }
            }
            if (weightType.ToLower().Contains("home"))
                dumbbellItems1.Add(new DumbellModel() { Key = "Tap to enter new dumbbells", Id = -1 });
            else if (weightType.ToLower().Contains("other"))
                dumbbellItems2.Add(new DumbellModel() { Key = "Tap to enter new dumbbells", Id = -1 });
            else
                dumbbellItems.Add(new DumbellModel() { Key = "Tap to enter new dumbbells", Id = -1 });


            }
            catch (Exception ex)
            {

            }

        }

        private async void GeneratePlatesArray(string weightType)
        {
            try { 
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType)?.Value;
            if (weightType.ToLower().Contains("home"))
                platesItems1.Clear();
            else if (weightType.ToLower().Contains("other"))
                platesItems2.Clear();
            else
                platesItems.Clear();
            
            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PlateModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "lb" ? "lbs" : "kg";
                    if (weightType.ToLower().Contains("home"))
                        platesItems1.Add(model);
                    else if (weightType.ToLower().Contains("other"))
                        platesItems2.Add(model);
                    else
                        platesItems.Add(model);
                }
            }
            if (weightType.ToLower().Contains("home"))
                platesItems1.Add(new PlateModel() { Key = AppResources.TapToEnterNewPlates, Id = -1 });
            else if (weightType.ToLower().Contains("other"))
                platesItems2.Add(new PlateModel() { Key = AppResources.TapToEnterNewPlates, Id = -1 });
            else
                platesItems.Add(new PlateModel() { Key = AppResources.TapToEnterNewPlates, Id = -1 });

            }
            catch (Exception ex)
            {

            }
        }

        private async void GenerateDumbbellArrayHome(string weightType)
        {
            try { 
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            dumbbellItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new DumbellModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    dumbbellItems.Add(model);

                }
            }
            platesItems.Add(new PlateModel()
            { Key = "Tap to enter new dumbbells", Id = -1 });

            }
            catch (Exception ex)
            {

            }
            //  dumbellsItems.Add(new DumbellModel() { Key = "Tap to enter new dumbbell", Id = -1 });
        }

        private async void GeneratePlatesArrayHome(string weightType)
        {
            try { 
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            platesItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PlateModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    platesItems.Add(model);
                }
            }
            platesItems.Add(new PlateModel()
            { Key = AppResources.TapToEnterNewPlates, Id = -1 });

            }
            catch (Exception ex)
            {

            }
            //  dumbellsItems.Add(new DumbellModel() { Key = "Tap to enter new dumbbell", Id = -1 });


        }

        private async void GenerateDumbbellArrayOther(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;

            dumbbellItems.Clear();
            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new DumbellModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    dumbbellItems.Add(model);
                }
            }
            platesItems.Add(new PlateModel()
            { Key = "Tap to enter new dumbbells", Id = -1 });

            // dumbellsItems.Add(new DumbellModel() { Key = "Tap to enter new dumbbell", Id = -1 });

        }
        private async void GeneratePlatesArrayOther(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            platesItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PlateModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    platesItems.Add(model);
                }
            }
            platesItems.Add(new PlateModel()
            { Key = AppResources.TapToEnterNewPlates, Id = -1 });

            // dumbellsItems.Add(new DumbellModel() { Key = "Tap to enter new dumbbell", Id = -1 });

        }

        private async Task SetUserEquipmentSettings()
        {
            var model = new EquipmentModel()
            {
                IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
                IsDumbbellEnabled = LocalDBManager.Instance.GetDBSetting("Dumbbell").Value == "true",
                IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true",
                IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
                IsHomeEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true",
                IsHomeChinupBar = LocalDBManager.Instance.GetDBSetting("HomeChinUp").Value == "true",
                IsHomeDumbbell = LocalDBManager.Instance.GetDBSetting("HomeDumbbell").Value == "true",
                IsHomePlate = LocalDBManager.Instance.GetDBSetting("HomePlate").Value == "true",
                IsHomePully = LocalDBManager.Instance.GetDBSetting("HomePully").Value == "true",
                IsOtherEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true",
                IsOtherChinupBar = LocalDBManager.Instance.GetDBSetting("OtherChinUp").Value == "true",
                IsOtherDumbbell = LocalDBManager.Instance.GetDBSetting("OtherDumbbell").Value == "true",
                IsOtherPlate = LocalDBManager.Instance.GetDBSetting("OtherPlate").Value == "true",
                IsOtherPully = LocalDBManager.Instance.GetDBSetting("OtherPully").Value == "true",
            };

            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true" && model.IsEquipmentEnabled)
                model.Active = "gym";
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true" && model.IsHomeEquipmentEnabled)
                model.Active = "home";
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true" && model.IsOtherEquipmentEnabled)
                model.Active = "other";
            if (string.IsNullOrEmpty(model.Active))
            {
                var hmm = "";
                if (model.IsOtherEquipmentEnabled)
                    hmm = "other";
                if (model.IsHomeEquipmentEnabled)
                    hmm = "home";
                if (model.IsEquipmentEnabled)
                    hmm = "gym";
                model.Active = hmm;
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", hmm == "other" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", hmm == "home" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("GymEquipment", hmm == "gym" ? "true" : "");
            }

            if (IsOnAppeatingFinished)
            {
                IsEquipmentEdited = true;
                NotifyGloabalSettingsChanged();
                await DrMuscleRestClient.Instance.SetUserEquipmentSettings(model);
                
            }
        }

        private async Task SetUserEquipmentSettingsWithLoader()
        {
            var model = new EquipmentModel()
            {
                IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
                IsDumbbellEnabled = LocalDBManager.Instance.GetDBSetting("Dumbbell").Value == "true",
                IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true",
                IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
                IsHomeEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true",
                IsHomeChinupBar = LocalDBManager.Instance.GetDBSetting("HomeChinUp").Value == "true",
                IsHomeDumbbell = LocalDBManager.Instance.GetDBSetting("HomeDumbbell").Value == "true",
                IsHomePlate = LocalDBManager.Instance.GetDBSetting("HomePlate").Value == "true",
                IsHomePully = LocalDBManager.Instance.GetDBSetting("HomePully").Value == "true",
                IsOtherEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true",
                IsOtherChinupBar = LocalDBManager.Instance.GetDBSetting("OtherChinUp").Value == "true",
                IsOtherDumbbell = LocalDBManager.Instance.GetDBSetting("OtherDumbbell").Value == "true",
                IsOtherPlate = LocalDBManager.Instance.GetDBSetting("OtherPlate").Value == "true",
                IsOtherPully = LocalDBManager.Instance.GetDBSetting("OtherPully").Value == "true",
            };

            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true" && model.IsEquipmentEnabled)
                model.Active = "gym";
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true" && model.IsHomeEquipmentEnabled)
                model.Active = "home";
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true" && model.IsOtherEquipmentEnabled)
                model.Active = "other";
            if (string.IsNullOrEmpty(model.Active))
            {
                var hmm = "";
                if (model.IsOtherEquipmentEnabled)
                    hmm = "other";
                if (model.IsHomeEquipmentEnabled)
                    hmm = "home";
                if (model.IsEquipmentEnabled)
                    hmm = "gym";
                model.Active = hmm;
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", hmm == "other" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", hmm == "home" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("GymEquipment", hmm == "gym" ? "true" : "");
            }

            if (IsOnAppeatingFinished)
            {
                IsEquipmentEdited = true;
                NotifyGloabalSettingsChanged();
                await DrMuscleRestClient.Instance.SetUserEquipmentSettingsWithLoader(model);
                
            }
        }

        private async void SetUserReminder()
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserReminderWithoutLoader(new UserInfosModel()
            {
                IsReminder = ReminderSwitch.IsToggled
            });
            
        }

        private async void SetUserCardio()
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserCardio(new UserInfosModel()
            {
                IsCardio = CardioSwitch.IsToggled
            });
            
        }

        private async void SetUserMobility()
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserMobility(new UserInfosModel()
            {
                IsMobility = flexiblitySwitch.IsToggled
            });
            
        }

        private async void SetUserExerciseQuickMode()
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserExerciseQuickMode(new UserInfosModel()
            {
                IsExerciseQuickMode = ExerciseQuickSwitch.IsToggled
            });
            
        }

        private async void SetUserWorkoutReminderEmail()
        {
            NotifyGloabalSettingsChanged();
            LocalDBManager.Instance.SetDBSetting("IsEmailReminder", emailReminderSwitch.IsToggled ? "true" : "false");            
            await DrMuscleRestClient.Instance.SetUserWorkoutReminderEmail(new UserInfosModel()
            {
                IsReminderEmail = emailReminderSwitch.IsToggled
            });
            
            UpdateReminderHours();
        }

        private async void UpdateMassUnit(string munit)
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserMassUnit(new UserInfosModel()
            {
                MassUnit = munit
            });
            
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                {
                    //weight1 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                    //grams = Math.Round(weight1 * (decimal)2.4);

                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    var weight1 = new MultiUnityWeight(value, "kg");
                    BodyweightEntry.Text = string.Format("{0:0.00}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weight1.Kg : weight1.Lb);
                }
                if (IsOnAppeatingFinished)
                {
                    if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                    {
                        GeneratePlatesArray("PlatesKg");
                        GeneratePlatesArray("HomePlatesKg");
                        GeneratePlatesArray("OtherPlatesKg");
                    }
                    else
                    {
                        GeneratePlatesArray("PlatesLb");
                        GeneratePlatesArray("HomePlatesLb");
                        GeneratePlatesArray("OtherPlatesLb");
                    }

                    if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                    {
                        GenerateDumbbellArray("DumbbellKg");
                        GenerateDumbbellArray("HomeDumbbellKg");
                        GenerateDumbbellArray("OtherDumbbellKg");

                        GeneratePulleyArray("PulleyKg");
                        GeneratePulleyArray("HomePulleyKg");
                        GeneratePulleyArray("OtherPulleyKg");
                    }
                    else
                    {
                        GenerateDumbbellArray("DumbbellLb");
                        GenerateDumbbellArray("HomeDumbbellLb");
                        GenerateDumbbellArray("OtherDumbbellLb");

                        GeneratePulleyArray("PulleyLb");
                        GeneratePulleyArray("HomePulleyLb");
                        GeneratePulleyArray("OtherPulleyLb");
                    }

                    MultiUnityWeight increments = null;
                    MultiUnityWeight max = null;
                    MultiUnityWeight min = null;
                    if (LocalDBManager.Instance.GetDBSetting("SliderValue") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("SliderValue").Value != null)
                        {
                            //if (munit == "lb")
                            //    LocalDBManager.Instance.GetDBSetting("SliderValue").Value
                        }
                    }
                    if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("workout_increments").Value != null)
                        {
                            var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                            increments = new MultiUnityWeight(value, "kg");
                            if (munit == "lb")
                            {
                                foreach (var item in platesItems.Reverse())
                                {
                                    if (item.Id == -1)
                                        continue;
                                    if (Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture) >= (int)increments.Lb)
                                    {
                                        increments = new MultiUnityWeight(Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture), "lb");
                                        _increments = increments;
                                        //LocalDBManager.Instance.SetDBSetting("workout_increments", increments.Kg.ToString().Replace(",", "."));
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                foreach (var item in platesItems.Reverse())
                                {
                                    if (item.Id == -1)
                                        continue;
                                    if (Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture) >= increments.Kg)
                                    {
                                        increments = new MultiUnityWeight(Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture), "kg");
                                        //LocalDBManager.Instance.SetDBSetting("workout_increments", increments.Kg.ToString().Replace(",", "."));
                                        _increments = increments;
                                        break;
                                    }
                                }
                            }
                            //UnitEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{increments.Kg}" : $"{increments.Lb}";

                        }
                    }
                    //else
                    //    UnitEntry.Text = "";

                    if (LocalDBManager.Instance.GetDBSetting("workout_max") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("workout_max").Value != null)
                        {
                            var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_max").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                            max = new MultiUnityWeight(value, "kg");
                            //MaxEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{max.Kg}" : $"{max.Lb}";
                            LocalDBManager.Instance.SetDBSetting("workout_max", max.Kg.ToString().Replace(",", "."));
                        }
                    }
                    //else
                    //    MaxEntry.Text = "";
                    if (LocalDBManager.Instance.GetDBSetting("workout_min") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("workout_min").Value != null)
                        {
                            var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_min").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                            min = new MultiUnityWeight(value, "kg");
                           // MinEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{min.Kg}" : $"{min.Lb}";
                            LocalDBManager.Instance.SetDBSetting("workout_min", min.Kg.ToString().Replace(",", "."));
                        }
                    }
                    //else
                    //    MinEntry.Text = "";
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserIncrements(new UserInfosModel()
                    {
                        Increments = increments,
                        Max = max,
                        Min = min
                    });
                    
                }
            }
            catch (Exception ex)
            {

            }
            Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage() { OnlyRefresh = true }, "UpdatedWorkoutMessage");
        }

        private async void UpdateBackOffSet()
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserBackOffSet(new UserInfosModel()
            {
                IsBackOffSet = BackOffSetSwitch.IsToggled
            });
            
            LocalDBManager.Instance.SetDBSetting("BackOffSet", BackOffSetSwitch.IsToggled ? "true" : "false");
        }

        private async void Update1By1Side()
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUser1By1Side(new UserInfosModel()
            {
                Is1By1Side = Switch1By1Side.IsToggled
            });
            
            LocalDBManager.Instance.SetDBSetting("1By1Side", Switch1By1Side.IsToggled ? "true" : "false");
        }

        //StrengthPhase
        private async void UpdateStrengthPhase()
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserStrength(new UserInfosModel()
            {
                IsStrength = StrengthSwitch.IsToggled
            });
            
            Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage() { OnlyRefresh = true }, "UpdatedWorkoutMessage");
            LocalDBManager.Instance.SetDBSetting("StrengthPhase", StrengthSwitch.IsToggled ? "true" : "false");
        }

        private async void UpdateSetSetyle(bool? IsNormal)
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserSetStyle(new UserInfosModel()
            {
                IsNormalSet = IsNormal
            });
            
        }
        private async void UpdatePyramidSetStyle(bool isPyramid)
        {
            NotifyGloabalSettingsChanged();
            await DrMuscleRestClient.Instance.SetUserPyramidSetStyle(new UserInfosModel()
            {
                IsPyramid = isPyramid
            });
            
        }

        private void setLocale()
        {
            var localize = DependencyService.Get<ILocalize>();
            if (localize != null)
            {
                localize.SetLocale(new CultureInfo(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value));
                ResourceLoader.Instance.SetCultureInfo(new CultureInfo(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value));
                MessagingCenter.Send(new LanguageChangeMessage(), "LocalizeUpdated");
            }
        }



        public void LoadCustomReps()
        {
            try
            {
                RepsMinimumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsminimum").Value;
                RepsMaximumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsmaximum").Value;
            }
            catch (Exception)
            {
                UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Title = AppResources.ConnectionError,
                    Message = AppResources.PleaseCheckInternetConnection,
                    OkText = AppResources.GotIt
                });

            }
            CustomRepsStack.IsVisible = true;
        }

        void PlateItems_Unfocused(object sender, Xamarin.Forms.FocusEventArgs e)
        {
            //Do code for save new values for next time
            var mi = ((Entry)sender);
            PlateModel m = (PlateModel)mi.BindingContext;

        }

        void UnitEntry_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {

            var entry = (Entry)sender;
            const string textRegex = @"^\d+(?:[\.,]\d{0,2})?$";
            var text = e.NewTextValue.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
            {
                entry.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
            }

            }
            catch (Exception ex)
            {

            }
        }

        //TODO: Plates
        void OnCancelClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            s.Children[0].IsVisible = false;
            s.Children[1].IsVisible = false;
            s.Children[2].IsVisible = false;
            s.Children[3].IsVisible = true;
        }

        void OnContextMenuClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                if (m.IsSystemPlates)
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = false;
                    s.Children[3].IsVisible = false;
                }
                else
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = true;
                    s.Children[3].IsVisible = false;
                }
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                if (m.IsSystemPlates)
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = false;
                    s.Children[3].IsVisible = false;
                }
                else
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = true;
                    s.Children[3].IsVisible = false;
                }
            }
            else 
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                if (m.IsSystemPlates)
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = false;
                    s.Children[3].IsVisible = false;
                }
                else
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = true;
                    s.Children[3].IsVisible = false;
                }
            }
        }
        public void OnEdit(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditCounts(m, "");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{AppResources.EditPlateWeight}",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditCounts(m, "");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditDumbbellCounts(m, "");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit dumbbell weight",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditDumbbellCounts(m, "");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else    
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditPulleyCounts(m, "");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit pulley weight",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditPulleyCounts(m, "");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
        }

        public void OnEditHome(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditCounts(m, "Home");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{AppResources.EditPlateWeight}",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditCounts(m, "Home");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditDumbbellCounts(m, "Home");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit dumbbell weight",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditDumbbellCounts(m, "Home");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditPulleyCounts(m, "Home");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit pulley weight",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditPulleyCounts(m, "Home");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
        }

        public void OnEditOther(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            bool isPlates = false;
            if (mi.CommandParameter is PlateModel)
                isPlates = true;
            if (isPlates)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditCounts(m, "Other");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{AppResources.EditPlateWeight}",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditCounts(m, "Other");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                //Dumbbell
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditDumbbellCounts(m, "Other");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{"Edit dumbbell weight"}",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditDumbbellCounts(m, "Other");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else
            {
                //Dumbbell
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditPulleyCounts(m, "Other");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{"Edit pulley weight"}",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditPulleyCounts(m, "Other");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
        }
        void FirsttimeExercisePopup_OnTextChanged(PromptTextChangedArgs obj)
        {

            const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
            var text = obj.Value.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
            {
                double result;
                obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
                double.TryParse(obj.Value, out result);
                obj.Value = result.ToString();
            }
        }
        void ExerciseRepsPopup_OnTextChanged(PromptTextChangedArgs obj)
        {
            const string textRegex = @"^\d+(?:)?$";
            bool IsValid = Regex.IsMatch(obj.Value, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
            {
                double result;
                obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
                double.TryParse(obj.Value, out result);
                obj.Value = result.ToString();
            }
        }
        public void OnDelete(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = AppResources.DeletePlates,
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            platesItems.Remove(m);
                            SaveEquipments("");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete dumbbells",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            dumbbellItems.Remove(m);
                            SaveDumbellEquipments("");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete pulleys",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            pulleyItems.Remove(m);
                            SavePulleyEquipments("");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
        }

        public void OnDeleteHome(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = AppResources.DeletePlates,
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            platesItems1.Remove(m);
                            SaveEquipments("Home");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete dumbbells",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            dumbbellItems1.Remove(m);
                            SaveDumbellEquipments("Home");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete pulley",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            pulleyItems1.Remove(m);
                            SavePulleyEquipments("Home");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
        }

        public void OnDeleteOther(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = AppResources.DeletePlates,
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            platesItems2.Remove(m);
                            SaveEquipments("Other");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete dumbbells",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            dumbbellItems2.Remove(m);
                            SaveDumbellEquipments("Other");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete pulleys",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            pulleyItems2.Remove(m);
                            SavePulleyEquipments("Other");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
        }
        void AskForEditPulleyCounts(PulleyModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many?",
                Message = "Enter counts",
                Placeholder = AppResources.TapToEnterHowMany,
                MaxLength = 5,
                Text = m.Value.ToString(),
                OkText = AppResources.Edit,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok || string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        //Save new value
                        SavePulleyEquipments(profile);

                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }


        void AskForEditDumbbellCounts(DumbellModel m, string profile)        {            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
            Title = "How many?",
            Message = "Enter counts",
            Placeholder = AppResources.TapToEnterHowMany,
                MaxLength = 5,
                Text = m.Value.ToString(),
                OkText = AppResources.Edit,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok || string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        //Save new value
                        SaveDumbellEquipments(profile);

                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }


        void AskForEditCounts(PlateModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many?",
                Message = "Enter counts",
                MaxLength = 5, 
                Placeholder = AppResources.TapToEnterHowMany,
                Text = m.Value.ToString(),
                OkText = AppResources.Edit,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok || string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        //Save new value
                        SaveEquipments(profile);

                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void NewDumbbells(string profile)
        {
            DumbellModel m = new DumbellModel();
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            m.WeightType = massUnit;
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"Add dumbbell weight",
                MaxLength = 6,
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Placeholder = AppResources.EnterWeights,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    m.Value = 2;
                    AddDumbbellCounts(m, profile);
                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void NewPulley(string profile)
        {
            PulleyModel m = new PulleyModel();
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            m.WeightType = massUnit;
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"Add pulley weight",
                MaxLength = 6,
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Placeholder = AppResources.EnterWeights,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    AddPulleyCounts(m, profile);
                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }


        private async void NewPlates(string profile)
        {
            PlateModel m = new PlateModel();
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            m.WeightType = massUnit;
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"{AppResources.AddPlateWeight}",
                MaxLength = 5,
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Placeholder = AppResources.EnterWeights,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    AddPlatesCounts(m, profile);
                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void AddPulleyCounts(PulleyModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many?",
                Message = "Enter counts",
                MaxLength = 6,
                Placeholder = AppResources.TapToEnterHowMany,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int dumbbellCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = dumbbellCount;
                        m.IsSystemPlates = false;
                        if (profile == "")
                        {
                            pulleyItems.Insert(pulleyItems.Count - 1, m);
                        }
                        if (profile == "Home")
                        {
                            pulleyItems1.Insert(pulleyItems1.Count - 1, m);
                        }
                        if (profile == "Other")
                        {
                            pulleyItems2.Insert(pulleyItems2.Count - 1, m);
                        }
                        SavePulleyEquipments(profile);
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }


        private async void AddDumbbellCounts(DumbellModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many?",
                Message = "Enter counts",
                MaxLength = 6,
                Text = m.Value.ToString(),
                Placeholder = AppResources.TapToEnterHowMany,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int dumbbellCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = dumbbellCount;
                        m.IsSystemPlates = false;
                        if (profile == "")
                        {
                            dumbbellItems.Insert(dumbbellItems.Count - 1, m);
                        }
                        if (profile == "Home")
                        {
                            dumbbellItems1.Insert(dumbbellItems1.Count - 1, m);
                        }
                        if (profile == "Other")
                        {
                            dumbbellItems2.Insert(dumbbellItems2.Count - 1, m);
                        }
                        SaveDumbellEquipments(profile);
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void AddPlatesCounts(PlateModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many?",
                Message = "Enter counts",
                Placeholder = AppResources.TapToEnterHowMany,
                MaxLength = 5,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        m.IsSystemPlates = false;
                        if (profile == "")
                        {
                            platesItems.Insert(platesItems.Count - 1, m);
                        }
                        if (profile == "Home")
                        {
                            platesItems1.Insert(platesItems1.Count - 1, m);
                        }
                        if (profile == "Other")
                        {
                            platesItems2.Insert(platesItems2.Count - 1, m);
                        }
                        SaveEquipments(profile);
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        void Reminder_Click(object sender, EventArgs args)
        {
            PopupNavigation.Instance.PushAsync(new ReminderPopup());
        }

        private async void SavePulleyEquipments(string profile)
        {
            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                if (profile == "")
                {
                    var kgString = "";
                    foreach (var item in pulleyItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyKg", kgString);

                }
                else if (profile == "Home")
                {
                    var kgString = "";
                    foreach (var item in pulleyItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyKg", kgString);
                }
                else if (profile == "Other")
                {
                    var kgString = "";
                    foreach (var item in pulleyItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyKg", kgString);
                }
                try
                {
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserEquipmentPulleySettings(new EquipmentModel()
                    {
                        AvilablePulley = LocalDBManager.Instance.GetDBSetting($"PulleyKg")?.Value,
                        AvilableHomePulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyKg")?.Value,
                        AvilableOtherPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyKg")?.Value,
                        AvilableLbPulley = LocalDBManager.Instance.GetDBSetting($"PulleyLb")?.Value,
                        AvilableHomeLbPulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyLb")?.Value,
                        AvilableOtherLbPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyLb")?.Value,

                    });
                    
                }
                catch (Exception ex)
                {

                }
                
            }
            else
            {
                if (profile == "")
                {
                    var lbString = "";
                    foreach (var item in pulleyItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyLb", lbString);
                }
                else if (profile == "Home")
                {
                    var lbString = "";
                    foreach (var item in pulleyItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyLb", lbString);
                }
                else if (profile == "Other")
                {
                    var lbString = "";
                    foreach (var item in pulleyItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyLb", lbString);
                }
                try
                {
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserEquipmentPulleySettings(new EquipmentModel()
                    {
                        AvilablePulley = LocalDBManager.Instance.GetDBSetting($"PulleyKg")?.Value,
                        AvilableHomePulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyKg")?.Value,
                        AvilableOtherPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyKg")?.Value,
                        AvilableLbPulley = LocalDBManager.Instance.GetDBSetting($"PulleyLb")?.Value,
                        AvilableHomeLbPulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyLb")?.Value,
                        AvilableOtherLbPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyLb")?.Value,
                    });
                    
                }
                catch (Exception ex)
                {

                }
                
            }
        }


        private async void SaveDumbellEquipments(string profile)
        {
            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                if (profile == "")
                {
                    var kgString = "";
                    foreach (var item in dumbbellItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellKg", kgString);

                }
                else if (profile == "Home")
                {
                    var kgString = "";
                    foreach (var item in dumbbellItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellKg", kgString);
                }
                else if (profile == "Other")
                {
                    var kgString = "";
                    foreach (var item in dumbbellItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellKg", kgString);
                }
                try
                {
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserEquipmentDumbbellSettings(new EquipmentModel()
                    {
                        AvilableDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellKg")?.Value,
                        AvilableHomeDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellKg")?.Value,
                        AvilableOtherDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellKg")?.Value,
                        AvilableLbDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellLb")?.Value,
                        AvilableHomeLbDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellLb")?.Value,
                        AvilableOtherLbDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellLb")?.Value,

                    });
                    
                }
                catch (Exception ex)
                {

                }
                try
                {
                    //var zeroPlates = platesItems.Where(x => x.Key == "0.5").FirstOrDefault();
                    //var onePlates = platesItems.Where(x => x.Key == "1.25").FirstOrDefault();

                    //if (profile == "Home")
                    //{
                    //    zeroPlates = platesItems1.Where(x => x.Key == "0.5").FirstOrDefault();
                    //    onePlates = platesItems1.Where(x => x.Key == "1.25").FirstOrDefault();
                    //}
                    //else if (profile == "Other")
                    //{
                    //    zeroPlates = platesItems2.Where(x => x.Key == "0.5").FirstOrDefault();
                    //    onePlates = platesItems2.Where(x => x.Key == "1.25").FirstOrDefault();
                    //}
                    //decimal value = 0;
                    //if (LocalDBManager.Instance.GetDBSetting("workout_increments")?.Value != null)
                    //{
                    //    value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    //}
                    //var unit = new MultiUnityWeight(value, "kg");
                    //if (zeroPlates.Value == 0 && onePlates.Value != 0 && Math.Round(unit.Kg, 2) != (decimal)2.5)
                    //{
                    //    ConfirmConfig p = new ConfirmConfig()
                    //    {
                    //        Title = "No 0.5-kg plates",
                    //        Message = "Update recommendations to match your plates?",
                    //        OkText = "Update",
                    //        CancelText = AppResources.Cancel,
                    //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    //    };

                    //    var response = await UserDialogs.Instance.ConfirmAsync(p);
                    //    if (response)
                    //    {
                    //        UnitEntry.Unfocus();
                    //        _increments = new MultiUnityWeight((decimal)2.5, "kg");
                    //        UnitEntry.Text = "2.5";
                    //        LocalDBManager.Instance.SetDBSetting("workout_increments", "2.5");
                    //        await DrMuscleRestClient.Instance.SetUserIncrementsOnly(new UserInfosModel()
                    //        {
                    //            Increments = new MultiUnityWeight((decimal)2.5, "kg")
                    //        });

                    //    }

                    //}


                }
                catch (Exception ex)
                {

                }
            }
            else
            {
                if (profile == "")
                {
                    var lbString = "";
                    foreach (var item in dumbbellItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellLb", lbString);
                }
                else if (profile == "Home")
                {
                    var lbString = "";
                    foreach (var item in dumbbellItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellLb", lbString);
                }
                else if (profile == "Other")
                {
                    var lbString = "";
                    foreach (var item in dumbbellItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellLb", lbString);
                }
                try
                {
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserEquipmentDumbbellSettings(new EquipmentModel()
                    {
                        AvilableDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellKg")?.Value,
                        AvilableHomeDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellKg")?.Value,
                        AvilableOtherDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellKg")?.Value,
                        AvilableLbDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellLb")?.Value,
                        AvilableHomeLbDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellLb")?.Value,
                        AvilableOtherLbDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellLb")?.Value,
                    });
                    
                }
                catch (Exception ex)
                {

                }
                try
                {
                    //var zeroPlates = platesItems.Where(x => x.Key == "2.5").FirstOrDefault();
                    //var onePlates = platesItems.Where(x => x.Key == "5").FirstOrDefault();
                    //if (profile == "Home")
                    //{
                    //    zeroPlates = platesItems1.Where(x => x.Key == "2.5").FirstOrDefault();
                    //    onePlates = platesItems1.Where(x => x.Key == "5").FirstOrDefault();
                    //}
                    //if (profile == "Other")
                    //{
                    //    zeroPlates = platesItems2.Where(x => x.Key == "2.5").FirstOrDefault();
                    //    onePlates = platesItems2.Where(x => x.Key == "5").FirstOrDefault();
                    //}

                    //decimal value = 0;
                    //if (LocalDBManager.Instance.GetDBSetting("workout_increments")?.Value != null)
                    //{
                    //    value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    //}

                    //var unit = new MultiUnityWeight(value, "kg");
                    //if (zeroPlates.Value == 0 && onePlates.Value != 0 && Math.Round(unit.Lb, 2) != 10)
                    //{
                    //    ConfirmConfig p = new ConfirmConfig()
                    //    {
                    //        Title = "No 2.5-lbs plates",
                    //        Message = "Update recommendations to match your plates?",
                    //        OkText = "Update",
                    //        CancelText = AppResources.Cancel,
                    //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    //    };

                    //    var response = await UserDialogs.Instance.ConfirmAsync(p);
                    //    if (response)
                    //    {
                    //        UnitEntry.Unfocus();
                    //        _increments = new MultiUnityWeight((decimal)10, "lb");
                    //        UnitEntry.Text = "10";
                    //        LocalDBManager.Instance.SetDBSetting("workout_increments", new MultiUnityWeight((decimal)10, "lb").Kg.ToString().ReplaceWithDot());
                    //        await DrMuscleRestClient.Instance.SetUserIncrementsOnly(new UserInfosModel()
                    //        {
                    //            Increments = new MultiUnityWeight((decimal)10, "lb")
                    //        });

                    //    }

                    //}


                }
                catch (Exception ex)
                {

                }
            }
        }

        private async void SaveEquipments(string profile)
        {
            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                if (profile == "")
                {
                    var kgString = "";
                    foreach (var item in platesItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesKg", kgString);
                    
                }
                else if (profile == "Home")
                {
                    var kgString = "";
                    foreach (var item in platesItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesKg", kgString);
                }
                else if (profile == "Other")
                {
                    var kgString = "";
                    foreach (var item in platesItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesKg", kgString);
                }
                try
                {
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserEquipmentPlateSettings(new EquipmentModel()
                    {
                        AvilablePlate = LocalDBManager.Instance.GetDBSetting($"PlatesKg")?.Value,
                        AvilableHomePlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesKg")?.Value,
                        AvilableOtherPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesKg")?.Value,
                        AvilableLbPlate = LocalDBManager.Instance.GetDBSetting($"PlatesLb")?.Value,
                        AvilableHomeLbPlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesLb")?.Value,
                        AvilableOtherLbPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesLb")?.Value,

                    });
                    
                }
                catch (Exception ex)
                {

                }
                //try
                //{
                //    var zeroPlates = platesItems.Where(x => x.Key == "0.5").FirstOrDefault();
                //    var onePlates = platesItems.Where(x => x.Key == "1.25").FirstOrDefault();

                //    if (profile == "Home")
                //    {
                //         zeroPlates = platesItems1.Where(x => x.Key == "0.5").FirstOrDefault();
                //         onePlates = platesItems1.Where(x => x.Key == "1.25").FirstOrDefault();
                //    }
                //    else if (profile == "Other")
                //    {
                //         zeroPlates = platesItems2.Where(x => x.Key == "0.5").FirstOrDefault();
                //         onePlates = platesItems2.Where(x => x.Key == "1.25").FirstOrDefault();
                //    }
                //    decimal value = 0;
                //    if (LocalDBManager.Instance.GetDBSetting("workout_increments")?.Value != null)
                //    {
                //        value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                //    }
                //        var unit = new MultiUnityWeight(value, "kg");
                //        if (zeroPlates.Value == 0 && onePlates.Value != 0 && Math.Round(unit.Kg, 2) != (decimal)2.5)
                //        {
                //            ConfirmConfig p = new ConfirmConfig()
                //            {
                //                Title = "No 0.5-kg plates",
                //                Message = "Update recommendations to match your plates?",
                //                OkText = "Update",
                //                CancelText = AppResources.Cancel,
                //                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                //            };

                //            var response = await UserDialogs.Instance.ConfirmAsync(p);
                //            if (response)
                //            {
                //            UnitEntry.Unfocus();
                //            _increments = new MultiUnityWeight((decimal)2.5, "kg");
                //            UnitEntry.Text = "2.5";
                //            LocalDBManager.Instance.SetDBSetting("workout_increments", "2.5");
                //                await DrMuscleRestClient.Instance.SetUserIncrementsOnly(new UserInfosModel()
                //                {
                //                    Increments = new MultiUnityWeight((decimal)2.5, "kg")
                //                });
                                
                //            }
                            
                //        }
                    

                //}
                //catch (Exception ex)
                //{

                //}
            }
            else
            {
                if (profile == "")
                {
                    var lbString = "";
                    foreach (var item in platesItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesLb", lbString);
                }
                else if (profile == "Home")
                {
                    var lbString = "";
                    foreach (var item in platesItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesLb", lbString);
                }
                else if (profile == "Other")
                {
                    var lbString = "";
                    foreach (var item in platesItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesLb", lbString);
                }
                try
                {
                    NotifyGloabalSettingsChanged();
                    await DrMuscleRestClient.Instance.SetUserEquipmentPlateSettings(new EquipmentModel()
                    {
                        AvilablePlate = LocalDBManager.Instance.GetDBSetting($"PlatesKg")?.Value,
                        AvilableHomePlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesKg")?.Value,
                        AvilableOtherPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesKg")?.Value,
                        AvilableLbPlate = LocalDBManager.Instance.GetDBSetting($"PlatesLb")?.Value,
                        AvilableHomeLbPlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesLb")?.Value,
                        AvilableOtherLbPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesLb")?.Value,

                    });
                    
                }
                catch (Exception ex)
                {

                }
                //try
                //{
                //    var zeroPlates = platesItems.Where(x => x.Key == "2.5").FirstOrDefault();
                //    var onePlates = platesItems.Where(x => x.Key == "5").FirstOrDefault();
                //    if (profile == "Home")
                //    {
                //         zeroPlates = platesItems1.Where(x => x.Key == "2.5").FirstOrDefault();
                //         onePlates = platesItems1.Where(x => x.Key == "5").FirstOrDefault();
                //    }
                //    if (profile == "Other")
                //    {
                //        zeroPlates = platesItems2.Where(x => x.Key == "2.5").FirstOrDefault();
                //        onePlates = platesItems2.Where(x => x.Key == "5").FirstOrDefault();
                //    }

                //    decimal value = 0;
                //    if (LocalDBManager.Instance.GetDBSetting("workout_increments")?.Value != null)
                //    {
                //        value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                //    }
                    
                //        var unit = new MultiUnityWeight(value, "kg");
                //        if (zeroPlates.Value == 0 && onePlates.Value != 0 && Math.Round(unit.Lb, 2) != 10)
                //        {
                //            ConfirmConfig p = new ConfirmConfig()
                //            {
                //                Title = "No 2.5-lbs plates",
                //                Message = "Update recommendations to match your plates?",
                //                OkText = "Update",
                //                CancelText = AppResources.Cancel,
                //                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                //            };

                //            var response = await UserDialogs.Instance.ConfirmAsync(p);
                //            if (response)
                //            {
                //            UnitEntry.Unfocus();
                //            _increments = new MultiUnityWeight((decimal)10, "lb");
                //            UnitEntry.Text = "10";
                //            LocalDBManager.Instance.SetDBSetting("workout_increments",new MultiUnityWeight((decimal)10, "lb").Kg.ToString().ReplaceWithDot());
                //                await DrMuscleRestClient.Instance.SetUserIncrementsOnly(new UserInfosModel()
                //                {
                //                    Increments = new MultiUnityWeight((decimal)10, "lb")
                //                });

                //            }
                            
                //        }
                    

                //}
                //catch (Exception ex)
                //{

                //}
            }
        }
        //
        void MobilityRepsEntry_TextChanged(System.Object sender, Xamarin.Forms.TextChangedEventArgs e)
        {
            try
            {

            var entry = (Entry)sender;
            const string textRegex = @"^\d+(?:)?$";
            var text = e.NewTextValue.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
            {
                entry.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
            }

            }
            catch (Exception ex)
            {

            }
        }

        void SetEntry_TextChanged(System.Object sender, Xamarin.Forms.TextChangedEventArgs e)
        {
            try
            {

            var entry = (Entry)sender;
            const string textRegex = @"^\d+(?:[\.,]\d{0,2})?$";
            var text = e.NewTextValue.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
            {
                entry.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
                    
                        //"x nb of sets; e.g. 30-40 min for 2 sets, 45-60 min for 3 sets, etc.]";
                }
            if (!string.IsNullOrEmpty(entry.Text)) { 
                int set = int.Parse(entry.Text);
                LblCustomWorkset.Text = $"Workouts last about {15 * set}-{20 * set} min";
                }
            }

            catch (Exception ex)
            {

            }
        }

        async void ReconfigureButton_Clicked(System.Object sender, System.EventArgs e)
        {
            //

            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
            {
                Title = "Are you sure?",
                Message = "Your program, goals, and settings may change.",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Reconfigure",
                CancelText = AppResources.Cancel,
            };
            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
            if (isConfirm)
            {
                PagesFactory.PushAsync<ReconfigureBoardingPage>(true);
            }
            else
            {
                return;
            }
        }


        void GymEquipment()
        {
            //Biceps //Chest //Abs
            Device.BeginInvokeOnMainThread(() =>
            {
                
            });
        }
        private void GymEquipmentUnselected()
        {
            
        }

        void BtnGymEquipmentClicked(System.Object sender, System.EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("HomeEquipment", "");
            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "");
            //if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
            //{
            //    LocalDBManager.Instance.SetDBSetting("GymEquipment", "");
            //    GymEquipmentUnselected();

            //}
            //else
            //{
            bool isSetted = LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true";
            LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");
            GymEquipment();
            //}
            if (LocalDBManager.Instance.GetDBSetting("Equipment")?.Value == "true" && !isSetted && !IsGymEnabled)
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    IsGymEnabled = true;
                    IsHomeEnabled = false;
                    IsOtherEnabled = false;
                    SetUserEquipmentSettings();
                });
                
            }
            else
            {
                ResetEquipmeentToggle();
            }
        }

        void HomeEquipment()
        {

            //Biceps //Chest //Abs
            Device.BeginInvokeOnMainThread(() =>
            {
                
            });
        }

        private void HomeEquipmentUnSelected()
        {
            
        }

        void BtnHomeEquipmentClicked(System.Object sender, System.EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("GymEquipment", "");
            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "");

            bool isSetted = LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true";
            LocalDBManager.Instance.SetDBSetting("HomeEquipment", "true");
            HomeEquipment();

            if (LocalDBManager.Instance.GetDBSetting("HomeMainEquipment")?.Value == "true" && !isSetted && !IsHomeEnabled)
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    IsGymEnabled = false;
                    IsHomeEnabled = true;
                    IsOtherEnabled = false;
                    SetUserEquipmentSettings();
                });
            }
            else
            {
                ResetEquipmeentToggle();
            }
        }

        void OtherEquipment()
        {

            //Biceps //Chest //Abs

            Device.BeginInvokeOnMainThread(() =>
            {
                
            });
        }
        private void OtherEquipmentUnselected()
        {

            
        }

        void BtnOtherEquipmentClicked(System.Object sender, System.EventArgs e)
        {

            LocalDBManager.Instance.SetDBSetting("GymEquipment", "");
            LocalDBManager.Instance.SetDBSetting("HomeEquipment", "");
            //if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
            //{
            //    LocalDBManager.Instance.SetDBSetting("OtherEquipment", "");
            //    OtherEquipmentUnselected();

            //}
            //else
            //{
            bool isSetted = LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true";
            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "true");
            OtherEquipment();

            //}
            if (LocalDBManager.Instance.GetDBSetting("OtherMainEquipment")?.Value == "true" && !isSetted && !IsOtherEnabled)
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    IsGymEnabled = false;
                    IsHomeEnabled = false;
                    IsOtherEnabled = true;
                    SetUserEquipmentSettings();
                });
            }
            else
            {
                ResetEquipmeentToggle();
            }
        }
        private void ResetEquipmeentToggle()
        {
            var model = new EquipmentModel()
            {
                IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                IsHomeEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true",
                IsOtherEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true",

            };
            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true" && model.IsEquipmentEnabled)
                model.Active = "gym";
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true" && model.IsHomeEquipmentEnabled)
                model.Active = "home";
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true" && model.IsOtherEquipmentEnabled)
                model.Active = "other";
            if (string.IsNullOrEmpty(model.Active))
            {
                var hmm = "";
                if (IsOtherEnabled && model.IsOtherEquipmentEnabled)
                    hmm = "other";
                if (IsHomeEnabled && model.IsHomeEquipmentEnabled)
                    hmm = "home";
                if (IsGymEnabled && model.IsEquipmentEnabled)
                    hmm = "gym";
                model.Active = hmm;

                if (string.IsNullOrEmpty(model.Active))
                {
                    if (model.IsOtherEquipmentEnabled)
                        hmm = "other";
                    if (model.IsHomeEquipmentEnabled)
                        hmm = "home";
                    if (model.IsEquipmentEnabled)
                        hmm = "gym";
                }
                model.Active = hmm;
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", hmm == "other" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", hmm == "home" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("GymEquipment", hmm == "gym" ? "true" : "");
            }
        }

        void ViewMoreStats_Clicked(System.Object sender, System.EventArgs e)
        {
            Device.OpenUri(new Uri("https://dashboard.dr-muscle.com/"));
        }

        void PickerBodyPart_Unfocused(System.Object sender, Xamarin.Forms.FocusEventArgs e)
        {
            string priority = PickerBodyPart.SelectedIndex == -1 || PickerBodyPart.SelectedIndex == 0 ? "" : (string)PickerBodyPart.SelectedItem;
            LocalDBManager.Instance.SetDBSetting("BodypartPriority", priority);
            if (priority == "")
                LblBodypart.Text = "Adds 1 exercise for your priority part";
            else
                LblBodypart.Text = string.Format("Adds 1 {0} exercise", LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value.Trim().ToLower());
            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);

        }

        void PickerMaxWorkoutDuration_Unfocused(System.Object sender, Xamarin.Forms.FocusEventArgs e)
        {
            try
            {
                var item = maxWorkoutDurations
                .First(x => x.Key == PickerWorkoutDuration.SelectedItem.ToString());
            SetMaxWorkoutDurationText();
            LocalDBManager.Instance.SetDBSetting("MaxWorkoutDuration", item.Value.ToString());
            //Add API call here
            DrMuscleRestClient.Instance.SetUserWorkoutDuration(new UserInfosModel() { WorkoutDuration = item.Value });
            NotifyGloabalSettingsChanged();
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
            }
        }
        void LearnMoreWButtonClicked(System.Object sender, System.EventArgs e)
        {
            if (CheckTrialUser())
                return;

            PagesFactory.PushAsync<LearnPage>();
        }

        void SetMaxWorkoutDurationText()
        {
            var item = maxWorkoutDurations
                .First(x => x.Key == PickerWorkoutDuration.SelectedItem.ToString());
            var isRestPause = LocalDBManager.Instance.GetDBSetting("SetStyle")?.Value == "RestPause";
            switch (item.Value)
            {
                case 0:
                    LblMaxWorkoutDuration.Text = "No change to workout.";
                    break;
                case 1:
                    LblMaxWorkoutDuration.Text = isRestPause ? "1 warm-up set, max 2 work sets." : "No warm-up sets, max 2 work sets.";
                    break;
                case 2:
                    LblMaxWorkoutDuration.Text = isRestPause ? "1 warm-up set, max 3 work sets." : "1 warm-up set, max 2 work sets.";
                    break;
                case 3:
                    LblMaxWorkoutDuration.Text =  isRestPause ? "1 warm-up set, max 4 work sets." : "1 warm-up set, max 3 work sets.";
                    break;
            }
        }
        private bool CheckTrialUser()
        {
            if (App.IsFreePlan)
            {
                ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                {
                    Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
                    Title = "You discovered a premium feature!",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Upgrade",
                    CancelText = "Maybe later",
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            PagesFactory.PushAsync<SubscriptionPage>();
                        }
                        else
                        {

                        }
                    }
                };
                UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
            return App.IsFreePlan;
        }

        void SetStylePicker_Unfocused(System.Object sender, Xamarin.Forms.FocusEventArgs e)
        {
            
            switch (SetStylePicker.SelectedIndex)
            {
                case 0:
                    UpdateSetSetyle(false);
                    LblRestPauseSets.Text = "Harder, but makes your workouts ~59% faster";
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                    LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
                    break;
                case 1:
                    UpdateSetSetyle(true);
                    LblRestPauseSets.Text = "Takes more time, but builds more strength";
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                    LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
                    break;
                case 2:
                    //Pyramid
                    LblRestPauseSets.Text = "Reps decrease from set to set";
                    UpdatePyramidSetStyle(true);

                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                    LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
                    break;
                case 3:
                    //Reverse pyramid
                    LblRestPauseSets.Text = "Reps increase from set to set";
                    UpdateSetSetyle(null);

                    LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "true");
                    LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
                    break;
                default:
                    break;
            }
            SetMaxWorkoutDurationText();
        }

        async void ResetButton_Clicked(System.Object sender, System.EventArgs e)
        {
            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
            {
                Title = "Are you sure?",
                Message = "Reset history for all exercises? This cannot be undone.",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Reset all",
                CancelText = AppResources.Cancel,
            };
            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
            if (isConfirm)
            {
                NotifyGloabalSettingsChanged();
                await DrMuscleRestClient.Instance.ResetAllExercise();
                
            }
            else
            {
                return;
            }
        }

        void ChangeEmailButton_Clicked(System.Object sender, System.EventArgs e)
        {
            GetEmail();
        }
        private void GetEmail()
        {
            PromptConfig p = new PromptConfig()
            {
                InputType = InputType.Email,
                IsCancellable = true,
                Title = "Update account email",
                Placeholder = "Enter your email",
                OkText = "Update email",
                CancelText = "Cancel",
                Text = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = new Action<PromptResult>(GetEmailAction)
            };

            UserDialogs.Instance.Prompt(p);
        }
        private async void GetEmailAction(PromptResult response)
        {

            if (!CrossConnectivity.Current.IsConnected)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = AppResources.PleaseCheckInternetConnection,
                    Title = AppResources.ConnectionError,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
                
                GetEmailAction(response);
                return;
            }
            if (response.Ok)
            {
                if (!Emails.ValidateEmail(response.Text))
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        Message = AppResources.InvalidEmailError,
                        Title = AppResources.InvalidEmailAddress,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    });
                    GetEmail();
                    return;
                }
                var email = LocalDBManager.Instance.GetDBSetting("email")?.Value.ToLower();
                if (response.Text.ToLower() == email)
                    return;
                BooleanModel existingUser = await DrMuscleRestClient.Instance.UpdateEmail(new IsEmailAlreadyExistModel() { email = response.Text });
                if (existingUser != null)
                {
                    if (!existingUser.Result)
                    {

                        if (existingUser.api?.ErrorMessage == "Failed to update")
                        {
                            AlertConfig ShowAlertPopUp1 = new AlertConfig()
                            {
                                Title = "Failed to update",
                                Message = $"You are using {email} email in your subscription, to changing this email causes it to remove your subscription",
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = "OK",


                            };
                            await UserDialogs.Instance.AlertAsync(ShowAlertPopUp1);

                            return;
                        }
                        AlertConfig ShowAlertPopUp = new AlertConfig()
                        {
                            Title = "Error",
                            Message = existingUser.api.ErrorMessage,
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Try again",
                            

                        };
                        await UserDialogs.Instance.AlertAsync(ShowAlertPopUp);

                        return;
                    }
                    else
                    {
                       // LblEmail.Text = $"Currently {response.Text.ToLower()}";
                        LocalDBManager.Instance.SetDBSetting("email", response.Text.ToLower());
                    }

                }
                else
                    GetEmailAction(response);

               
            }
        }

        async void hoursBeforeEntry_Unfocused(System.Object sender, Xamarin.Forms.FocusEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(hoursBeforeEntry.Text))
                {
                    var count = int.Parse(hoursBeforeEntry.Text.ReplaceWithDot());
                    if (count < 0 || count > 23)
                    {
                        await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Message = "Workout reminder hours should be from 0 to 23.",
                            OkText = AppResources.Ok
                        });
                        return;
                    }

                    await DrMuscleRestClient.Instance.SetReminderHoursbeforeWorkout(new UserInfosModel()
                    {
                        ReminderBeforeHours = int.Parse(hoursBeforeEntry.Text.ReplaceWithDot())
                    });
                    LocalDBManager.Instance.SetDBSetting("ReminderHours", hoursBeforeEntry.Text.ReplaceWithDot());

                    UpdateReminderHours();
                }
                else
                {
                    //await DrMuscleRestClient.Instance.SetCustomWarmups(new UserInfosModel()
                    //{
                    //    WarmupsValue = null
                    //});
                    //LocalDBManager.Instance.SetDBSetting("warmups", null);
                }
                //LocalDBManager.Instance.ResetReco();
            }
            catch (Exception)
            {

            }

        }

        async void UpdateReminderHours()
        {
            if (LocalDBManager.Instance.GetDBSetting("RecommendedReminder")?.Value == "false")
            {
                //Set reminder based on Custom reminder - recovery hours
                return;
            }
            try
            {

           
                var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            var index = 0;
            
            try
            {
                if (workouts.Sets != null)
                {
                   
                }
                else
                {
                    
                    workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
                }
                if (workouts.GetUserProgramInfoResponseModel != null)
                {
                    
                }
            }
            catch (Exception ex)
            {

                workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            }

            // var summaryLevelUpModel = new BotModel() { Type = BotType.SummaryLevelup };
            var summaryRestModel = new BotModel()
            {
                Type = BotType.SummaryRest,
            };
           
            var RequiredHours = 18;
            int hours = 0;
            int minutes = 0;

            if (workouts != null && workouts.LastWorkoutDate != null)
            {
                bool IsInserted = false;

                TimeSpan timeSpan;
                String dayStr = "days";
                int days = 0;
                hours = 0;
                minutes = 0;

                if (workouts.LastWorkoutDate != null)
                {

                    days = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays;
                    hours = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalHours;
                    minutes = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalMinutes;
                    if (days > 0)
                        dayStr = days == 1 ? "day" : "days";
                    else if (hours > 0 && hours < 72)
                        dayStr = hours <= 1 ? "hour" : "hours";
                    else if (minutes < 60)
                        dayStr = minutes <= 1 ? "minute" : "minutes";

                    var d = 0;
                    if (days > 0)
                        d = days;
                    else
                    {
                        d = timeSpan.Days;

                        if (days > 0)
                            dayStr = d == 1 ? "day" : "days";
                        else if (hours > 0 && hours < 72)
                            dayStr = hours <= 1 ? "hour" : "hours";
                        else if (minutes < 60)
                            dayStr = minutes <= 1 ? "minute" : "minutes";


                    }
                }



                if (workouts.LastWorkoutDate != null)
                {
                    RequiredHours = 18;
                    if (workouts != null && workouts.GetUserProgramInfoResponseModel != null && workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null && workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null && workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.IsSystemExercise)
                    {
                        if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bodyweight") ||
workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("mobility") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("powerlifting") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("full-body") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bands"))
                        {

                            RequiredHours = 42;
                            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                            {
                                if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) < 30)
                                    RequiredHours = 18;
                            }
                            if (workouts.LastConsecutiveWorkoutDays > 1 && workouts.LastWorkoutDate != null && (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays < 2)
                                RequiredHours = 42;
                        }

                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[home] push") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[home] pull") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[home] legs") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[gym] push") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[gym] pull") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[gym] legs"))
                        {
                            RequiredHours = 18;
                            if (workouts.LastConsecutiveWorkoutDays > 5 && workouts.LastWorkoutDate != null && (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays < 3)

                            {
                                RequiredHours = 42;
                            }
                        }
                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("split"))
                        {
                            RequiredHours = 18;
                            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                            {
                                if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) > 50)
                                    RequiredHours = 42;
                            }
                            if (workouts.LastConsecutiveWorkoutDays > 1 && workouts.LastWorkoutDate != null && (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays < 2)
                                RequiredHours = 42;
                        }
                    }

                    if (days > 0 && hours >= RequiredHours)
                    {
                        summaryRestModel.SinceTime = $"{days} {dayStr}";
                        summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                        summaryRestModel.TrainRest = "Train";
                        summaryRestModel.StrengthTextColor = AppThemeConstants.GreenColor;
                        summaryRestModel.TrainRestText = "Coach says";// "Recovered";// (days > 9 ? "I may recommend lighter weights" : "You should have recovered").ToLower().FirstCharToUpper();
                        //BotList.Add(restModel);
                        IsInserted = true;
                        //await AddQuestion(days > 9 ? $"{AppResources.YourLastWorkoutWas} {days} {dayStr} ago. I may recommend a light session. Start planned workout?" : $"Your last workout was {days} {dayStr} ago. You should have recovered. Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                    }
                    else if (hours > 0)
                    {

                        if (hours < RequiredHours)
                        {
                            if (LocalDBManager.Instance.GetDBSetting("RecommendedReminder")?.Value == "true")
                            {
                                //set Recommended days notification if user has set recommended
                                DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", TimeSpan.FromHours(RequiredHours - hours) + DateTime.Now.TimeOfDay, 1111, NotificationInterval.Week);
                            }
                            else
                                DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1111);
                            summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;
                            var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                            //summaryRestModel.TrainRest = $"{hours}/{RequiredHours} {h}";
                            //summaryRestModel.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();

                            summaryRestModel.TrainRest = "Rest";
                            summaryRestModel.SinceTime = $"{hours}/{RequiredHours} {h}";
                            if (LocalDBManager.Instance.GetDBSetting($"WorkoutAdded{DateTime.Now.Date.AddDays(1)}")?.Value == "true")
                            {
                                summaryRestModel.SinceTime = "18 hours";
                            }
                            summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                            //lifted.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                            summaryRestModel.TrainRestText = "Coach says";// "Fatigued";// $"At least {RequiredHours - hours} {h} more to recover".ToLower().FirstCharToUpper();

                            //BotList.Add(restModel);
                            IsInserted = true;
                            //await AddQuestion($"Your last workout was {hours} {dayStr} ago. I'm not sure it makes sense to work out again now... Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                        }
                        else
                        {
                            summaryRestModel.SinceTime = $"{hours} {dayStr}";
                            summaryRestModel.SinceTime = $"{hours}/{RequiredHours} hours";
                            if (LocalDBManager.Instance.GetDBSetting($"WorkoutAdded{DateTime.Now.Date.AddDays(1)}")?.Value == "true")
                            {
                                summaryRestModel.SinceTime = "18 hours";
                            }
                            summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                            summaryRestModel.TrainRest = "Train";
                            summaryRestModel.StrengthTextColor = AppThemeConstants.GreenColor;
                            summaryRestModel.TrainRestText = "Coach says";// "Recovered"; //"You should have recovered".ToLower().FirstCharToUpper();
                            //BotList.Add(restModel);
                            IsInserted = true;
                            // await AddQuestion($"Your last workout was {hours} {dayStr} ago. You should have recovered. Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                        }

                    }
                    else
                    {
                        var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                        //summaryRestModel.TrainRest = $"{RequiredHours} hours";
                        summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;
                        //summaryRestModel.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                        summaryRestModel.TrainRest = "Rest";
                        summaryRestModel.SinceTime = $"{RequiredHours} {h}";
                        summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                        summaryRestModel.TrainRestText = "Coach says";// "Fatigued";//$"At least {RequiredHours - hours} {h} more to recover".ToLower().FirstCharToUpper();
                        if (LocalDBManager.Instance.GetDBSetting($"WorkoutAdded{DateTime.Now.Date.AddDays(1)}")?.Value == "true")
                        {
                            summaryRestModel.SinceTime = "18 hours";
                        }
                      
                        //BotList.Add(restModel);
                        IsInserted = true;
                        //await AddQuestion($"Your last workout was {minutes} {dayStr} ago. I'm not sure it makes sense to work out again today... Up next is {workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.Label}.");
                    }
                }

                if (!IsInserted)
                {
                    summaryRestModel.SinceTime = $"N/A";
                    summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                    summaryRestModel.TrainRest = "Train";
                   
                    //BotList.Add(summaryRestModel);

                }

            }
            else
            {
                RequiredHours = 18;
                if (workouts != null && workouts.GetUserProgramInfoResponseModel != null && workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null && workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null && workouts.GetUserProgramInfoResponseModel.RecommendedProgram.IsSystemExercise)
                {
                    if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bodyweight") ||
workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("mobility") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("powerlifting") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("full-body") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bands"))
                    {
                        RequiredHours = 42;
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                        {
                            if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) < 30)
                                RequiredHours = 18;
                        }
                    }
                    else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("split"))
                    {
                        RequiredHours = 18;
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                        {
                            if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) > 50)
                                RequiredHours = 42;
                        }
                    }
                }
                //summaryRestModel.TrainRest = $"{RequiredHours} hours";
                summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;
                //summaryRestModel.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();

                //summaryRestModel.TrainRest = $"{RequiredHours} hours";
                summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;
                //summaryRestModel.TrainRestText = $"More to recover".ToLower().FirstCharToUpper();
                summaryRestModel.TrainRest = "Rest";
                summaryRestModel.SinceTime = $"{RequiredHours} hours";
                summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                summaryRestModel.TrainRestText = "Coach says";// "Fatigued";// $"At least {RequiredHours} hours more to recover".ToLower().FirstCharToUpper();


            }
            if (summaryRestModel.TrainRest == "Rest")
            {
                //Set Reminder email if enabled
                if (LocalDBManager.Instance.GetDBSetting("IsEmailReminder") == null)
                    LocalDBManager.Instance.SetDBSetting("IsEmailReminder", "true");
                if (RequiredHours - hours > 0)
                {
                    //need to - reminder hours
                    var beforeHour = 0;
                    try
                    {
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("ReminderHours")?.Value))
                            beforeHour = int.Parse(LocalDBManager.Instance.GetDBSetting("ReminderHours")?.Value);
                    }
                    catch (Exception ex)
                    {

                    }

                    var date = DateTime.Now;
                    if (workouts.LastWorkoutDate != null)
                    {
                        date = ((DateTime)workouts.LastWorkoutDate).ToLocalTime();
                    }
                    DrMuscleRestClient.Instance.SetUserEmailReminderTime(new UserModel()
                    {
                        LastActiveDate = date.AddHours(RequiredHours - hours - beforeHour)
                    });

                    if (LocalDBManager.Instance.GetDBSetting("IsEmailReminder")?.Value == "true")
                    {
                        try
                        {

                            //Set Local Notification
                            var dt1 = DateTime.Now.AddHours(RequiredHours - hours - beforeHour);
                            var timeSpan = dt1 - DateTime.Now ;

                            //var timeSpan = new TimeSpan(dt);
                            var workoutName = $"{workouts?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Label}";
                            var weekStreak = "";
                            if (workouts.ConsecutiveWeeks != null && workouts.ConsecutiveWeeks.Count > 0)
                            {
                                var lastTime = workouts.ConsecutiveWeeks.Last();
                                var year = Convert.ToString(lastTime.MaxWeek).Substring(0, 4);
                                var weekOfYear = Convert.ToString(lastTime.MaxWeek).Substring(4, 2);
                                CultureInfo myCI = new CultureInfo("en-US");
                                Calendar cal = myCI.Calendar;

                                if (int.Parse(year) == DateTime.Now.Year)
                                {
                                    var currentWeekOfYear = cal.GetWeekOfYear(DateTime.Now, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
                                    if (int.Parse(weekOfYear) >= currentWeekOfYear)
                                    {
                                        if (lastTime.ConsecutiveWeeks > 0)
                                        {
                                            weekStreak = $"—{lastTime.ConsecutiveWeeks}-week streak!";
                                        }
                                    }

                                    else if (int.Parse(weekOfYear) == currentWeekOfYear - 1)
                                    {
                                        if (lastTime.ConsecutiveWeeks > 0)
                                        {
                                            weekStreak = $"—{lastTime.ConsecutiveWeeks}-week streak!";
                                        }
                                    }
                                }
                            }
                            timeSpan = TimeSpan.FromHours(RequiredHours - hours - beforeHour) + DateTime.Now.TimeOfDay;
                            DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification($"Workout in {beforeHour} {(beforeHour < 2 ? "hour" : "hours")}", $"{workoutName}{weekStreak}", timeSpan, 1122, NotificationInterval.Week);

                        }
                        catch (Exception ex)
                        {

                        }
                    }
                    else
                    {
                        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1122);
                    }
                }

                
            }
            }
            catch (Exception ex)
            {

            }
        }

        async void DeleteButton_Clicked(System.Object sender, System.EventArgs e)
        {
            
            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
            {
                Title = "Are you sure?",
                Message = "Delete everything? This cannot be undone.",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Delete",
                CancelText = AppResources.Cancel,
            };
            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
            if (isConfirm)
            {
                var response = await DrMuscleRestClient.Instance.DeleteAccount();
                if (response != null)
                {
                    AlertConfig alert = new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Message = "Account purged.",
                        Title = "Deletion successful",
                        OkText = "Continue"
                    };
                    await UserDialogs.Instance.AlertAsync(alert);

                    if (App.IsMealPlan || App.IsV1User)
                    {
                        ConfirmConfig alert2 = new ConfirmConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Message = "Subscription still active. Email us to cancel?",
                            Title = "In-app records deleted",
                            OkText = "Email",
                            CancelText = "Skip"
                        };
                        var res = await UserDialogs.Instance.ConfirmAsync(alert2);
                        if (res)
                        {
                            await HelperClass.SendMail("Subscription question");
                        }
                    }
                    Logout();
                }
            }
            else
            {
                return;
            }
        }

        private async void Logout()
        {
            CancelNotification();
            LocalDBManager.Instance.Reset();
            CurrentLog.Instance.Reset();
            App.IsV1User = false;
            App.IsV1UserTrial = false;
            App.IsCongratulated = false;
            App.IsSupersetPopup = false;
            App.IsFreePlan = false;

            ((App)Application.Current).UserWorkoutContexts.workouts = new GetUserWorkoutLogAverageResponse();

            ((App)Application.Current).UserWorkoutContexts.SaveContexts();
            ((App)Application.Current).WorkoutHistoryContextList.Histories = new List<HistoryModel>();
            ((App)Application.Current).WorkoutHistoryContextList.SaveContexts();
            ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
            ((App)Application.Current).WorkoutLogContext.SaveContexts();

            ((App)Application.Current).WeightsContextList.Weights = new List<UserWeight>();
            ((App)Application.Current).WeightsContextList.SaveContexts();

            try
            {
                if (((global::DrMuscle.MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage.Navigation.NavigationStack[0] is LearnPage)
                    ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).SelectedItem = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[0];
            }
            catch (Exception ex)
            {

            }
            await PagesFactory.PopToRootAsync();
            ((App)Application.Current).displayCreateNewAccount = true;
            // await PagesFactory.PushAsync<WelcomePage>(true);
            //PagesFactory.PushAsync<MainOnboardingPage>();
            PagesFactory.PopThenPushAsync<MainOnboardingPage>(true);
            
            //Call rewake API if Apple login
            //
            //var nvc = new List<KeyValuePair<string, string>>();
            //nvc.Add(new KeyValuePair<string, string>("client_id", "TEST2"));
            //nvc.Add(new KeyValuePair<string, string>("client_secret", "TEST2"));
            //nvc.Add(new KeyValuePair<string, string>("token", "TEST2"));
            //nvc.Add(new KeyValuePair<string, string>("token_type_hint", "TEST2"));
            //var client = new HttpClient();
            //var req = new HttpRequestMessage(HttpMethod.Post, "https://appleid.apple.com/auth/revoke") { Content = new FormUrlEncodedContent(nvc) };
            //var res = await client.SendAsync(req);
        }
        private void CancelNotification()
        {
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1251);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1351);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1451);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1551);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1651);
        }
    }

  

    //protected override void OnAppearing()
    //{
    //    base.OnAppearing();
    //    MessagingCenter.Send(new UpdateSettingsMessage(), "UpdateSettingsMessage");
    //}


    //public override async void OnBeforeShow()
    //{
    //    base.OnBeforeShow();
    //    MessagingCenter.Send(new UpdateSettingsMessage(), "UpdateSettingsMessage");
    //}

    //protected override bool OnBackButtonPressed()
    //{
    //    Device.BeginInvokeOnMainThread(async () =>
    //    {
    //        var result = await DisplayAlert("Exit", "Are you sure you want to exit?", AppResources.Yes, AppResources.No);
    //        if (result)
    //        {
    //            var kill = DependencyService.Get<IKillAppService>();
    //            kill.ExitApp();
    //        }

    //    });

    //    return true;
    //}
}


