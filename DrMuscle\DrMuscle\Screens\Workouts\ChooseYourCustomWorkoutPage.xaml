﻿<?xml version="1.0" encoding="utf-8"?>
<t:DrMusclePage xmlns="http://xamarin.com/schemas/2014/forms" xmlns:helpers="clr-namespace:DrMuscle.Helpers" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" xmlns:local="clr-namespace:DrMuscle" xmlns:t="clr-namespace:DrMuscle.Layout"
xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
                xmlns:effects="clr-namespace:DrMuscle.Effects"
                                xmlns:app="clr-namespace:DrMuscle.Constants"

xmlns:s="clr-namespace:SegmentedControl.FormsPlugin.Abstractions;assembly=SegmentedControl.FormsPlugin.Abstractions" x:Class="DrMuscle.Screens.Workouts.ChooseYourCustomWorkoutPage"  xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView">
    <t:DrMusclePage.Resources>
        <ResourceDictionary>
            <helpers:NegateBooleanConverter x:Key="BooleanInverter" />
        </ResourceDictionary>
    </t:DrMusclePage.Resources>
    <t:DrMusclePage.Content>
    <AbsoluteLayout >
        

            <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,5,20,5" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
                <StackLayout BackgroundColor="Transparent" Padding="0" VerticalOptions="FillAndExpand">
                    <Label x:Name="LblMyWorkouts" Style="{StaticResource WorkoutLabelStyle}"  />
                    <Grid x:Name="workoutGrid">
                    <t:DrMuscleListView Grid.Row="0" x:Name="WorkoutListView" VerticalScrollBarVisibility="Never" Margin="0,0,0,5" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="White">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ViewCell Height="45">
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent">
                                        <Label Text="{Binding Label}" HorizontalOptions="StartAndExpand" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" />
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                                            <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                            <t:DrMuscleButton Clicked="OnRename" Text="{Binding [Rename].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextRenameButton}"  />
                                            <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}"  />
                                            <!--<t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />-->
                                            <t:DrMuscleButton Clicked="OnReset" Text="{Binding [More].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextResetButton}" />
                                            <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" IsVisible="{Binding IsSystemExercise, Converter={StaticResource BooleanInverter}}"  Style="{StaticResource ItemContextMoreButton}" />
                                        </StackLayout>
                                    </StackLayout>
                                </ViewCell>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </t:DrMuscleListView>
                    <StackLayout Grid.Row="0" x:Name="EmptyWorkouts" VerticalOptions="Start" IsVisible="false"  Spacing="10" Padding="0,25,0,2">

                <Image WidthRequest="70" HeightRequest="70" HorizontalOptions="Center" VerticalOptions="Start" Source="Lists.png" />


                    <Label Text="No custom workout yet" Margin="0,5,0,0" HorizontalOptions="Center" FontSize="Medium" Font="Bold,20" TextColor="Black" />
                    <Label Text="Tap &quot;+&quot; to create your custom workout" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

            </StackLayout>
                        </Grid>
                    <Label x:Name="LblMyPrograms" Style="{StaticResource WorkoutLabelStyle}" />
                    <Grid>
                    <t:DrMuscleListView Grid.Row="0" x:Name="ProgramListView" VerticalScrollBarVisibility="Never" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="White">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ViewCell Height="45">
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent">
                                        <Label Text="{Binding Label}" HorizontalOptions="StartAndExpand" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" />
                                        <!--                                        </StackLayout>-->
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                                            <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                            <t:DrMuscleButton Clicked="OnRename" Text="{Binding [Rename].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextRenameButton}"  />
                                            <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}"  />
                                            <t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                            <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" IsVisible="{Binding IsSystemExercise, Converter={StaticResource BooleanInverter}}"  Style="{StaticResource ItemContextMoreButton}" />
                                        </StackLayout>
                                    </StackLayout>
                                </ViewCell>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <ListView.Footer>
                            <BoxView HeightRequest="100" BackgroundColor="Transparent" />
                        </ListView.Footer>
                    </t:DrMuscleListView>
                    
                <StackLayout Grid.Row="0 " x:Name="EmptyProgram" VerticalOptions="Start"  IsVisible="false"  Spacing="10" Padding="0,25,0,2">

                <Image WidthRequest="70" HeightRequest="70" HorizontalOptions="Center" VerticalOptions="Start" Source="Lists.png" />


                    <Label Text="No custom program yet" Margin="0,5,0,0" HorizontalOptions="Center" FontSize="Medium" Font="Bold,20" TextColor="Black" />
                    <Label x:Name="LbEmptyProgram" Text="Tap &quot;+&quot; to create your custom program" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

            </StackLayout>
                    <!--<t:DrMuscleButton x:Name="WorkoutsButton" Text="New! Create on the Web" Style="{StaticResource buttonStyle}" Margin="0,15,0,0" />-->
                        </Grid>
                </StackLayout>


               




            </StackLayout>
        
        <Image x:Name="PlusIcon" Margin="0,0,20,20" HeightRequest="70" WidthRequest="70" VerticalOptions="Center" HorizontalOptions="Center" Aspect="AspectFit" AbsoluteLayout.LayoutFlags="PositionProportional" AbsoluteLayout.LayoutBounds="1, 1, 90, 90" effects:TooltipEffect.Text="Tap me"
                                effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Top"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="False" />
        <Image Source="PlusBlack.png" Margin="0,0,20,20" HeightRequest="70" WidthRequest="70" VerticalOptions="Center" HorizontalOptions="Center" Aspect="AspectFit" AbsoluteLayout.LayoutFlags="PositionProportional" AbsoluteLayout.LayoutBounds="1, 1, 90, 90">
            <Image.GestureRecognizers>
                <TapGestureRecognizer Tapped="NewTapped" />
            </Image.GestureRecognizers>

        </Image>
        <StackLayout x:Name="ActionStack" IsVisible="false" BackgroundColor="#55000000" VerticalOptions="FillAndExpand" Padding="20,5,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
            <StackLayout VerticalOptions="EndAndExpand">
                <pancakeView:PancakeView 
                    Style="{StaticResource PancakeViewStyleBlue}"
                                 Margin="0,15,0,0"
                OffsetAngle="90"
                                     HorizontalOptions="FillAndExpand" CornerRadius="0"  >

                    
                    <t:DrMuscleButton x:Name="workoutButton" Text="Custom workout" Style="{StaticResource highEmphasisButtonStyle}"  BackgroundColor="Transparent" VerticalOptions="End" />
                </pancakeView:PancakeView>
                <pancakeView:PancakeView 
                    Style="{StaticResource PancakeViewStyleBlue}"
                    HorizontalOptions="FillAndExpand"   OffsetAngle="90" CornerRadius="0"  >

                    
                    <t:DrMuscleButton x:Name="programButton" Text="Custom program" Style="{StaticResource highEmphasisButtonStyle}" BorderWidth="0" BorderColor="Transparent" BackgroundColor="Transparent" VerticalOptions="End" Margin="0,0,0,0" />
                </pancakeView:PancakeView>
                <pancakeView:PancakeView 
                    Style="{StaticResource PancakeViewStyleBlue}"
                                     HorizontalOptions="FillAndExpand"
                OffsetAngle="90" CornerRadius="0"  >

                    

                    <t:DrMuscleButton x:Name="UploadButton" Text="Upload program" Style="{StaticResource highEmphasisButtonStyle}" BorderWidth="0" BorderColor="Transparent" BackgroundColor="Transparent" VerticalOptions="End" Margin="0,0,0,0" />
                </pancakeView:PancakeView>
                <Image Source="PlusBlack.png"  Margin="0,0,0,20" HeightRequest="70" WidthRequest="70" VerticalOptions="End" HorizontalOptions="End" Aspect="AspectFit"  >
                    <Image.GestureRecognizers>
                        <TapGestureRecognizer Tapped="NewTapped" />
                    </Image.GestureRecognizers>
                </Image>

            </StackLayout>
            <StackLayout.GestureRecognizers>
                <TapGestureRecognizer Tapped="NewTapped" />
            </StackLayout.GestureRecognizers>
        </StackLayout>
    </AbsoluteLayout>
        </t:DrMusclePage.Content>
</t:DrMusclePage>