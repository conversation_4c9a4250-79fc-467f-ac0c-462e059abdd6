﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Acr.UserDialogs;
using DrMuscle.Dependencies;
using DrMuscle.Helpers;
using DrMuscle.Layout;
using DrMuscle.Message;
using DrMuscle.Resx;
using Xamarin.Forms;
using System.Linq;
using Plugin.Connectivity;

namespace DrMuscle.Screens.User
{
    public partial class InboxPage : DrMusclePage, IActiveAware
    {
        public event EventHandler IsActiveChanged;

        bool _isActive;
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    IsActiveChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }
        bool IsAdmin = false;
        string supportUrl = "";
        public ObservableCollection<Messages> messageList = new ObservableCollection<Messages>();

        public InboxPage()
        {
            InitializeComponent();
            RefreshLocalized();
            lstView.ItemsSource = messageList;
            //Test
            //SendBirdClient.Init("05F82C36-1159-4179-8C49-5910C7F51D7D");
            lstView.ItemTapped += LstView_ItemTapped;
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });

        }
        void RefreshLocalized()
        {
            Title = AppResources.Support;
        }
        public override void OnBeforeShow()
        {
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            if (messageList.Count == 0)
            {
                if (CrossConnectivity.Current.IsConnected)
                    UserDialogs.Instance.ShowLoading();
                
            }

            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                {
                    ConnectionErrorPopup();

                    return;
                }
                if (LocalDBManager.Instance.GetDBSetting("email") == null)
                    return;
                IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");
                
                Connect_Handler();
            }
            catch (Exception ex)
            {

            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
           
         
        }


        async void Connect_Handler()
        {
            try
            {

          
                if (!IsAdmin)
                {

                   
                    Messages messages = new Messages()
                    {
                        Message = AppResources.TapHereFor11Chat,
                        Nickname = AppResources.ChatWithSupport,
                        CreatedDate = DateTime.Now,
                        ChatType = ChannelType.Group,
                        UserId = "<EMAIL>"
                    };
                    messageList.Add(messages);

                }
                else
                {
                    //Fetch list of Group channel
                    // if (messageList.Count > 0)
                    //     return;
                    
                ObservableCollection<Messages> msgList = new ObservableCollection<Messages>();
                

                var list = await DrMuscleRestClient.Instance.FetchInbox(messageList.Count == 0 ? true : false);
                
                List<Messages> lst = new List<Messages>();
                            foreach (var item in list)
                            {
                                try
                                {
                                    Messages messages = new Messages()
                                    {
                                        Message = item.ChatModel.Message,
                                        Nickname = item.SenderName,
                                        CreatedDate = item.UpdatedAt,
                                        ChatType = ChannelType.Group,
                                        UserId = item.SenderEmail,
                                        AdminId = "<EMAIL>",
                                        SupportChannelUrl = item.SenderEmail.ToLower().Equals("<EMAIL>") ? item.ReceiverId : item.SenderId,
                                        IsUnread = !item.ChatModel.IsRead,
                                        NormalUSerEmail = item.SenderEmail == "<EMAIL>" ? item.ReceiverEmail : item.SenderEmail,
                                        NormalUSerName = item.SenderEmail == "<EMAIL>" ? item.ReceiverName : item.SenderName,
                                        ChatRoomId = item.Id,
                                        IsV1User = item.IsV1user,
                                        IsBothReplied = item.IsBothReplied
                                    };
                                    lst.Add(messages);

                                }
                                catch (Exception)
                                {

                                }
                            }
                            foreach (var item in lst.OrderByDescending(x => x.IsUnread).ToList())
                            {
                                msgList.Add(item);
                            }
                            Device.BeginInvokeOnMainThread(() =>
                            {
                                messageList = msgList;
                                lstView.ItemsSource = messageList;
                                lstView.ScrollToLast();
                                lstView.ScrollToFirst();
                            });

                        
                    

                }


            }
            catch (Exception e)
            {
            }

        }
        async void LstView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (!IsAdmin)
            {
                if (((Messages)e.Item).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ReceiverEmail = ((Messages)e.Item).NormalUSerEmail;
                    CurrentLog.Instance.ReceiverName = ((Messages)e.Item).NormalUSerName;
                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    await PagesFactory.PushAsync<SupportPage>();
                }
                else
                    await PagesFactory.PushAsync<ChatPage>();
            }
            else
            {
                if (((Messages)e.Item).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ReceiverEmail = ((Messages)e.Item).NormalUSerEmail;
                    CurrentLog.Instance.ReceiverName = ((Messages)e.Item).NormalUSerName;
                    CurrentLog.Instance.RoomId = ((Messages)e.Item).ChatRoomId;
                    CurrentLog.Instance.ChannelUrl = ((Messages)e.Item).SupportChannelUrl;

                    await PagesFactory.PushAsync<SupportPage>();
                }
                else
                    await PagesFactory.PushAsync<ChatPage>();
            }
        }

        void Handle_ItemAppearing(object sender, Xamarin.Forms.ItemVisibilityEventArgs e)
        {
            var itemTypeObject = e.Item as Messages;
           
        }

        void Handle_ItemDisappearing(object sender, Xamarin.Forms.ItemVisibilityEventArgs e)
        {

        }

    }
}
