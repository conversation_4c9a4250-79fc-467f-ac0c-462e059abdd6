<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>Dr. Muscle</string>
	<key>CFBundleIdentifier</key>
	<string>com.drmaxmuscle.max</string>
	<key>CFBundleName</key>
	<string>Dr. Muscle</string>
	<key>CFBundleShortVersionString</key>
	<string>2.7.2490</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1865252523754972</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.707210235326-ldcslmjtnjib5bklf23efrhp8u9qrpq3</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>drmuscle</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>2.7.2490</string>
	<key>FacebookAppID</key>
	<string>1865252523754972</string>
	<key>FacebookDisplayName</key>
	<string>Dr. Muscle</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fbauth</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
	<key>MinimumOSVersion</key>
	<string>14</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSHealthShareUsageDescription</key>
	<string>To save total workout duration </string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>To give total workout duration </string>
	<key>UIAppFonts</key>
	<array>
		<string>RobotoRegular.ttf</string>
		<string>JuraDemiBold.ttf</string>
		<string>Jura-Bold.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiresPersistentWiFi</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>XSAppIconAssets</key>
	<string>Assets.xcassets/AppIcon.appiconset</string>
	<key>branch_app_domain</key>
	<string>drmuscle.app.link</string>
	<key>branch_key</key>
	<string>key_live_neM4shDTT8ro8C0fJFXbidkaCwlAYzm7</string>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<string>BackgroundWorkTimerInBackground</string>
	</array>
	<key>FacebookClientToken</key>
	<string>ed86bf693c0253c017e9c96865a91c2b</string>
</dict>
</plist>
