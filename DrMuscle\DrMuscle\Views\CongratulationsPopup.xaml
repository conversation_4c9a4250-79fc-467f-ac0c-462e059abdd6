﻿<?xml version="1.0" encoding="UTF-8" ?>
<pages:PopupPage
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    CloseWhenBackgroundIsClicked="False"
    xmlns:t="clr-namespace:DrMuscle.Layout"
    xmlns:constants="clr-namespace:DrMuscle.Constants"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
    xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
    xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms"
    xmlns:forms="clr-namespace:Particle.Forms;assembly=Particle.Forms"
    BackgroundColor="#99000000"
    x:Class="DrMuscle.Views.CongratulationsPopup">
    <Frame
        Padding="0"
        CornerRadius="4"
        HasShadow="False"
        IsClippedToBounds="True"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="CenterAndExpand"
        BackgroundColor="Transparent"
        Margin="20,0,20,0">
        <Grid
            BackgroundColor="Transparent"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Center"
            Padding="0,0,0,15">

            <Frame
                Padding="0,0,0,20"
                Margin="0,10,0,0"
                CornerRadius="6"
                HasShadow="False"
                IsClippedToBounds="True"
                HorizontalOptions="FillAndExpand">
                <StackLayout
                    Padding="0,0,0,20">
                
                    <BoxView Margin="0,0,0,0" BackgroundColor="Transparent" />
                    <Image Margin="0,0,0,0" x:Name="ImgName" WidthRequest="100" HeightRequest="100" HorizontalOptions="Center" VerticalOptions="Start" Source="TrueState.png" />

                    <Label Margin="0,15,0,0" Text="Success!" x:Name="LblHeading" HorizontalOptions="Center" FontSize="26" FontAttributes="Bold" TextColor="Black" HorizontalTextAlignment="Center" />
                    <Label Text="Account created" x:Name="LblSubHead" Margin="15,0,15,10" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>
<!--How was today's workout?-->
                    <Label Text="" IsVisible="false" Margin="15,0,15,10" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

                    <t:DrMuscleButton Text="Send feedback" Clicked="DrMuscleButtonFeedback_Clicked" BackgroundColor="Transparent"  Style="{StaticResource menubuttonStyle}" Margin="25,10,25,0" HeightRequest="66" />
                    <t:DrMuscleButton Text="Share free trial" Clicked="DrMuscleButtonShareTrial_Clicked" BackgroundColor="Transparent" Style="{StaticResource menubuttonStyle}" Margin="25,0"  HeightRequest="66"  />
                    <pancakeView:PancakeView
            Padding="0"
            x:Name="OkAction"
            IsClippedToBounds="true"
            OffsetAngle="90"
            CornerRadius="0"
                        VerticalOptions="EndAndExpand"
                        HorizontalOptions="FillAndExpand"
                        Margin="25,0"
                        Style="{StaticResource PancakeViewStyleBlue}"
                        HeightRequest="66">
                 
                    <Label
                        x:Name="OkButton"
                        Text="Continue"
                        FontAttributes="Bold"
                        BackgroundColor="Transparent"
                        VerticalTextAlignment="Center"
                        TextColor="White"
                        HorizontalTextAlignment="Center"
                        HorizontalOptions="FillAndExpand"
                        Margin="0,0"
                        HeightRequest="55"
                         />
                    
                        </pancakeView:PancakeView>
                     
                    <Label Text="" x:Name="LblTipText" Margin="15,0,15,0"
HeightRequest="55"
HorizontalOptions="Center"
                          VerticalOptions="Center" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>
                     
                </StackLayout>
                
            </Frame>
            <!--<Image Grid.Row="0" Margin="0,25" Source="SharpCurve.png" HorizontalOptions="FillAndExpand" HeightRequest="120"  VerticalOptions="Start" Aspect="Fill" />-->
            <Image Grid.Row="0" Margin="0,50,0,20" Source="" WidthRequest="50" HeightRequest="50" HorizontalOptions="Center" VerticalOptions="Start" />
            <forms:ParticleView x:Name="MyParticleCanvas"
                              FallingParticlesPerSecond="25.0"
                              IsActive="False"
                              IsRunning="False"
                              HasFallingParticles="True"
                              VerticalOptions="FillAndExpand"
                              HorizontalOptions="FillAndExpand"
                              InputTransparent="True"/>
                    </Grid>
    </Frame>
       

</pages:PopupPage>
