<?xml version="1.0" encoding="utf-8"?>
<t:DrMusclePage xmlns="http://xamarin.com/schemas/2014/forms"
                xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                xmlns:oxy="clr-namespace:OxyPlot.Xamarin.Forms;assembly=OxyPlot.Xamarin.Forms"
                x:Class="DrMuscle.Screens.History.HistortWeightPage"
                xmlns:t="clr-namespace:DrMuscle.Layout"
                xmlns:control="clr-namespace:DrMuscle.Controls"
                xmlns:helpers="clr-namespace:DrMuscle.Helpers"
                xmlns:effects="clr-namespace:DrMuscle.Effects"
                xmlns:app="clr-namespace:DrMuscle.Constants"
                xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
                xmlns:s="clr-namespace:SegmentedControl.FormsPlugin.Abstractions;assembly=SegmentedControl.FormsPlugin.Abstractions"
                xmlns:history="clr-namespace:DrMuscle.Screens.HistortWeightPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <DataTemplate x:Key="historySetTemplate"
                          x:Name="SetTemplate">
                <ViewCell Height="45">
                    <StackLayout Orientation="Horizontal"
                                 Padding="20,0"
                                 BackgroundColor="Transparent"
                                 HeightRequest="45"
                                 VerticalOptions="Center">
                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                        <Label Margin="0,0,5,0"
                               Opacity="1"
                               Text="{Binding CreatedDate, StringFormat='{0:MMM d}'}"
                               Style="{StaticResource LabelStyle}"
                               WidthRequest="90"
                               VerticalTextAlignment="Center"
                               Font="16"
                               HorizontalOptions="Start" />
                        <Label Margin="0,0,5,0"
                               Opacity="1"
                               Text="{Binding Label}"
                               Style="{StaticResource LabelStyle}"
                               VerticalTextAlignment="Center"
                               Font="16"
                               HorizontalOptions="Start" HorizontalTextAlignment="Start" />
                        </StackLayout>
                        <StackLayout Orientation="Horizontal"
                                     HorizontalOptions="EndAndExpand">
                            <!-- <t:DrMuscleButton Clicked="OnCancelClicked" -->
                            <!--                   Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" -->
                            <!--                   CommandParameter="{Binding .}" -->
                            <!--                   IsVisible="false" -->
                            <!--                   HorizontalOptions="End" -->
                            <!--                   Style="{StaticResource ItemContextCancelButton}" /> -->
                            
                            <t:DrMuscleButton Clicked="OnEditClicked"
                                              Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="End"
                                              Style="{StaticResource ItemContextEditButton}" />
                            <t:DrMuscleButton Clicked="OnDeleteWeightClicked"
                                              Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                              CommandParameter="{Binding .}"
                                              IsVisible="false"
                                              HorizontalOptions="End"
                                              BackgroundColor="Red" BorderColor="Red" TextColor="White"
                                              Style="{StaticResource ItemContextDeleteButton}" />
                            <t:DrMuscleButton Clicked="OnContextMenuClicked"
                                              CommandParameter="{Binding .}"
                                              HorizontalOptions="End"
                                              
                                              Style="{StaticResource ItemContextMoreButton}" />
                        </StackLayout>
                    </StackLayout>
                </ViewCell>
            </DataTemplate>
            <DataTemplate x:Key="historyHeaderTemplate"
                          x:Name="SetHeaderTemplate">
                <ViewCell Height="40">
                    <StackLayout Orientation="Horizontal"
                                 BackgroundColor="#AAEEEEEE"
                                 Padding="20,0"
                                 HeightRequest="40"
                                 VerticalOptions="Center">
                        <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
                        <Label Margin="0,0,5,0"
                               Opacity="1"
                               Text="{Binding Label}"
                               Style="{StaticResource LabelStyle}"
                               VerticalTextAlignment="Center"
                               Font="Bold,15"
                               HorizontalOptions="Start" />
                    </StackLayout>
                </ViewCell>
            </DataTemplate>
        </ResourceDictionary>
        </ContentPage.Resources>
    <Grid>
    <StackLayout HorizontalOptions="FillAndExpand"
                 Padding="0,0,0,10"
                 Spacing="0"
                                      >

        <StackLayout Orientation="Horizontal"
                     BackgroundColor="Transparent"
                     HeightRequest="45"
                     Padding="20,0"
                     VerticalOptions="StartAndExpand">
            <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
            <Label Margin="0,0,5,0"
                   Opacity="1"
                   Text="DATE"
                   Style="{StaticResource LabelStyle}"
                   VerticalTextAlignment="Center"
                   Font="Bold,16"
                   WidthRequest="90"
                   HorizontalOptions="Start" >
                </Label>
            <Label Margin="0,0,5,0"
                   Opacity="1"
                   x:Name="massUnitType"
                   Text="KG"
                   Style="{StaticResource LabelStyle}"
                   VerticalTextAlignment="Center"
                   Font="Bold,16"
                   HorizontalOptions="Start" />

        </StackLayout>
            <t:DrMuscleListView x:Name="HistoryListView"
                                VerticalOptions="FillAndExpand"
                                BackgroundColor="Transparent"
                                SeparatorVisibility="None"
                                IsGroupingEnabled="False"
                                RowHeight="45" >
                
            </t:DrMuscleListView>
            
        
    </StackLayout>
    </Grid>
</t:DrMusclePage>