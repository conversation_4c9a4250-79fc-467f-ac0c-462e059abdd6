﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.props" Condition="Exists('..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.props')" />
  <Import Project="..\..\packages\Xamarin.Build.Download.0.11.4\build\Xamarin.Build.Download.props" Condition="Exists('..\..\packages\Xamarin.Build.Download.0.11.4\build\Xamarin.Build.Download.props')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Migration.1.0.10\build\monoandroid120\Xamarin.AndroidX.Migration.props" Condition="Exists('..\..\packages\Xamarin.AndroidX.Migration.1.0.10\build\monoandroid120\Xamarin.AndroidX.Migration.props')" />
  <Import Project="..\packages\xamstore-xamarin.inappbilling.1.5\build\monoandroid\xamstore-xamarin.inappbilling.props" Condition="Exists('..\packages\xamstore-xamarin.inappbilling.1.5\build\monoandroid\xamstore-xamarin.inappbilling.props')" />
  <Import Project="..\packages\Xamarin.Forms.2.5.0.121934\build\netstandard1.0\Xamarin.Forms.props" Condition="Exists('..\packages\Xamarin.Forms.2.5.0.121934\build\netstandard1.0\Xamarin.Forms.props')" />
  <Import Project="..\packages\Xamarin.Build.Download.0.4.7\build\Xamarin.Build.Download.props" Condition="Exists('..\packages\Xamarin.Build.Download.0.4.7\build\Xamarin.Build.Download.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{161F7FA3-9690-4E11-890B-F70A577C37CE}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DrMuscle.Droid</RootNamespace>
    <AssemblyName>DrMuscle.Droid</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <AndroidApplication>true</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.Designer.cs</AndroidResgenFile>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <TargetFrameworkVersion>v13.0</TargetFrameworkVersion>
    <AndroidSupportedAbis>armeabi-v7a;x86;x86_64</AndroidSupportedAbis>
    <AndroidStoreUncompressedFileExtensions />
    <MandroidI18n />
    <JavaMaximumHeapSize>2G</JavaMaximumHeapSize>
    <JavaOptions />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <AndroidTlsProvider>
    </AndroidTlsProvider>
    <AndroidEnableMultiDex>True</AndroidEnableMultiDex>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>True</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <JavaMaximumHeapSize>2G</JavaMaximumHeapSize>
    <AndroidKeyStore>True</AndroidKeyStore>
    <AndroidSigningKeyStore>../../publishingdoc.keystore</AndroidSigningKeyStore>
    <AndroidSigningStorePass>nmrojvbnpG2B</AndroidSigningStorePass>
    <AndroidSigningKeyAlias>publishingdoc</AndroidSigningKeyAlias>
    <AndroidSigningKeyPass>nmrojvbnpG2B</AndroidSigningKeyPass>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <BundleAssemblies>false</BundleAssemblies>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidLinkMode>None</AndroidLinkMode>
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <JavaMaximumHeapSize>2G</JavaMaximumHeapSize>
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
    <AndroidKeyStore>True</AndroidKeyStore>
    <AndroidSigningKeyStore>../../publishingdoc.keystore</AndroidSigningKeyStore>
    <AndroidSigningStorePass>nmrojvbnpG2B</AndroidSigningStorePass>
    <AndroidSigningKeyAlias>publishingdoc</AndroidSigningKeyAlias>
    <AndroidSigningKeyPass>nmrojvbnpG2B</AndroidSigningKeyPass>
    <AndroidPackageFormat>apk</AndroidPackageFormat>
    <!-- Configure Sentry org and project -->
    <SentryOrg>drmuscle-8da500fa5</SentryOrg>
    <SentryProject>dotnet-xamarin</SentryProject>
    <!--
      Each of the below features are opt-in.
      Enable the features you wish to use.
    -->
    <!-- Sends symbols to Sentry, enabling symbolication of stack traces. -->
    <SentryUploadSymbols>true</SentryUploadSymbols>
    <!-- Sends sources to Sentry, enabling display of source context. -->
    <SentryUploadSources>true</SentryUploadSources>
    <!-- If you are targeting Android, sends proguard mapping file to Sentry. -->
    <SentryUploadAndroidProguardMapping>true</SentryUploadAndroidProguardMapping>
    <AndroidSupportedAbis>armeabi-v7a;x86;x86_64;arm64-v8a</AndroidSupportedAbis>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AndHUD, Version=1.4.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AndHUD.1.4.3\lib\monoandroid81\AndHUD.dll</HintPath>
    </Reference>
    <Reference Include="FormsViewGroup, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.5.0.0.2622\lib\MonoAndroid13.0\FormsViewGroup.dll</HintPath>
    </Reference>
    <Reference Include="Google.ZXing.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.ZXing.Core.3.5.2.2\lib\monoandroid12.0\Google.ZXing.Core.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=1.3.6.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\..\packages\K4os.Compression.LZ4.1.3.6\lib\netstandard2.1\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter, Version=0.0.0.0, Culture=neutral, PublicKeyToken=8a600e2fee7ba272, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AppCenter.5.0.3\lib\MonoAndroid10.0\Microsoft.AppCenter.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter.Analytics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=8a600e2fee7ba272, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AppCenter.Analytics.5.0.3\lib\MonoAndroid10.0\Microsoft.AppCenter.Analytics.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter.Crashes, Version=0.0.0.0, Culture=neutral, PublicKeyToken=8a600e2fee7ba272, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AppCenter.Crashes.5.0.3\lib\MonoAndroid10.0\Microsoft.AppCenter.Crashes.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Android" />
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.13.0.3\lib\netstandard2.0\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="OxyPlot, Version=2.1.2.0, Culture=neutral, PublicKeyToken=638079a8f0bd61e9, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Core.2.1.2\lib\netstandard2.0\OxyPlot.dll</HintPath>
    </Reference>
    <Reference Include="OxyPlot.Xamarin.Android, Version=0.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Xamarin.Forms.2.1.0\lib\MonoAndroid10\OxyPlot.Xamarin.Android.dll</HintPath>
    </Reference>
    <Reference Include="OxyPlot.Xamarin.Forms, Version=2.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Xamarin.Forms.2.1.0\lib\MonoAndroid10\OxyPlot.Xamarin.Forms.dll</HintPath>
    </Reference>
    <Reference Include="OxyPlot.Xamarin.Forms.Platform.Android, Version=0.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Xamarin.Forms.2.1.0\lib\MonoAndroid10\OxyPlot.Xamarin.Forms.Platform.Android.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.InAppBilling, Version=6.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Plugin.InAppBilling.7.1.0\lib\monoandroid13.0\Plugin.InAppBilling.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.StoreReview, Version=6.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Plugin.StoreReview.6.2.0\lib\monoandroid12.0\Plugin.StoreReview.dll</HintPath>
    </Reference>
    <Reference Include="Rg.Plugins.Popup, Version=2.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Rg.Plugins.Popup.2.1.0\lib\monoandroid10.0\Rg.Plugins.Popup.dll</HintPath>
    </Reference>
    <Reference Include="SegmentedControl.FormsPlugin.Abstractions, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SegmentedControl.FormsPlugin.2.0.1\lib\MonoAndroid\SegmentedControl.FormsPlugin.Abstractions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SegmentedControl.FormsPlugin.Android, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SegmentedControl.FormsPlugin.2.0.1\lib\MonoAndroid\SegmentedControl.FormsPlugin.Android.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Sentry, Version=4.2.1.0, Culture=neutral, PublicKeyToken=fba2ec45388e2af0, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Sentry.4.2.1\lib\netstandard2.1\Sentry.dll</HintPath>
    </Reference>
    <Reference Include="Sentry.Android.AssemblyReader, Version=4.2.1.0, Culture=neutral, PublicKeyToken=fba2ec45388e2af0, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Sentry.Android.AssemblyReader.4.2.1\lib\netstandard2.0\Sentry.Android.AssemblyReader.dll</HintPath>
    </Reference>
    <Reference Include="Sentry.Xamarin, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Sentry.Xamarin.2.0.0\lib\monoandroid11.0\Sentry.Xamarin.dll</HintPath>
    </Reference>
    <Reference Include="Sentry.Xamarin.Forms, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Sentry.Xamarin.Forms.2.0.0\lib\netstandard2.0\Sentry.Xamarin.Forms.dll</HintPath>
    </Reference>
    <Reference Include="SkiaSharp, Version=2.88.0.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SkiaSharp.2.88.7\lib\monoandroid1.0\SkiaSharp.dll</HintPath>
    </Reference>
    <Reference Include="SkiaSharp.Views.Android, Version=2.88.0.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SkiaSharp.Views.2.88.7\lib\monoandroid1.0\SkiaSharp.Views.Android.dll</HintPath>
    </Reference>
    <Reference Include="SkiaSharp.Views.Forms, Version=2.88.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SkiaSharp.Views.Forms.2.88.7\lib\monoandroid1.0\SkiaSharp.Views.Forms.dll</HintPath>
    </Reference>
    <Reference Include="Splat, Version=14.8.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Splat.14.8.12\lib\netstandard2.0\Splat.dll</HintPath>
    </Reference>
    <Reference Include="SQLite-net, Version=1.8.116.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\sqlite-net-pcl.1.8.116\lib\netstandard2.0\SQLite-net.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.batteries_v2, Version=2.1.8.2226, Culture=neutral, PublicKeyToken=8226ea5df37bcae9, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCLRaw.bundle_green.2.1.8\lib\monoandroid90\SQLitePCLRaw.batteries_v2.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.core, Version=2.1.8.2226, Culture=neutral, PublicKeyToken=1488e028ca7ab535, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCLRaw.core.2.1.8\lib\netstandard2.0\SQLitePCLRaw.core.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.lib.e_sqlite3.android, Version=2.1.8.2226, Culture=neutral, PublicKeyToken=e4ad490600e2234c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCLRaw.lib.e_sqlite3.android.2.1.8\lib\monoandroid90\SQLitePCLRaw.lib.e_sqlite3.android.dll</HintPath>
    </Reference>
    <Reference Include="SQLitePCLRaw.provider.e_sqlite3, Version=1.1.14.520, Culture=neutral, PublicKeyToken=9c301db686d0bd12, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCLRaw.provider.e_sqlite3.android.1.1.14\lib\MonoAndroid\SQLitePCLRaw.provider.e_sqlite3.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Buffers.4.5.1\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Collections.Immutable.8.0.0\lib\netstandard2.0\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Memory.4.5.5\lib\netstandard2.0\System.Memory.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Reflection.Metadata, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Reflection.Metadata.8.0.0\lib\netstandard2.0\System.Reflection.Metadata.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Text.Encodings.Web.8.0.0\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Text.Json.8.0.3\lib\netstandard2.0\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Net.Http" />
    <Reference Include="Java.Interop" />
    <Reference Include="Xamarin.Android.Google.BillingClient, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Google.BillingClient.6.0.1.4\lib\monoandroid12.0\Xamarin.Android.Google.BillingClient.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Activity, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Activity.1.8.2.1\lib\monoandroid12.0\Xamarin.AndroidX.Activity.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Activity.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Activity.Ktx.1.8.2.1\lib\monoandroid12.0\Xamarin.AndroidX.Activity.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Annotation, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Annotation.1.7.1.1\lib\monoandroid12.0\Xamarin.AndroidX.Annotation.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Annotation.Experimental, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Annotation.Experimental.1.4.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Annotation.Experimental.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Annotation.Jvm, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Annotation.Jvm.1.7.1.1\lib\monoandroid12.0\Xamarin.AndroidX.Annotation.Jvm.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.AppCompat, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.AppCompat.1.6.1.7\lib\monoandroid12.0\Xamarin.AndroidX.AppCompat.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.AppCompat.AppCompatResources, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.AppCompat.AppCompatResources.*******\lib\monoandroid12.0\Xamarin.AndroidX.AppCompat.AppCompatResources.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Arch.Core.Common, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Arch.Core.Common.2.2.0.7\lib\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Common.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Arch.Core.Runtime, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Arch.Core.Runtime.2.2.0.7\lib\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.AsyncLayoutInflater, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.AsyncLayoutInflater.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.AsyncLayoutInflater.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.CardView, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.CardView.1.0.0.25\lib\monoandroid12.0\Xamarin.AndroidX.CardView.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Collection, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Collection.1.4.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Collection.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Collection.Jvm, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Collection.Jvm.1.4.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Collection.Jvm.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Collection.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Collection.Ktx.1.4.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Collection.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Concurrent.Futures, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Concurrent.Futures.********\lib\monoandroid12.0\Xamarin.AndroidX.Concurrent.Futures.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.ConstraintLayout, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.ConstraintLayout.2.1.4.10\lib\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.ConstraintLayout.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.ConstraintLayout.Core.1.0.4.10\lib\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.CoordinatorLayout, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.CoordinatorLayout.********\lib\monoandroid12.0\Xamarin.AndroidX.CoordinatorLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Core.1.12.0.4\lib\monoandroid12.0\Xamarin.AndroidX.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Core.Core.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Core.Core.Ktx.1.12.0.4\lib\monoandroid12.0\Xamarin.AndroidX.Core.Core.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.CursorAdapter, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.CursorAdapter.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.CursorAdapter.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.CustomView, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.CustomView.1.1.0.22\lib\monoandroid12.0\Xamarin.AndroidX.CustomView.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.CustomView.PoolingContainer, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.CustomView.PoolingContainer.1.0.0.9\lib\monoandroid12.0\Xamarin.AndroidX.CustomView.PoolingContainer.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.DocumentFile, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.DocumentFile.1.0.1.23\lib\monoandroid12.0\Xamarin.AndroidX.DocumentFile.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.DrawerLayout, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.DrawerLayout.1.2.0.7\lib\monoandroid12.0\Xamarin.AndroidX.DrawerLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.DynamicAnimation, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.DynamicAnimation.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.DynamicAnimation.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Emoji2, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Emoji2.1.4.0.4\lib\monoandroid12.0\Xamarin.AndroidX.Emoji2.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Emoji2.ViewsHelper, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Emoji2.ViewsHelper.1.4.0.4\lib\monoandroid12.0\Xamarin.AndroidX.Emoji2.ViewsHelper.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Fragment, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Fragment.1.6.2.2\lib\monoandroid12.0\Xamarin.AndroidX.Fragment.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Fragment.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Fragment.Ktx.1.6.2.2\lib\monoandroid12.0\Xamarin.AndroidX.Fragment.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Interpolator, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Interpolator.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.Interpolator.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Legacy.Support.Core.UI, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.UI.1.0.0.24\lib\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.UI.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Legacy.Support.Core.Utils, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.Utils.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.Utils.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Legacy.Support.V4, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Legacy.Support.V4.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.V4.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.Common, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.Common.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Common.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.LiveData.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.Process, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.Process.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Process.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.Runtime, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.Runtime.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.ViewModel, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.ViewModel.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.ViewModelSavedState, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.2.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Loader, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Loader.********\lib\monoandroid12.0\Xamarin.AndroidX.Loader.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.LocalBroadcastManager, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.LocalBroadcastManager.1.1.0.11\lib\monoandroid12.0\Xamarin.AndroidX.LocalBroadcastManager.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Media, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Media.1.7.0.1\lib\monoandroid12.0\Xamarin.AndroidX.Media.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.MediaRouter, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.MediaRouter.1.6.0.3\lib\monoandroid12.0\Xamarin.AndroidX.MediaRouter.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.MultiDex, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.MultiDex.2.0.1.23\lib\monoandroid12.0\Xamarin.AndroidX.MultiDex.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Navigation.Common, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Navigation.Common.*******\lib\monoandroid12.0\Xamarin.AndroidX.Navigation.Common.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Navigation.Runtime, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Navigation.Runtime.*******\lib\monoandroid12.0\Xamarin.AndroidX.Navigation.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Palette, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Palette.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.Palette.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Preference, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Preference.1.2.1.4\lib\monoandroid12.0\Xamarin.AndroidX.Preference.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Print, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Print.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.Print.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.ProfileInstaller.ProfileInstaller, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.1.3.1.6\lib\monoandroid12.0\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.RecyclerView, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.RecyclerView.1.3.2.2\lib\monoandroid12.0\Xamarin.AndroidX.RecyclerView.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.ResourceInspection.Annotation, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.ResourceInspection.Annotation.1.0.1.11\lib\monoandroid12.0\Xamarin.AndroidX.ResourceInspection.Annotation.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.SavedState, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.SavedState.1.2.1.7\lib\monoandroid12.0\Xamarin.AndroidX.SavedState.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.SavedState.SavedState.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.SavedState.SavedState.Ktx.1.2.1.7\lib\monoandroid12.0\Xamarin.AndroidX.SavedState.SavedState.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.SlidingPaneLayout, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.SlidingPaneLayout.********\lib\monoandroid12.0\Xamarin.AndroidX.SlidingPaneLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Startup.StartupRuntime, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Startup.StartupRuntime.********\lib\monoandroid12.0\Xamarin.AndroidX.Startup.StartupRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.SwipeRefreshLayout, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.SwipeRefreshLayout.********\lib\monoandroid12.0\Xamarin.AndroidX.SwipeRefreshLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Tracing.Tracing, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Tracing.Tracing.*******\lib\monoandroid12.0\Xamarin.AndroidX.Tracing.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Transition, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Transition.********\lib\monoandroid12.0\Xamarin.AndroidX.Transition.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.VectorDrawable, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.VectorDrawable.********\lib\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.VectorDrawable.Animated, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.VectorDrawable.Animated.********\lib\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.Animated.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.VersionedParcelable, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.VersionedParcelable.*******\lib\monoandroid12.0\Xamarin.AndroidX.VersionedParcelable.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.ViewPager, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.ViewPager.1.0.0.23\lib\monoandroid12.0\Xamarin.AndroidX.ViewPager.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.ViewPager2, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.ViewPager2.1.0.0.25\lib\monoandroid12.0\Xamarin.AndroidX.ViewPager2.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Window, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Window.1.2.0.2\lib\monoandroid12.0\Xamarin.AndroidX.Window.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Window.Extensions.Core.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.AndroidX.Window.Extensions.Core.Core.1.0.0.5\lib\monoandroid12.0\Xamarin.AndroidX.Window.Extensions.Core.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Essentials, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Essentials.1.8.1\lib\monoandroid13.0\Xamarin.Essentials.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Abt, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Abt.121.1.1.5\lib\monoandroid12.0\Xamarin.Firebase.Abt.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Analytics, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Analytics.121.3.0.4\lib\monoandroid12.0\Xamarin.Firebase.Analytics.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Annotations, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Annotations.116.2.0.5\lib\monoandroid12.0\Xamarin.Firebase.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Common, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Common.120.4.2.1\lib\monoandroid12.0\Xamarin.Firebase.Common.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Common.Ktx, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Common.Ktx.120.4.2.1\lib\monoandroid12.0\Xamarin.Firebase.Common.Ktx.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Components, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Components.117.1.5.1\lib\monoandroid12.0\Xamarin.Firebase.Components.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Config, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Config.121.5.0.1\lib\monoandroid12.0\Xamarin.Firebase.Config.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Datatransport, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Datatransport.118.2.0.3\lib\monoandroid12.0\Xamarin.Firebase.Datatransport.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Encoders, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Encoders.117.0.0.13\lib\monoandroid12.0\Xamarin.Firebase.Encoders.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Encoders.JSON, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Encoders.JSON.118.0.1.5\lib\monoandroid12.0\Xamarin.Firebase.Encoders.JSON.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Encoders.Proto, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Encoders.Proto.116.0.0.8\lib\monoandroid12.0\Xamarin.Firebase.Encoders.Proto.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Iid, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Iid.121.1.0.13\lib\monoandroid12.0\Xamarin.Firebase.Iid.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Iid.Interop, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Iid.Interop.117.1.0.13\lib\monoandroid12.0\Xamarin.Firebase.Iid.Interop.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Installations, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Installations.117.2.0.1\lib\monoandroid12.0\Xamarin.Firebase.Installations.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Installations.InterOp, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Installations.InterOp.117.2.0.1\lib\monoandroid12.0\Xamarin.Firebase.Installations.InterOp.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Measurement.Connector, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Measurement.Connector.120.0.1.1\lib\monoandroid12.0\Xamarin.Firebase.Measurement.Connector.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Firebase.Messaging, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.Messaging.123.3.1.1\lib\monoandroid12.0\Xamarin.Firebase.Messaging.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Forms.CarouselView, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.CarouselView.2.3.0-pre2\lib\MonoAndroid10\Xamarin.Forms.CarouselView.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Core, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.5.0.0.2622\lib\MonoAndroid13.0\Xamarin.Forms.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Forms.Platform, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.5.0.0.2622\lib\MonoAndroid13.0\Xamarin.Forms.Platform.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Forms.Platform.Android, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.5.0.0.2622\lib\MonoAndroid13.0\Xamarin.Forms.Platform.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Forms.Xaml, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.5.0.0.2622\lib\MonoAndroid13.0\Xamarin.Forms.Xaml.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.Android.DataTransport.TransportApi, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.Android.DataTransport.TransportApi.3.0.0.10\lib\monoandroid12.0\Xamarin.Google.Android.DataTransport.TransportApi.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.Android.DataTransport.TransportBackendCct, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.Android.DataTransport.TransportBackendCct.3.1.9.4\lib\monoandroid12.0\Xamarin.Google.Android.DataTransport.TransportBackendCct.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.Android.DataTransport.TransportRuntime, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.Android.DataTransport.TransportRuntime.3.1.9.4\lib\monoandroid12.0\Xamarin.Google.Android.DataTransport.TransportRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.Android.Play.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.Android.Play.Core.1.10.3.9\lib\monoandroid12.0\Xamarin.Google.Android.Play.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.AutoValue.Annotations, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.AutoValue.Annotations.1.10.4.3\lib\monoandroid12.0\Xamarin.Google.AutoValue.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.Dagger, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.Dagger.2.48.1.1\lib\monoandroid12.0\Xamarin.Google.Dagger.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.ErrorProne.Annotations, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.ErrorProne.Annotations.2.23.0.1\lib\monoandroid12.0\Xamarin.Google.ErrorProne.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.Guava.ListenableFuture, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.Guava.ListenableFuture.1.0.0.18\lib\monoandroid12.0\Xamarin.Google.Guava.ListenableFuture.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Ads.Identifier, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Ads.Identifier.118.0.1.8\lib\monoandroid12.0\Xamarin.GooglePlayServices.Ads.Identifier.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Auth, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Auth.120.7.0.2\lib\monoandroid12.0\Xamarin.GooglePlayServices.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Auth.Api.Phone, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Auth.Api.Phone.118.0.1.8\lib\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Api.Phone.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Auth.Base, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Auth.Base.118.0.10.2\lib\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Base.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Base, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Base.118.2.0.5\lib\monoandroid12.0\Xamarin.GooglePlayServices.Base.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Basement, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Basement.118.2.0.5\lib\monoandroid12.0\Xamarin.GooglePlayServices.Basement.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.CloudMessaging, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.CloudMessaging.117.0.2.8\lib\monoandroid12.0\Xamarin.GooglePlayServices.CloudMessaging.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Fido, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Fido.120.1.0.1\lib\monoandroid12.0\Xamarin.GooglePlayServices.Fido.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Measurement, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Measurement.121.3.0.4\lib\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Measurement.Api, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Measurement.Api.121.3.0.4\lib\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Api.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Measurement.Base, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Measurement.Base.121.3.0.4\lib\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Base.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Measurement.Impl, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Measurement.Impl.121.3.0.4\lib\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Measurement.Sdk, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.121.3.0.4\lib\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Measurement.Sdk.Api, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.Api.121.3.0.4\lib\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.Api.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Stats, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Stats.117.0.3.8\lib\monoandroid12.0\Xamarin.GooglePlayServices.Stats.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.GooglePlayServices.Tasks, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.GooglePlayServices.Tasks.118.0.2.6\lib\monoandroid12.0\Xamarin.GooglePlayServices.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.JavaX.Inject, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.JavaX.Inject.1.0.0.11\lib\monoandroid12.0\Xamarin.JavaX.Inject.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Jetbrains.Annotations, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Jetbrains.Annotations.24.1.0.2\lib\monoandroid12.0\Xamarin.Jetbrains.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Kotlin.StdLib, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Kotlin.StdLib.1.9.23\lib\monoandroid12.0\Xamarin.Kotlin.StdLib.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Kotlin.StdLib.Common, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Kotlin.StdLib.Common.1.9.23\lib\monoandroid12.0\Xamarin.Kotlin.StdLib.Common.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Kotlin.StdLib.Jdk7, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Kotlin.StdLib.Jdk7.1.9.23\lib\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk7.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Kotlin.StdLib.Jdk8, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Kotlin.StdLib.Jdk8.1.9.23\lib\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk8.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.KotlinX.Coroutines.Android, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.KotlinX.Coroutines.Android.1.8.0.1\lib\monoandroid12.0\Xamarin.KotlinX.Coroutines.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.KotlinX.Coroutines.Core, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.KotlinX.Coroutines.Core.1.8.0.1\lib\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.KotlinX.Coroutines.Core.Jvm, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.KotlinX.Coroutines.Core.Jvm.1.8.0.1\lib\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.Jvm.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.KotlinX.Coroutines.Play.Services, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.KotlinX.Coroutines.Play.Services.1.7.3.3\lib\monoandroid12.0\Xamarin.KotlinX.Coroutines.Play.Services.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Plugin.Calendar, Version=2.0.9699.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Plugin.Calendar.2.0.9699\lib\monoandroid10.0\Xamarin.Plugin.Calendar.dll</HintPath>
    </Reference>
    <Reference Include="XFShapeView">
      <HintPath>..\..\packages\VG.XFShapeView.1.0.5\lib\MonoAndroid10\XFShapeView.dll</HintPath>
    </Reference>
    <Reference Include="XFShapeView.Droid">
      <HintPath>..\..\packages\VG.XFShapeView.1.0.5\lib\MonoAndroid10\XFShapeView.Droid.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.Connectivity.Abstractions">
      <HintPath>..\..\packages\Xam.Plugin.Connectivity.3.2.0\lib\MonoAndroid10\Plugin.Connectivity.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.Connectivity">
      <HintPath>..\..\packages\Xam.Plugin.Connectivity.3.2.0\lib\MonoAndroid10\Plugin.Connectivity.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.Settings.Abstractions">
      <HintPath>..\..\packages\Xam.Plugins.Settings.3.1.1\lib\MonoAndroid10\Plugin.Settings.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.Settings">
      <HintPath>..\..\packages\Xam.Plugins.Settings.3.1.1\lib\MonoAndroid10\Plugin.Settings.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.2.4.11.982\lib\MonoAndroid10\FFImageLoading.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading.Platform">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.2.4.11.982\lib\MonoAndroid10\FFImageLoading.Platform.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading.Transformations">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.Transformations.2.4.11.982\lib\MonoAndroid10\FFImageLoading.Transformations.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading.Forms">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.Forms.2.4.11.982\lib\MonoAndroid10\FFImageLoading.Forms.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading.Forms.Platform">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.Forms.2.4.11.982\lib\MonoAndroid10\FFImageLoading.Forms.Platform.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.Toast.Abstractions">
      <HintPath>..\..\packages\Plugin.Toast.2.2.0\lib\MonoAndroid10\Plugin.Toast.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.Toast">
      <HintPath>..\..\packages\Plugin.Toast.2.2.0\lib\MonoAndroid10\Plugin.Toast.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.Core.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.Core.Android.1*******\lib\monoandroid90\Xamarin.Facebook.Core.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.AppLinks.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.AppLinks.Android.1*******\lib\monoandroid90\Xamarin.Facebook.AppLinks.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.Messenger.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.Messenger.Android.1*******\lib\monoandroid90\Xamarin.Facebook.Messenger.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.Places.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.Places.Android.7.1.0\lib\monoandroid90\Xamarin.Facebook.Places.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.Common.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.Common.Android.1*******\lib\monoandroid90\Xamarin.Facebook.Common.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.Login.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.Login.Android.1*******\lib\monoandroid90\Xamarin.Facebook.Login.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.Share.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.Share.Android.1*******\lib\monoandroid90\Xamarin.Facebook.Share.Android.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.Android.1*******\lib\monoandroid90\Xamarin.Facebook.Android.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.LatestVersion">
      <HintPath>..\..\packages\Xam.Plugin.LatestVersion.2.1.0\lib\monoandroid71\Plugin.LatestVersion.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Arch.Core.Common">
      <HintPath>..\..\packages\Xamarin.Android.Arch.Core.Common.1.1.1.3\lib\monoandroid90\Xamarin.Android.Arch.Core.Common.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Arch.Lifecycle.Common">
      <HintPath>..\..\packages\Xamarin.Android.Arch.Lifecycle.Common.1.1.1.3\lib\monoandroid90\Xamarin.Android.Arch.Lifecycle.Common.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Arch.Lifecycle.Runtime">
      <HintPath>..\..\packages\Xamarin.Android.Arch.Lifecycle.Runtime.1.1.1.3\lib\monoandroid90\Xamarin.Android.Arch.Lifecycle.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Collections">
      <HintPath>..\..\packages\Xamarin.Android.Support.Collections.********\lib\monoandroid90\Xamarin.Android.Support.Collections.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.VersionedParcelable">
      <HintPath>..\..\packages\Xamarin.Android.Support.VersionedParcelable.********\lib\monoandroid90\Xamarin.Android.Support.VersionedParcelable.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Compat">
      <HintPath>..\..\packages\Xamarin.Android.Support.Compat.********\lib\monoandroid90\Xamarin.Android.Support.Compat.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Arch.Core.Runtime">
      <HintPath>..\..\packages\Xamarin.Android.Arch.Core.Runtime.1.1.1.3\lib\monoandroid90\Xamarin.Android.Arch.Core.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Arch.Lifecycle.LiveData.Core">
      <HintPath>..\..\packages\Xamarin.Android.Arch.Lifecycle.LiveData.Core.1.1.1.3\lib\monoandroid90\Xamarin.Android.Arch.Lifecycle.LiveData.Core.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Arch.Lifecycle.LiveData">
      <HintPath>..\..\packages\Xamarin.Android.Arch.Lifecycle.LiveData.1.1.1.3\lib\monoandroid90\Xamarin.Android.Arch.Lifecycle.LiveData.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Arch.Lifecycle.ViewModel">
      <HintPath>..\..\packages\Xamarin.Android.Arch.Lifecycle.ViewModel.1.1.1.3\lib\monoandroid90\Xamarin.Android.Arch.Lifecycle.ViewModel.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.CursorAdapter">
      <HintPath>..\..\packages\Xamarin.Android.Support.CursorAdapter.********\lib\monoandroid90\Xamarin.Android.Support.CursorAdapter.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.DocumentFile">
      <HintPath>..\..\packages\Xamarin.Android.Support.DocumentFile.********\lib\monoandroid90\Xamarin.Android.Support.DocumentFile.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Interpolator">
      <HintPath>..\..\packages\Xamarin.Android.Support.Interpolator.********\lib\monoandroid90\Xamarin.Android.Support.Interpolator.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.LocalBroadcastManager">
      <HintPath>..\..\packages\Xamarin.Android.Support.LocalBroadcastManager.********\lib\monoandroid90\Xamarin.Android.Support.LocalBroadcastManager.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Print">
      <HintPath>..\..\packages\Xamarin.Android.Support.Print.********\lib\monoandroid90\Xamarin.Android.Support.Print.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.AsyncLayoutInflater">
      <HintPath>..\..\packages\Xamarin.Android.Support.AsyncLayoutInflater.********\lib\monoandroid90\Xamarin.Android.Support.AsyncLayoutInflater.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.CustomView">
      <HintPath>..\..\packages\Xamarin.Android.Support.CustomView.********\lib\monoandroid90\Xamarin.Android.Support.CustomView.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.CardView">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.CardView.********\lib\monoandroid90\Xamarin.Android.Support.v7.CardView.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.CoordinaterLayout">
      <HintPath>..\..\packages\Xamarin.Android.Support.CoordinaterLayout.********\lib\monoandroid90\Xamarin.Android.Support.CoordinaterLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.DrawerLayout">
      <HintPath>..\..\packages\Xamarin.Android.Support.DrawerLayout.********\lib\monoandroid90\Xamarin.Android.Support.DrawerLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Loader">
      <HintPath>..\..\packages\Xamarin.Android.Support.Loader.********\lib\monoandroid90\Xamarin.Android.Support.Loader.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.SlidingPaneLayout">
      <HintPath>..\..\packages\Xamarin.Android.Support.SlidingPaneLayout.********\lib\monoandroid90\Xamarin.Android.Support.SlidingPaneLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.SwipeRefreshLayout">
      <HintPath>..\..\packages\Xamarin.Android.Support.SwipeRefreshLayout.********\lib\monoandroid90\Xamarin.Android.Support.SwipeRefreshLayout.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.Palette">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.Palette.********\lib\monoandroid90\Xamarin.Android.Support.v7.Palette.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Vector.Drawable">
      <HintPath>..\..\packages\Xamarin.Android.Support.Vector.Drawable.********\lib\monoandroid90\Xamarin.Android.Support.Vector.Drawable.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.ViewPager">
      <HintPath>..\..\packages\Xamarin.Android.Support.ViewPager.********\lib\monoandroid90\Xamarin.Android.Support.ViewPager.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Animated.Vector.Drawable">
      <HintPath>..\..\packages\Xamarin.Android.Support.Animated.Vector.Drawable.********\lib\monoandroid90\Xamarin.Android.Support.Animated.Vector.Drawable.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.CustomTabs">
      <HintPath>..\..\packages\Xamarin.Android.Support.CustomTabs.********\lib\monoandroid90\Xamarin.Android.Support.CustomTabs.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Transition">
      <HintPath>..\..\packages\Xamarin.Android.Support.Transition.********\lib\monoandroid90\Xamarin.Android.Support.Transition.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v4">
      <HintPath>..\..\packages\Xamarin.Android.Support.v4.********\lib\monoandroid90\Xamarin.Android.Support.v4.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.AppCompat">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.AppCompat.********\lib\monoandroid90\Xamarin.Android.Support.v7.AppCompat.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.RecyclerView">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.RecyclerView.********\lib\monoandroid90\Xamarin.Android.Support.v7.RecyclerView.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Design">
      <HintPath>..\..\packages\Xamarin.Android.Support.Design.********\lib\monoandroid90\Xamarin.Android.Support.Design.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.MediaRouter">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.MediaRouter.********\lib\monoandroid90\Xamarin.Android.Support.v7.MediaRouter.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Annotations">
      <HintPath>..\..\packages\Xamarin.Android.Support.Annotations.********\lib\monoandroid90\Xamarin.Android.Support.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Core.Utils">
      <HintPath>..\..\packages\Xamarin.Android.Support.Core.Utils.********\lib\monoandroid90\Xamarin.Android.Support.Core.Utils.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Media.Compat">
      <HintPath>..\..\packages\Xamarin.Android.Support.Media.Compat.********\lib\monoandroid90\Xamarin.Android.Support.Media.Compat.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Fragment">
      <HintPath>..\..\packages\Xamarin.Android.Support.Fragment.********\lib\monoandroid90\Xamarin.Android.Support.Fragment.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Core.UI">
      <HintPath>..\..\packages\Xamarin.Android.Support.Core.UI.********\lib\monoandroid90\Xamarin.Android.Support.Core.UI.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
    <Reference Include="SlideOverKit">
      <HintPath>..\..\packages\SlideOverKit.*******\lib\MonoAndroid10\SlideOverKit.dll</HintPath>
    </Reference>
    <Reference Include="SlideOverKit.Droid">
      <HintPath>..\..\packages\SlideOverKit.*******\lib\MonoAndroid10\SlideOverKit.Droid.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.CurrentActivity">
      <HintPath>..\..\packages\Plugin.CurrentActivity.*******\lib\monoandroid44\Plugin.CurrentActivity.dll</HintPath>
    </Reference>
    <Reference Include="Tooltip">
      <HintPath>..\..\packages\XamarinAndroidTooltip.3.0.0\lib\monoandroid90\Tooltip.dll</HintPath>
    </Reference>
    <Reference Include="Naxam.TargetTooltip.Droid">
      <HintPath>..\..\packages\Naxam.TargetTooltip.Droid.1.3.15\lib\MonoAndroid10\Naxam.TargetTooltip.Droid.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.Vibrate">
      <HintPath>..\..\packages\Xam.Plugins.Vibrate.*******\lib\monoandroid60\Plugin.Vibrate.dll</HintPath>
    </Reference>
    <Reference Include="Acr.UserDialogs">
      <HintPath>..\..\packages\Acr.UserDialogs.7.2.0.564\lib\monoandroid10.0\Acr.UserDialogs.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Browser">
      <HintPath>..\..\packages\Xamarin.AndroidX.Browser.*******\lib\monoandroid90\Xamarin.AndroidX.Browser.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Lifecycle.LiveData">
      <HintPath>..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.*******\lib\monoandroid90\Xamarin.AndroidX.Lifecycle.LiveData.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Forms.PancakeView">
      <HintPath>..\..\packages\Xamarin.Forms.PancakeView.2.3.0.759\lib\monoandroid10.0\Xamarin.Forms.PancakeView.dll</HintPath>
    </Reference>
    <Reference Include="Bolts.AppLinks">
      <HintPath>..\..\packages\Bolts.1.4.0.1\lib\MonoAndroid403\Bolts.AppLinks.dll</HintPath>
    </Reference>
    <Reference Include="Bolts.Tasks">
      <HintPath>..\..\packages\Bolts.1.4.0.1\lib\MonoAndroid403\Bolts.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="Branch-Xamarin-Lib.Droid">
      <HintPath>..\..\packages\Branch-Xamarin-Linking-SDK.7.0.7\lib\MonoAndroid\Branch-Xamarin-Lib.Droid.dll</HintPath>
    </Reference>
    <Reference Include="Branch-Xamarin-SDK">
      <HintPath>..\..\packages\Branch-Xamarin-Linking-SDK.7.0.7\lib\MonoAndroid\Branch-Xamarin-SDK.dll</HintPath>
    </Reference>
    <Reference Include="Branch-Xamarin-SDK.Droid">
      <HintPath>..\..\packages\Branch-Xamarin-Linking-SDK.7.0.7\lib\MonoAndroid\Branch-Xamarin-SDK.Droid.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.GoogleClient">
      <HintPath>..\..\packages\Plugin.GoogleClient.2.1.12\lib\monoandroid10.0\Plugin.GoogleClient.dll</HintPath>
    </Reference>
    <Reference Include="Plugin.FirebasePushNotification">
      <HintPath>..\..\packages\Plugin.FirebasePushNotification.3.4.35\lib\monoandroid12.0\Plugin.FirebasePushNotification.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.AndroidX.Navigation.UI">
      <HintPath>..\..\packages\Xamarin.AndroidX.Navigation.UI.2.3.5.5\lib\monoandroid12.0\Xamarin.AndroidX.Navigation.UI.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Android.Binding.InstallReferrer">
      <HintPath>..\..\packages\Xamarin.Android.Binding.InstallReferrer.2.2.0\lib\MonoAndroid10\Xamarin.Android.Binding.InstallReferrer.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Facebook.GamingServices.Android">
      <HintPath>..\..\packages\Xamarin.Facebook.GamingServices.Android.1*******\lib\monoandroid90\Xamarin.Facebook.GamingServices.Android.dll</HintPath>
    </Reference>
    <Reference Include="Microcharts">
      <HintPath>..\..\packages\Microcharts.Forms.1.0.0-preview1\lib\netstandard2.0\Microcharts.dll</HintPath>
    </Reference>
    <Reference Include="Microcharts.Droid">
      <HintPath>..\..\packages\Microcharts.Android.1.0.0-preview1\lib\MonoAndroid10\Microcharts.Droid.dll</HintPath>
    </Reference>
    <Reference Include="Microcharts.Forms">
      <HintPath>..\..\packages\Microcharts.Forms.1.0.0-preview1\lib\netstandard2.0\Microcharts.Forms.dll</HintPath>
    </Reference>
    <Reference Include="SkiaChart">
      <HintPath>..\..\packages\SkiaChart.Forms.1.2.50\lib\netstandard2.0\SkiaChart.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading.Svg.Platform">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.Svg.2.4.11.982\lib\MonoAndroid10\FFImageLoading.Svg.Platform.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading.Svg.Forms">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.Svg.Forms.2.4.11.982\lib\MonoAndroid10\FFImageLoading.Svg.Forms.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Google.Android.Material">
      <HintPath>..\..\packages\Xamarin.Google.Android.Material.1.4.0.6\lib\monoandroid12.0\Xamarin.Google.Android.Material.dll</HintPath>
    </Reference>
    <Reference Include="Particle.Forms">
      <HintPath>..\..\packages\particle.forms.1.0.0\lib\netstandard2.0\Particle.Forms.dll</HintPath>
    </Reference>
    <Reference Include="ImageFromXamarinUI">
      <HintPath>..\..\packages\ImageFromXamarinUI.1.0.0\lib\monoandroid10.0\ImageFromXamarinUI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DrMuscleEntryRenderer.cs" />
    <Compile Include="FacebookLoginButtonRenderer.cs" />
    <Compile Include="FacebookManager_Droid.cs" />
    <Compile Include="Renderer\WindowBackgroundColorImplementation.cs" />
    <Compile Include="Service\NetworkCheck.cs" />
    <Compile Include="SplashActivity.cs" />
    <Compile Include="DrMuscleSubscription_Droid.cs" />
    <Compile Include="GenericButtonRenderer.cs" />
    <Compile Include="MainActivity.cs" />
    <Compile Include="MainApplication.cs" />
    <Compile Include="PurchaseManager.cs" />
    <Compile Include="Resources.cs" />
    <Compile Include="Resources\Resource.Designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SQLite_Android.cs" />
    <Compile Include="Styles_Droid.cs" />
    <Compile Include="AudioService.cs" />
    <Compile Include="Firebase_Droid.cs" />
    <Compile Include="CustomSwitchRenderer.cs" />
    <Compile Include="CustomPickerRenderer.cs" />
    <Compile Include="KillAppService.cs" />
    <Compile Include="DrMusclePageRenderer.cs" />
    <Compile Include="Localize.cs" />
    <Compile Include="ExtendedEntry.cs" />
    <Compile Include="Renderer\DrMuscleEditorRenderer.cs" />
    <Compile Include="Helpers\Settings.cs" />
    <Compile Include="Renderer\CustomEditorRenderer.cs" />
    <Compile Include="CustomTabbedPageRenderer.cs" />
    <Compile Include="Renderer\AutoBotListView.cs" />
    <Compile Include="Renderer\DrMuscleScrollView.cs" />
    <Compile Include="Renderer\ZoomableScrollviewRenderer.cs" />
    <Compile Include="Renderer\ExtendedLabelRenderer.cs" />
    <Compile Include="Renderer\ScreenshotService.cs" />
    <Compile Include="Renderer\HyperlinkLabelRenderer.cs" />
    <Compile Include="Renderer\CustomPushHandlerForFirebaseNotification.cs" />
    <Compile Include="Service\VersionInfoService.cs" />
    <Compile Include="Renderer\DrMuscleEntryRenderer.cs" />
    <Compile Include="Renderer\ListViewRenderer.cs" />
    <Compile Include="Renderer\KeyboardHelper.cs" />
    <Compile Include="Effects\DropShadowEffect.cs" />
    <Compile Include="Effects\ListViewSortableEffect.cs" />
    <Compile Include="Service\AlarmAndNotificationService.cs" />
    <Compile Include="BroadcastReceiver\AlarmReceiver.cs" />
    <Compile Include="Renderer\TimePickerRender.cs" />
    <Compile Include="Renderer\ExtendedLightBlueLabelRender.cs" />
    <Compile Include="Renderer\EnterFullScreenRequestedEventArgs.cs" />
    <Compile Include="Renderer\FullScreenEnabledWebChromeClient.cs" />
    <Compile Include="Renderer\FullScreenEnabledWebViewRenderer.cs" />
    <Compile Include="Renderer\DropDownPickerRenderer.cs" />
    <Compile Include="Helpers\BadgeView.cs" />
    <Compile Include="Helpers\Extensions.cs" />
    <Compile Include="Renderer\AutoSizeLabelRenderer.cs" />
    <Compile Include="Renderer\MyShellRenderer.cs" />
    <Compile Include="Service\WearService.cs" />
    <Compile Include="Effects\TooltipEffect.cs" />
    <Compile Include="FormsVideoLibrary\VideoPicker.cs" />
    <Compile Include="FormsVideoLibrary\VideoPlayerRenderer.cs" />
    <Compile Include="Renderer\ExtendedButtonRenderer.cs" />
    <Compile Include="Renderer\SetsCellRenderer.cs" />
    <Compile Include="Renderer\CustomFrameShadowRenderer.cs" />
    <Compile Include="Service\MyRemoteConfigurationService.cs" />
    <Compile Include="Service\OpenImplementation.cs" />
    <Compile Include="Renderer\PickerViewRenderer.cs" />
    <Compile Include="Service\NotificationRequestService.cs" />
    <Compile Include="Service\NotificationsInterface.cs" />
    <Compile Include="Service\AppSettingsInterface.cs" />
    <Compile Include="Service\KeyboardServiceRenderer.cs" />
    <Compile Include="Renderer\WorkoutEntryRenderer.cs" />
    <Compile Include="Renderer\ContextMenuButtonRenderer.cs" />
    <Compile Include="Service\DroidShareService.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <GoogleServicesJson Include="google-services.json" />
    <None Include="packages.config" />
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
    <None Include="Resources\Close_Gray.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\light_blue_arrow.png" />
    <AndroidResource Include="Resources\drawable-hdpi\light_blue_arrow_down.png" />
    <AndroidResource Include="Resources\drawable-hdpi\menu_blue.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\light_blue_arrow.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\light_blue_arrow_down.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\light_blue_arrow.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\light_blue_arrow_down.png" />
    <AndroidResource Include="Resources\drawable\buttonRed.xml" />
    <AndroidResource Include="Resources\drawable\buttonGray.xml" />
    <AndroidResource Include="Resources\drawable\buttonGreen.xml" />
    <AndroidResource Include="Resources\drawable-xhdpi\facebook.png" />
    <AndroidResource Include="Resources\drawable-hdpi\stopwatch.png" />
    <AndroidResource Include="Resources\drawable\swap.png" />
    <AndroidResource Include="Resources\drawable\Backgroundblack.png" />
    <AndroidResource Include="Resources\drawable\icon_search_gray.png" />
    <AndroidResource Include="Resources\drawable\DrMuscleLogo.png" />
    <AndroidResource Include="Resources\drawable\Flag_English.png" />
    <AndroidResource Include="Resources\drawable\Flag_French.png" />
    <AndroidResource Include="Resources\drawable\Flag_Swedish.png" />
    <AndroidResource Include="Resources\drawable\backspace_white.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\stars_5.png" />
    <AndroidResource Include="Resources\drawable\hide.png" />
    <AndroidResource Include="Resources\drawable\skip.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\hide.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\hide.png" />
    <AndroidResource Include="Resources\drawable-hdpi\hide.png" />
    <AndroidResource Include="Resources\drawable\plate.png" />
    <AndroidResource Include="Resources\drawable\plate45.png" />
    <AndroidResource Include="Resources\drawable\plate2half.png" />
    <AndroidResource Include="Resources\drawable\plate5.png" />
    <AndroidResource Include="Resources\drawable\plate10.png" />
    <AndroidResource Include="Resources\drawable\plate25.png" />
    <AndroidResource Include="Resources\drawable\plate35.png" />
    <AndroidResource Include="Resources\drawable\bar.png" />
    <AndroidResource Include="Resources\drawable\barKg.png" />
    <AndroidResource Include="Resources\drawable\plateKg25.png" />
    <AndroidResource Include="Resources\drawable\plateKg1.png" />
    <AndroidResource Include="Resources\drawable\plateKg2.png" />
    <AndroidResource Include="Resources\drawable\plateKg5.png" />
    <AndroidResource Include="Resources\drawable\plateKg10.png" />
    <AndroidResource Include="Resources\drawable\plateKg15.png" />
    <AndroidResource Include="Resources\drawable\plateKg20.png" />
    <AndroidResource Include="Resources\drawable\custom.png" />
    <AndroidResource Include="Resources\drawable-hdpi\edit_plate.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\edit_plate.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\edit_plate.png" />
    <AndroidResource Include="Resources\drawable\ic_send.png" />
    <AndroidResource Include="Resources\drawable-hdpi\ic_send.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\ic_send.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_send.png" />
    <AndroidResource Include="Resources\drawable-hdpi\chat_tab.png" />
    <AndroidResource Include="Resources\drawable-hdpi\home_tab.png" />
    <AndroidResource Include="Resources\drawable-hdpi\settings_tab.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\chat_tab.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\home_tab.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\settings_tab.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\chat_tab.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\home_tab.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\settings_tab.png" />
    <AndroidResource Include="Resources\drawable\adminprofile.png" />
    <AndroidResource Include="Resources\drawable\barBlank.png" />
    <AndroidResource Include="Resources\drawable-hdpi\exercise_tab.png" />
    <AndroidResource Include="Resources\drawable-hdpi\workout_tab.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\exercise_tab.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\workout_tab.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\exercise_tab.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\workout_tab.png" />
    <AndroidResource Include="Resources\drawable\Flag_Germany.png" />
    <AndroidResource Include="Resources\drawable\carlPhoto.png" />
    <AndroidResource Include="Resources\drawable-hdpi\Splash.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\Splash.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\Splash.png" />
    <AndroidResource Include="Resources\layout\Launch.axml" />
    <AndroidResource Include="Resources\drawable-hdpi\bottomText.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\bottomText.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\bottomText.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\facebook_share.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\facebook_f_white.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\down.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\up.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\PlusBlack.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\me_tab.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\me_tab.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\me_tab.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\me_tab.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\icon_notification.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\more.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\more.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\more.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\Leg.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\nav.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Leg.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\nav.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\Leg.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\nav.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_add.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_added.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_edit.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_add.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_added.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_edit.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_add.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_added.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_edit.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\done.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\finishSet.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_minus.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_plus.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\done.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\finishSet.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_minus.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_plus.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\done.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\finishSet.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_minus.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_plus.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\navBottom.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Abs.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Biceps.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Calves.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Chest.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Legs.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Neck.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Shoulders.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Triceps.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Undefined.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\finishSet_orange.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\finishSet_orange.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\finishSet_orange.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\DragIndicator.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\android_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\android_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_edit_white.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_edit_white.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_edit_white.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_edit_white.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\android_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\android_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\xml\network_securoty_config.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\edit_plate_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\edit_plate_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\edit_plate_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\Undone.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Undone.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\Undone.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Forearm.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\mail.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\baseline_mail_outline_white_48.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\baseline_mail_outline_white_48.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\google_icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\google_icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\google_icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\facebook_white.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\gradient_background.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Abs_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Back_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Biceps_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Calves_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Chest_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Forearm_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Legs_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Shoulders_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Triceps_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Undefined_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\done2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\done2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\done2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Neck_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\open.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\raw\AlarmTone.mp3">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\big_plate.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\gradient_background2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plate2halfhalf.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plate5half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plate10half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plate25half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plate35half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plate45half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg1half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg2half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg5half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg10half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg15half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg20half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg25half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\orange2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\logo.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\logo.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\logo.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\barHalf.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\barKgHalf.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\green.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\bottom.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\middle.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\top.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\bottom2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\middle2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\top2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\down_arrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\up_arrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\down_arrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\up_arrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cardio.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Cardio_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\Learn_Tab.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\white_down_arrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\white_down_arrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\white_down_arrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\more_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\Play.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\more_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Play.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\more_dark_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\Play_dark_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\more_dark_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Play_dark_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Favorites.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\calander.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\history.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Exercise.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Fire.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Records.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\WorkoutNow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Clock.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\GradientPopupBackground.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Chain.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\My_exercises.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Flexed_Biceps.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Bodyweight.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\NextWorkout.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\WeightLifting.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\WorkoutDone.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Selected.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\RestRecovery.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ExerciseBackground.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\SettingsBackground.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\WorkoutBackground.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\Close.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\artin.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\jonus.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\brandLogo.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg05.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\plateKg05half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\barBlankHalf.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\heartIcon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\pushup.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Alternating_Single_Leg_Bridge.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Bent_Over_Row.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Curl.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Deadlift.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Decline_Bench_Press.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Front_Squat.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Hip_Thrust.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Incline_Bench_Press.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Reverse_Curl.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Romanian_Deadlift.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Sumo_Deadlift.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Barbell_Underhand_Bent_over_Row.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Bench_press.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Bicycle_Crunch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Bodyweight_Lunge.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Bodyweight_Squat.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Bodyweight_Standing_Calf_Raise.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Bar_Lateral_Pulldown.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Chop.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Close_Grip_Front_Lat_Pulldown.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Curl.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Kneeling_Crunch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Overhead_Triceps_Extension.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Seated_Row.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Triceps_Pushdown.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cable_Upright_Row.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Chin_up_UnderHand.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Chin_Up.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\crunch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Decline_Push_Up.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Diamond_Push_up.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Bent_ove_Row.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Biceps_Curl.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Front_Raise.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Lateral_Raise.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Rear_Fly.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Side_Bend.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Split_Squat.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Squat.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Standing_Overhead_Press.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Standing_Triceps_Extension.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Straight_Arm_Twisting_Crunch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Glutes_Bridge.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Knee_Touch_Crunch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Kneeling_Push_up.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Leg_Curl.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Leg_Press.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Seated_Leg_Extension.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Shoulder_Grip_Pull_up.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Sumo_Squat_male.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Triceps_Dip.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Weighted_Crunch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\WideGrip_Pulldown.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Flexibility.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Flexibility_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Lower_back.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Lower_back_Transparent.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Bent_Over_Reverse_Fly_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Bent_Over_Row_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Cossack_Squat.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Crab_Reach.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Crunch_arms_overhead.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Deadlift_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Down_Upward_Dog.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Feet_Elevated_Diamond_Push_up.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Front_Squat_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Hammer_Curl_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\High_Knee_to_Butt_Kick.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Inch_Worm.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Lateral_Raise_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Overhead_Shoulder_Stretch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Pistol_Squat.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Push_up_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Scorpion_Stretch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Seated_Row_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Shoulder_Press_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Side_to_Side_Leg_Swing.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Single_Leg_Glute_Bridge.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Standing_Triceps_Extension_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Supine_Biceps_Curl_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Triceps_Kickback_with_bands.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Twisting_Crunch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Wall_Arm_Push_up.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\World_Greatest_Stretch.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\EmptyStar.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\TrueState.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Lists.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\FirstWorkout.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Medal.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Star.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Trophy.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\SwappedSuccess.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Lamp.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Close_Gray.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\weightChart.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\logo2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Shadow.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\xml\RemoteConfigDefaults.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Dumbbell_Sumo_Squat.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Seated_Dumbbell_Triceps_Extension.gif">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ScaleIcon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\starTrophy.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\values\dimens.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\appleFruite.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rcustom.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate2half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate2halfhalf.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate5.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate5half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate10.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate10half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate25.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate25half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate35.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate35half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate45.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplate45half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg1.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg1half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg2half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg5.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg05.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg5half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg05half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg10.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg10half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg15.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg15half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg20.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg20half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg25.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\rplateKg25half.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\calArrow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\email.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\chekedGreen.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\logo1.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\page2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\page3.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\logo3.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\leaves.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\leaves2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\alert_ic_blue.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\victoriaProfile.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\deleteset.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\deleteset_yellow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\DrSign.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Icon2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_happy.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_neutral.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_sad.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_happy.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_neutral.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_sad.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_happy.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_neutral.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_sad.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_happy.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_neutral.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_sad.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_heart.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_heart.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_heart.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_heart.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\xml\file_paths.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\xml\wearable_app_desc.xml">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\survey_icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\survey_icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\deleteset_yellow.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\survey_icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\DrSign.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\survey_icon.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\Icon2.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_share.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_share.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_share.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_share.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_handshake.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_meal_plan.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_handshake.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_meal_plan.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_handshake.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_meal_plan.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_handshake.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_meal_plan.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\ic_share_exercise.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-hdpi\ic_share_exercise.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xhdpi\ic_share_exercise.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable-xxhdpi\ic_share_exercise.png">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\AndroidManifest.xml">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\Tabbar.axml" />
    <AndroidResource Include="Resources\layout\Toolbar.axml" />
    <AndroidResource Include="Resources\values\styles.xml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DrMuscle\DrMuscle.csproj">
      <Project>{F881CA0A-9272-4EEC-BCA9-39A1D57C31ED}</Project>
      <Name>DrMuscle</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel.csproj">
      <Project>{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}</Project>
      <Name>DrMuscleWebApiSharedModel</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\green.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\orange.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\splash_screen.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values\colors.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\menu.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\Workout.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\downarrow.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\uparrow.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values\strings.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\Minus.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\Plus.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Dumbbell_Revers_grip_Biceps_Curl.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidAsset Include="Assets\alarma.mp3" />
    <AndroidAsset Include="Assets\reps1.mp3" />
    <AndroidAsset Include="Assets\reps2.mp3" />
    <AndroidAsset Include="Assets\reps3.mp3" />
    <AndroidAsset Include="Assets\reps4.mp3" />
    <AndroidAsset Include="Assets\reps5.mp3" />
    <AndroidAsset Include="Assets\reps6.mp3" />
    <AndroidAsset Include="Assets\reps7.mp3" />
    <AndroidAsset Include="Assets\reps8.mp3" />
    <AndroidAsset Include="Assets\reps9.mp3" />
    <AndroidAsset Include="Assets\reps10.mp3" />
    <AndroidAsset Include="Assets\reps11.mp3" />
    <AndroidAsset Include="Assets\reps12.mp3" />
    <AndroidAsset Include="Assets\reps13.mp3" />
    <AndroidAsset Include="Assets\reps14.mp3" />
    <AndroidAsset Include="Assets\reps15.mp3" />
    <AndroidAsset Include="Assets\reps16.mp3" />
    <AndroidAsset Include="Assets\reps17.mp3" />
    <AndroidAsset Include="Assets\reps18.mp3" />
    <AndroidAsset Include="Assets\reps19.mp3" />
    <AndroidAsset Include="Assets\reps20.mp3" />
    <AndroidAsset Include="Assets\reps21.mp3" />
    <AndroidAsset Include="Assets\reps22.mp3" />
    <AndroidAsset Include="Assets\reps23.mp3" />
    <AndroidAsset Include="Assets\reps24.mp3" />
    <AndroidAsset Include="Assets\reps25.mp3" />
    <AndroidAsset Include="Assets\reps26.mp3" />
    <AndroidAsset Include="Assets\reps27.mp3" />
    <AndroidAsset Include="Assets\reps28.mp3" />
    <AndroidAsset Include="Assets\reps29.mp3" />
    <AndroidAsset Include="Assets\reps30.mp3" />
    <AndroidAsset Include="Assets\reps31.mp3" />
    <AndroidAsset Include="Assets\reps32.mp3" />
    <AndroidAsset Include="Assets\reps33.mp3" />
    <AndroidAsset Include="Assets\reps34.mp3" />
    <AndroidAsset Include="Assets\reps35.mp3" />
    <AndroidAsset Include="Assets\reps36.mp3" />
    <AndroidAsset Include="Assets\reps37.mp3" />
    <AndroidAsset Include="Assets\reps38.mp3" />
    <AndroidAsset Include="Assets\reps39.mp3" />
    <AndroidAsset Include="Assets\reps40.mp3" />
    <AndroidAsset Include="Assets\reps41.mp3" />
    <AndroidAsset Include="Assets\reps42.mp3" />
    <AndroidAsset Include="Assets\reps43.mp3" />
    <AndroidAsset Include="Assets\reps44.mp3" />
    <AndroidAsset Include="Assets\reps45.mp3" />
    <AndroidAsset Include="Assets\reps46.mp3" />
    <AndroidAsset Include="Assets\reps47.mp3" />
    <AndroidAsset Include="Assets\reps48.mp3" />
    <AndroidAsset Include="Assets\reps49.mp3" />
    <AndroidAsset Include="Assets\reps50.mp3" />
    <AndroidAsset Include="Assets\reps51.mp3" />
    <AndroidAsset Include="Assets\reps52.mp3" />
    <AndroidAsset Include="Assets\reps53.mp3" />
    <AndroidAsset Include="Assets\reps54.mp3" />
    <AndroidAsset Include="Assets\reps55.mp3" />
    <AndroidAsset Include="Assets\reps56.mp3" />
    <AndroidAsset Include="Assets\reps57.mp3" />
    <AndroidAsset Include="Assets\reps58.mp3" />
    <AndroidAsset Include="Assets\reps59.mp3" />
    <AndroidAsset Include="Assets\reps60.mp3" />
    <AndroidAsset Include="Assets\timer123.mp3" />
    <AndroidAsset Include="Assets\emptyAudio.wav" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Lying_Barbell_Triceps_Extension_Skullcrusher.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Pronated_Dumbbell_Curl.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Sec_Pause_Squat.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Single_Leg_Dip_on_floor.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Zercher_Squat.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Airborne_Lunge.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Barbell_Full_Squat.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Barbell_Standing_Leg_Calf_Raise.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Barbell_sumo_squat.gif" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\SendMesageIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\openEye.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\mail_icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\closeEye.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\closeEye.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\mail_icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\openEye.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\SendMesageIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\closeEye.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\mail_icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\openEye.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\SendMesageIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\facebook_icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\facebook_icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\facebook_icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\DrIconWhite.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\DrIconWhite.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\DrIconWhite.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\ProgressIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\ProgressIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\ProgressIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\victoriaProfileRound.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-hdpi\SplashNewIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xhdpi\SplashNewIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable-xxhdpi\SplashNewIcon.png" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
  <Import Project="..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Annotations.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Annotations.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Annotations.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Annotations.targets')" />
  <Import Project="..\packages\Xamarin.Android.Arch.Core.Common.1.0.0\build\MonoAndroid80\Xamarin.Android.Arch.Core.Common.targets" Condition="Exists('..\packages\Xamarin.Android.Arch.Core.Common.1.0.0\build\MonoAndroid80\Xamarin.Android.Arch.Core.Common.targets')" />
  <Import Project="..\packages\Xamarin.Android.Arch.Lifecycle.Common.1.0.1\build\MonoAndroid80\Xamarin.Android.Arch.Lifecycle.Common.targets" Condition="Exists('..\packages\Xamarin.Android.Arch.Lifecycle.Common.1.0.1\build\MonoAndroid80\Xamarin.Android.Arch.Lifecycle.Common.targets')" />
  <Import Project="..\packages\Xamarin.Android.Arch.Lifecycle.Runtime.1.0.0\build\MonoAndroid80\Xamarin.Android.Arch.Lifecycle.Runtime.targets" Condition="Exists('..\packages\Xamarin.Android.Arch.Lifecycle.Runtime.1.0.0\build\MonoAndroid80\Xamarin.Android.Arch.Lifecycle.Runtime.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Compat.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Compat.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Compat.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Compat.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Core.UI.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Core.UI.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Core.UI.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Core.UI.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Core.Utils.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Core.Utils.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Core.Utils.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Core.Utils.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.CustomTabs.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.CustomTabs.targets" Condition="Exists('..\packages\Xamarin.Android.Support.CustomTabs.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.CustomTabs.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Fragment.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Fragment.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Fragment.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Fragment.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Media.Compat.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Media.Compat.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Media.Compat.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Media.Compat.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Transition.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Transition.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Transition.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Transition.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.v4.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v4.targets" Condition="Exists('..\packages\Xamarin.Android.Support.v4.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v4.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.v7.CardView.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.CardView.targets" Condition="Exists('..\packages\Xamarin.Android.Support.v7.CardView.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.CardView.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.v7.Palette.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.Palette.targets" Condition="Exists('..\packages\Xamarin.Android.Support.v7.Palette.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.Palette.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.v7.RecyclerView.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.RecyclerView.targets" Condition="Exists('..\packages\Xamarin.Android.Support.v7.RecyclerView.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.RecyclerView.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Vector.Drawable.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Vector.Drawable.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Vector.Drawable.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Vector.Drawable.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Animated.Vector.Drawable.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Animated.Vector.Drawable.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Animated.Vector.Drawable.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Animated.Vector.Drawable.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.v7.AppCompat.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.AppCompat.targets" Condition="Exists('..\packages\Xamarin.Android.Support.v7.AppCompat.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.AppCompat.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.Design.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Design.targets" Condition="Exists('..\packages\Xamarin.Android.Support.Design.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.Design.targets')" />
  <Import Project="..\packages\Xamarin.Android.Support.v7.MediaRouter.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.MediaRouter.targets" Condition="Exists('..\packages\Xamarin.Android.Support.v7.MediaRouter.26.1.0.1\build\MonoAndroid80\Xamarin.Android.Support.v7.MediaRouter.targets')" />
  <Import Project="..\packages\Xamarin.Build.Download.0.4.7\build\Xamarin.Build.Download.targets" Condition="Exists('..\packages\Xamarin.Build.Download.0.4.7\build\Xamarin.Build.Download.targets')" />
  <Import Project="..\packages\Xamarin.Forms.2.5.0.121934\build\netstandard1.0\Xamarin.Forms.targets" Condition="Exists('..\packages\Xamarin.Forms.2.5.0.121934\build\netstandard1.0\Xamarin.Forms.targets')" />
  <Import Project="..\packages\Xamarin.GooglePlayServices.Basement.60.1142.0\build\MonoAndroid80\Xamarin.GooglePlayServices.Basement.targets" Condition="Exists('..\packages\Xamarin.GooglePlayServices.Basement.60.1142.0\build\MonoAndroid80\Xamarin.GooglePlayServices.Basement.targets')" />
  <Import Project="..\packages\Xamarin.GooglePlayServices.Tasks.60.1142.0\build\MonoAndroid80\Xamarin.GooglePlayServices.Tasks.targets" Condition="Exists('..\packages\Xamarin.GooglePlayServices.Tasks.60.1142.0\build\MonoAndroid80\Xamarin.GooglePlayServices.Tasks.targets')" />
  <Import Project="..\packages\Xamarin.Firebase.Common.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Common.targets" Condition="Exists('..\packages\Xamarin.Firebase.Common.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Common.targets')" />
  <Import Project="..\packages\Xamarin.Firebase.Iid.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Iid.targets" Condition="Exists('..\packages\Xamarin.Firebase.Iid.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Iid.targets')" />
  <Import Project="..\packages\Xamarin.Firebase.Analytics.Impl.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Analytics.Impl.targets" Condition="Exists('..\packages\Xamarin.Firebase.Analytics.Impl.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Analytics.Impl.targets')" />
  <Import Project="..\packages\Xamarin.Firebase.Analytics.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Analytics.targets" Condition="Exists('..\packages\Xamarin.Firebase.Analytics.60.1142.0\build\MonoAndroid80\Xamarin.Firebase.Analytics.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Arch.Core.Common.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Core.Common.targets" Condition="Exists('..\..\packages\Xamarin.Android.Arch.Core.Common.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Core.Common.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Arch.Lifecycle.Common.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.Common.targets" Condition="Exists('..\..\packages\Xamarin.Android.Arch.Lifecycle.Common.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.Common.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Arch.Lifecycle.Runtime.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.Runtime.targets" Condition="Exists('..\..\packages\Xamarin.Android.Arch.Lifecycle.Runtime.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.Runtime.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Collections.********\build\monoandroid90\Xamarin.Android.Support.Collections.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Collections.********\build\monoandroid90\Xamarin.Android.Support.Collections.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.VersionedParcelable.********\build\monoandroid90\Xamarin.Android.Support.VersionedParcelable.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.VersionedParcelable.********\build\monoandroid90\Xamarin.Android.Support.VersionedParcelable.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Compat.********\build\monoandroid90\Xamarin.Android.Support.Compat.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Compat.********\build\monoandroid90\Xamarin.Android.Support.Compat.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Arch.Core.Runtime.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Core.Runtime.targets" Condition="Exists('..\..\packages\Xamarin.Android.Arch.Core.Runtime.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Core.Runtime.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Arch.Lifecycle.LiveData.Core.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.LiveData.Core.targets" Condition="Exists('..\..\packages\Xamarin.Android.Arch.Lifecycle.LiveData.Core.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.LiveData.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Arch.Lifecycle.LiveData.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.LiveData.targets" Condition="Exists('..\..\packages\Xamarin.Android.Arch.Lifecycle.LiveData.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.LiveData.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Arch.Lifecycle.ViewModel.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.ViewModel.targets" Condition="Exists('..\..\packages\Xamarin.Android.Arch.Lifecycle.ViewModel.1.1.1.3\build\monoandroid90\Xamarin.Android.Arch.Lifecycle.ViewModel.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.CursorAdapter.********\build\monoandroid90\Xamarin.Android.Support.CursorAdapter.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.CursorAdapter.********\build\monoandroid90\Xamarin.Android.Support.CursorAdapter.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.DocumentFile.********\build\monoandroid90\Xamarin.Android.Support.DocumentFile.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.DocumentFile.********\build\monoandroid90\Xamarin.Android.Support.DocumentFile.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Interpolator.********\build\monoandroid90\Xamarin.Android.Support.Interpolator.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Interpolator.********\build\monoandroid90\Xamarin.Android.Support.Interpolator.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.LocalBroadcastManager.********\build\monoandroid90\Xamarin.Android.Support.LocalBroadcastManager.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.LocalBroadcastManager.********\build\monoandroid90\Xamarin.Android.Support.LocalBroadcastManager.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Print.********\build\monoandroid90\Xamarin.Android.Support.Print.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Print.********\build\monoandroid90\Xamarin.Android.Support.Print.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.AsyncLayoutInflater.********\build\monoandroid90\Xamarin.Android.Support.AsyncLayoutInflater.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.AsyncLayoutInflater.********\build\monoandroid90\Xamarin.Android.Support.AsyncLayoutInflater.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.CustomView.********\build\monoandroid90\Xamarin.Android.Support.CustomView.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.CustomView.********\build\monoandroid90\Xamarin.Android.Support.CustomView.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.CoordinaterLayout.********\build\monoandroid90\Xamarin.Android.Support.CoordinaterLayout.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.CoordinaterLayout.********\build\monoandroid90\Xamarin.Android.Support.CoordinaterLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.v7.CardView.********\build\monoandroid90\Xamarin.Android.Support.v7.CardView.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.v7.CardView.********\build\monoandroid90\Xamarin.Android.Support.v7.CardView.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.DrawerLayout.********\build\monoandroid90\Xamarin.Android.Support.DrawerLayout.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.DrawerLayout.********\build\monoandroid90\Xamarin.Android.Support.DrawerLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Loader.********\build\monoandroid90\Xamarin.Android.Support.Loader.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Loader.********\build\monoandroid90\Xamarin.Android.Support.Loader.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.SlidingPaneLayout.********\build\monoandroid90\Xamarin.Android.Support.SlidingPaneLayout.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.SlidingPaneLayout.********\build\monoandroid90\Xamarin.Android.Support.SlidingPaneLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.SwipeRefreshLayout.********\build\monoandroid90\Xamarin.Android.Support.SwipeRefreshLayout.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.SwipeRefreshLayout.********\build\monoandroid90\Xamarin.Android.Support.SwipeRefreshLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.v7.Palette.********\build\monoandroid90\Xamarin.Android.Support.v7.Palette.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.v7.Palette.********\build\monoandroid90\Xamarin.Android.Support.v7.Palette.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Vector.Drawable.********\build\monoandroid90\Xamarin.Android.Support.Vector.Drawable.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Vector.Drawable.********\build\monoandroid90\Xamarin.Android.Support.Vector.Drawable.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.ViewPager.********\build\monoandroid90\Xamarin.Android.Support.ViewPager.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.ViewPager.********\build\monoandroid90\Xamarin.Android.Support.ViewPager.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Animated.Vector.Drawable.********\build\monoandroid90\Xamarin.Android.Support.Animated.Vector.Drawable.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Animated.Vector.Drawable.********\build\monoandroid90\Xamarin.Android.Support.Animated.Vector.Drawable.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.CustomTabs.********\build\monoandroid90\Xamarin.Android.Support.CustomTabs.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.CustomTabs.********\build\monoandroid90\Xamarin.Android.Support.CustomTabs.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Transition.********\build\monoandroid90\Xamarin.Android.Support.Transition.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Transition.********\build\monoandroid90\Xamarin.Android.Support.Transition.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.v4.********\build\monoandroid90\Xamarin.Android.Support.v4.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.v4.********\build\monoandroid90\Xamarin.Android.Support.v4.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.v7.AppCompat.********\build\monoandroid90\Xamarin.Android.Support.v7.AppCompat.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.v7.AppCompat.********\build\monoandroid90\Xamarin.Android.Support.v7.AppCompat.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.v7.RecyclerView.********\build\monoandroid90\Xamarin.Android.Support.v7.RecyclerView.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.v7.RecyclerView.********\build\monoandroid90\Xamarin.Android.Support.v7.RecyclerView.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Design.********\build\monoandroid90\Xamarin.Android.Support.Design.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Design.********\build\monoandroid90\Xamarin.Android.Support.Design.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.v7.MediaRouter.********\build\monoandroid90\Xamarin.Android.Support.v7.MediaRouter.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.v7.MediaRouter.********\build\monoandroid90\Xamarin.Android.Support.v7.MediaRouter.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Annotations.********\build\monoandroid90\Xamarin.Android.Support.Annotations.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Annotations.********\build\monoandroid90\Xamarin.Android.Support.Annotations.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Core.Utils.********\build\monoandroid90\Xamarin.Android.Support.Core.Utils.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Core.Utils.********\build\monoandroid90\Xamarin.Android.Support.Core.Utils.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Media.Compat.********\build\monoandroid90\Xamarin.Android.Support.Media.Compat.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Media.Compat.********\build\monoandroid90\Xamarin.Android.Support.Media.Compat.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Fragment.********\build\monoandroid90\Xamarin.Android.Support.Fragment.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Fragment.********\build\monoandroid90\Xamarin.Android.Support.Fragment.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Core.UI.********\build\monoandroid90\Xamarin.Android.Support.Core.UI.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Core.UI.********\build\monoandroid90\Xamarin.Android.Support.Core.UI.targets')" />
  <Import Project="..\..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties TriggeredFromHotReload="False" />
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.*******\build\monoandroid90\Xamarin.AndroidX.Lifecycle.LiveData.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.*******\build\monoandroid90\Xamarin.AndroidX.Lifecycle.LiveData.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Browser.*******\build\monoandroid90\Xamarin.AndroidX.Browser.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Browser.*******\build\monoandroid90\Xamarin.AndroidX.Browser.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Navigation.UI.2.3.5.5\build\monoandroid12.0\Xamarin.AndroidX.Navigation.UI.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Navigation.UI.2.3.5.5\build\monoandroid12.0\Xamarin.AndroidX.Navigation.UI.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Migration.1.0.10\build\monoandroid120\Xamarin.AndroidX.Migration.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Migration.1.0.10\build\monoandroid120\Xamarin.AndroidX.Migration.targets')" />
  <Import Project="..\..\packages\Xamarin.Google.Android.Material.1.4.0.6\build\monoandroid12.0\Xamarin.Google.Android.Material.targets" Condition="Exists('..\..\packages\Xamarin.Google.Android.Material.1.4.0.6\build\monoandroid12.0\Xamarin.Google.Android.Material.targets')" />
  <Import Project="..\..\packages\Xamarin.Build.Download.0.11.4\build\Xamarin.Build.Download.targets" Condition="Exists('..\..\packages\Xamarin.Build.Download.0.11.4\build\Xamarin.Build.Download.targets')" />
  <Import Project="..\..\packages\SkiaSharp.NativeAssets.Android.2.88.7\build\monoandroid1.0\SkiaSharp.NativeAssets.Android.targets" Condition="Exists('..\..\packages\SkiaSharp.NativeAssets.Android.2.88.7\build\monoandroid1.0\SkiaSharp.NativeAssets.Android.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\SkiaSharp.NativeAssets.Android.2.88.7\build\monoandroid1.0\SkiaSharp.NativeAssets.Android.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\SkiaSharp.NativeAssets.Android.2.88.7\build\monoandroid1.0\SkiaSharp.NativeAssets.Android.targets'))" />
    <Error Condition="!Exists('..\..\packages\Sentry.4.2.1\build\Sentry.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Sentry.4.2.1\build\Sentry.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.ConstraintLayout.Core.1.0.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.ConstraintLayout.Core.1.0.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.Core.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.MultiDex.2.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.MultiDex.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.MultiDex.2.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.MultiDex.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Google.Android.Play.Core.1.10.3.9\build\monoandroid12.0\Xamarin.Google.Android.Play.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Google.Android.Play.Core.1.10.3.9\build\monoandroid12.0\Xamarin.Google.Android.Play.Core.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Google.Guava.ListenableFuture.1.0.0.18\build\monoandroid12.0\Xamarin.Google.Guava.ListenableFuture.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Google.Guava.ListenableFuture.1.0.0.18\build\monoandroid12.0\Xamarin.Google.Guava.ListenableFuture.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Annotations.116.2.0.5\build\monoandroid12.0\Xamarin.Firebase.Annotations.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Annotations.116.2.0.5\build\monoandroid12.0\Xamarin.Firebase.Annotations.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Jetbrains.Annotations.24.1.0.2\build\monoandroid12.0\Xamarin.Jetbrains.Annotations.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Jetbrains.Annotations.24.1.0.2\build\monoandroid12.0\Xamarin.Jetbrains.Annotations.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Kotlin.StdLib.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Kotlin.StdLib.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Annotation.Experimental.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Experimental.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Annotation.Experimental.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Experimental.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Annotation.Jvm.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Jvm.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Annotation.Jvm.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Jvm.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Annotation.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Annotation.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Arch.Core.Common.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Arch.Core.Common.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Common.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Arch.Core.Runtime.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Runtime.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Arch.Core.Runtime.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Runtime.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.CardView.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.CardView.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.CardView.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.CardView.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Collection.Jvm.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Jvm.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Collection.Jvm.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Jvm.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Collection.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Collection.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Collection.Ktx.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Collection.Ktx.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Concurrent.Futures.********\build\monoandroid12.0\Xamarin.AndroidX.Concurrent.Futures.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Concurrent.Futures.********\build\monoandroid12.0\Xamarin.AndroidX.Concurrent.Futures.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.CursorAdapter.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.CursorAdapter.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.CursorAdapter.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.CursorAdapter.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.DocumentFile.1.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.DocumentFile.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.DocumentFile.1.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.DocumentFile.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Interpolator.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Interpolator.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Interpolator.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Interpolator.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.LocalBroadcastManager.1.1.0.11\build\monoandroid12.0\Xamarin.AndroidX.LocalBroadcastManager.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.LocalBroadcastManager.1.1.0.11\build\monoandroid12.0\Xamarin.AndroidX.LocalBroadcastManager.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Print.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Print.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Print.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Print.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.ResourceInspection.Annotation.1.0.1.11\build\monoandroid12.0\Xamarin.AndroidX.ResourceInspection.Annotation.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.ResourceInspection.Annotation.1.0.1.11\build\monoandroid12.0\Xamarin.AndroidX.ResourceInspection.Annotation.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Tracing.Tracing.*******\build\monoandroid12.0\Xamarin.AndroidX.Tracing.Tracing.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Tracing.Tracing.*******\build\monoandroid12.0\Xamarin.AndroidX.Tracing.Tracing.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Startup.StartupRuntime.********\build\monoandroid12.0\Xamarin.AndroidX.Startup.StartupRuntime.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Startup.StartupRuntime.********\build\monoandroid12.0\Xamarin.AndroidX.Startup.StartupRuntime.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.1.3.1.6\build\monoandroid12.0\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.1.3.1.6\build\monoandroid12.0\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.VersionedParcelable.*******\build\monoandroid12.0\Xamarin.AndroidX.VersionedParcelable.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.VersionedParcelable.*******\build\monoandroid12.0\Xamarin.AndroidX.VersionedParcelable.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Window.Extensions.Core.Core.1.0.0.5\build\monoandroid12.0\Xamarin.AndroidX.Window.Extensions.Core.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Window.Extensions.Core.Core.1.0.0.5\build\monoandroid12.0\Xamarin.AndroidX.Window.Extensions.Core.Core.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Components.117.1.5.1\build\monoandroid12.0\Xamarin.Firebase.Components.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Components.117.1.5.1\build\monoandroid12.0\Xamarin.Firebase.Components.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Encoders.117.0.0.13\build\monoandroid12.0\Xamarin.Firebase.Encoders.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Encoders.117.0.0.13\build\monoandroid12.0\Xamarin.Firebase.Encoders.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Encoders.Proto.116.0.0.8\build\monoandroid12.0\Xamarin.Firebase.Encoders.Proto.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Encoders.Proto.116.0.0.8\build\monoandroid12.0\Xamarin.Firebase.Encoders.Proto.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Kotlin.StdLib.Common.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Kotlin.StdLib.Common.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Common.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Kotlin.StdLib.Jdk7.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk7.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Kotlin.StdLib.Jdk7.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk7.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Kotlin.StdLib.Jdk8.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk8.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Kotlin.StdLib.Jdk8.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk8.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Encoders.JSON.118.0.1.5\build\monoandroid12.0\Xamarin.Firebase.Encoders.JSON.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Encoders.JSON.118.0.1.5\build\monoandroid12.0\Xamarin.Firebase.Encoders.JSON.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Android.Google.BillingClient.6.0.1.4\build\monoandroid12.0\Xamarin.Android.Google.BillingClient.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Android.Google.BillingClient.6.0.1.4\build\monoandroid12.0\Xamarin.Android.Google.BillingClient.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Core.Jvm.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.Jvm.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.KotlinX.Coroutines.Core.Jvm.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.Jvm.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Android.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Android.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.KotlinX.Coroutines.Android.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Android.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Core.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.KotlinX.Coroutines.Core.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Common.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.Common.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Common.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Core.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Core.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.AsyncLayoutInflater.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.AsyncLayoutInflater.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.AsyncLayoutInflater.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.AsyncLayoutInflater.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Core.Core.Ktx.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.Core.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Core.Core.Ktx.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.Core.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.CustomView.1.1.0.22\build\monoandroid12.0\Xamarin.AndroidX.CustomView.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.CustomView.1.1.0.22\build\monoandroid12.0\Xamarin.AndroidX.CustomView.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.CoordinatorLayout.********\build\monoandroid12.0\Xamarin.AndroidX.CoordinatorLayout.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.CoordinatorLayout.********\build\monoandroid12.0\Xamarin.AndroidX.CoordinatorLayout.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.CustomView.PoolingContainer.1.0.0.9\build\monoandroid12.0\Xamarin.AndroidX.CustomView.PoolingContainer.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.CustomView.PoolingContainer.1.0.0.9\build\monoandroid12.0\Xamarin.AndroidX.CustomView.PoolingContainer.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.DrawerLayout.1.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.DrawerLayout.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.DrawerLayout.1.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.DrawerLayout.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Process.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Process.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.Process.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Process.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Emoji2.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Emoji2.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Emoji2.ViewsHelper.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.ViewsHelper.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Emoji2.ViewsHelper.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.ViewsHelper.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Loader.********\build\monoandroid12.0\Xamarin.AndroidX.Loader.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Loader.********\build\monoandroid12.0\Xamarin.AndroidX.Loader.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.Utils.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.Utils.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.Utils.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.Utils.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.DynamicAnimation.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.DynamicAnimation.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.DynamicAnimation.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.DynamicAnimation.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Media.1.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Media.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Media.1.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Media.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Palette.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Palette.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Palette.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Palette.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.RecyclerView.1.3.2.2\build\monoandroid12.0\Xamarin.AndroidX.RecyclerView.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.RecyclerView.1.3.2.2\build\monoandroid12.0\Xamarin.AndroidX.RecyclerView.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.SavedState.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.SavedState.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Activity.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Activity.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.SavedState.SavedState.Ktx.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.SavedState.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.SavedState.SavedState.Ktx.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.SavedState.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Activity.Ktx.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Activity.Ktx.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Navigation.Common.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Navigation.Common.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Common.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Navigation.Runtime.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Runtime.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Navigation.Runtime.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Runtime.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.SwipeRefreshLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SwipeRefreshLayout.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.SwipeRefreshLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SwipeRefreshLayout.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.VectorDrawable.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.VectorDrawable.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.VectorDrawable.Animated.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.Animated.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.VectorDrawable.Animated.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.Animated.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.AppCompat.AppCompatResources.*******\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.AppCompatResources.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.AppCompat.AppCompatResources.*******\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.AppCompatResources.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.ViewPager.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.ViewPager.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.ViewPager.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.ViewPager.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Fragment.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Fragment.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.AppCompat.1.6.1.7\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.AppCompat.1.6.1.7\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.ConstraintLayout.2.1.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.ConstraintLayout.2.1.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Fragment.Ktx.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Fragment.Ktx.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.MediaRouter.1.6.0.3\build\monoandroid12.0\Xamarin.AndroidX.MediaRouter.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.MediaRouter.1.6.0.3\build\monoandroid12.0\Xamarin.AndroidX.MediaRouter.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Transition.********\build\monoandroid12.0\Xamarin.AndroidX.Transition.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Transition.********\build\monoandroid12.0\Xamarin.AndroidX.Transition.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.ViewPager2.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.ViewPager2.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.ViewPager2.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.ViewPager2.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Window.1.2.0.2\build\monoandroid12.0\Xamarin.AndroidX.Window.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Window.1.2.0.2\build\monoandroid12.0\Xamarin.AndroidX.Window.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.SlidingPaneLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SlidingPaneLayout.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.SlidingPaneLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SlidingPaneLayout.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.UI.1.0.0.24\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.UI.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.UI.1.0.0.24\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.UI.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Legacy.Support.V4.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.V4.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Legacy.Support.V4.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.V4.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.AndroidX.Preference.1.2.1.4\build\monoandroid12.0\Xamarin.AndroidX.Preference.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.AndroidX.Preference.1.2.1.4\build\monoandroid12.0\Xamarin.AndroidX.Preference.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.props'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Basement.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Basement.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Basement.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Basement.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Measurement.Connector.120.0.1.1\build\monoandroid12.0\Xamarin.Firebase.Measurement.Connector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Measurement.Connector.120.0.1.1\build\monoandroid12.0\Xamarin.Firebase.Measurement.Connector.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Ads.Identifier.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Ads.Identifier.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Ads.Identifier.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Ads.Identifier.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Base.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Base.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Measurement.Base.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Base.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.Api.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.Api.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Stats.117.0.3.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Stats.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Stats.117.0.3.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Stats.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Impl.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Impl.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Measurement.Impl.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Impl.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Measurement.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Tasks.118.0.2.6\build\monoandroid12.0\Xamarin.GooglePlayServices.Tasks.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Tasks.118.0.2.6\build\monoandroid12.0\Xamarin.GooglePlayServices.Tasks.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Iid.Interop.117.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.Interop.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Iid.Interop.117.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.Interop.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Installations.InterOp.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.InterOp.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Installations.InterOp.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.InterOp.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Base.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Base.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Base.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Base.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Auth.Api.Phone.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Api.Phone.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Auth.Api.Phone.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Api.Phone.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Auth.Base.118.0.10.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Base.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Auth.Base.118.0.10.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Base.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.CloudMessaging.117.0.2.8\build\monoandroid12.0\Xamarin.GooglePlayServices.CloudMessaging.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.CloudMessaging.117.0.2.8\build\monoandroid12.0\Xamarin.GooglePlayServices.CloudMessaging.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Fido.120.1.0.1\build\monoandroid12.0\Xamarin.GooglePlayServices.Fido.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Fido.120.1.0.1\build\monoandroid12.0\Xamarin.GooglePlayServices.Fido.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Auth.120.7.0.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Auth.120.7.0.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Play.Services.1.7.3.3\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Play.Services.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.KotlinX.Coroutines.Play.Services.1.7.3.3\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Play.Services.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Common.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Common.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Abt.121.1.1.5\build\monoandroid12.0\Xamarin.Firebase.Abt.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Abt.121.1.1.5\build\monoandroid12.0\Xamarin.Firebase.Abt.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Common.Ktx.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.Ktx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Common.Ktx.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.Ktx.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Datatransport.118.2.0.3\build\monoandroid12.0\Xamarin.Firebase.Datatransport.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Datatransport.118.2.0.3\build\monoandroid12.0\Xamarin.Firebase.Datatransport.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Installations.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Installations.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Config.121.5.0.1\build\monoandroid12.0\Xamarin.Firebase.Config.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Config.121.5.0.1\build\monoandroid12.0\Xamarin.Firebase.Config.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Iid.121.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Iid.121.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Messaging.123.3.1.1\build\monoandroid12.0\Xamarin.Firebase.Messaging.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Messaging.123.3.1.1\build\monoandroid12.0\Xamarin.Firebase.Messaging.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Api.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.GooglePlayServices.Measurement.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Api.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.Analytics.121.3.0.4\build\monoandroid12.0\Xamarin.Firebase.Analytics.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.Analytics.121.3.0.4\build\monoandroid12.0\Xamarin.Firebase.Analytics.targets'))" />
  </Target>
  <Import Project="..\..\packages\Sentry.4.2.1\build\Sentry.targets" Condition="Exists('..\..\packages\Sentry.4.2.1\build\Sentry.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.ConstraintLayout.Core.1.0.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.Core.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.ConstraintLayout.Core.1.0.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.MultiDex.2.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.MultiDex.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.MultiDex.2.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.MultiDex.targets')" />
  <Import Project="..\..\packages\Xamarin.Google.Android.Play.Core.1.10.3.9\build\monoandroid12.0\Xamarin.Google.Android.Play.Core.targets" Condition="Exists('..\..\packages\Xamarin.Google.Android.Play.Core.1.10.3.9\build\monoandroid12.0\Xamarin.Google.Android.Play.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.Google.Guava.ListenableFuture.1.0.0.18\build\monoandroid12.0\Xamarin.Google.Guava.ListenableFuture.targets" Condition="Exists('..\..\packages\Xamarin.Google.Guava.ListenableFuture.1.0.0.18\build\monoandroid12.0\Xamarin.Google.Guava.ListenableFuture.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Annotations.116.2.0.5\build\monoandroid12.0\Xamarin.Firebase.Annotations.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Annotations.116.2.0.5\build\monoandroid12.0\Xamarin.Firebase.Annotations.targets')" />
  <Import Project="..\..\packages\Xamarin.Jetbrains.Annotations.24.1.0.2\build\monoandroid12.0\Xamarin.Jetbrains.Annotations.targets" Condition="Exists('..\..\packages\Xamarin.Jetbrains.Annotations.24.1.0.2\build\monoandroid12.0\Xamarin.Jetbrains.Annotations.targets')" />
  <Import Project="..\..\packages\Xamarin.Kotlin.StdLib.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.targets" Condition="Exists('..\..\packages\Xamarin.Kotlin.StdLib.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Annotation.Experimental.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Experimental.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Annotation.Experimental.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Experimental.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Annotation.Jvm.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Jvm.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Annotation.Jvm.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.Jvm.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Annotation.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Annotation.1.7.1.1\build\monoandroid12.0\Xamarin.AndroidX.Annotation.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Arch.Core.Common.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Common.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Arch.Core.Common.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Common.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Arch.Core.Runtime.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Runtime.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Arch.Core.Runtime.2.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.Arch.Core.Runtime.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.CardView.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.CardView.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.CardView.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.CardView.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Collection.Jvm.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Jvm.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Collection.Jvm.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Jvm.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Collection.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Collection.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Collection.Ktx.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Collection.Ktx.1.4.0.1\build\monoandroid12.0\Xamarin.AndroidX.Collection.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Concurrent.Futures.********\build\monoandroid12.0\Xamarin.AndroidX.Concurrent.Futures.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Concurrent.Futures.********\build\monoandroid12.0\Xamarin.AndroidX.Concurrent.Futures.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.CursorAdapter.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.CursorAdapter.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.CursorAdapter.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.CursorAdapter.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.DocumentFile.1.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.DocumentFile.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.DocumentFile.1.0.1.23\build\monoandroid12.0\Xamarin.AndroidX.DocumentFile.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Interpolator.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Interpolator.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Interpolator.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Interpolator.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.LocalBroadcastManager.1.1.0.11\build\monoandroid12.0\Xamarin.AndroidX.LocalBroadcastManager.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.LocalBroadcastManager.1.1.0.11\build\monoandroid12.0\Xamarin.AndroidX.LocalBroadcastManager.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Print.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Print.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Print.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Print.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.ResourceInspection.Annotation.1.0.1.11\build\monoandroid12.0\Xamarin.AndroidX.ResourceInspection.Annotation.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.ResourceInspection.Annotation.1.0.1.11\build\monoandroid12.0\Xamarin.AndroidX.ResourceInspection.Annotation.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Tracing.Tracing.*******\build\monoandroid12.0\Xamarin.AndroidX.Tracing.Tracing.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Tracing.Tracing.*******\build\monoandroid12.0\Xamarin.AndroidX.Tracing.Tracing.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Startup.StartupRuntime.********\build\monoandroid12.0\Xamarin.AndroidX.Startup.StartupRuntime.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Startup.StartupRuntime.********\build\monoandroid12.0\Xamarin.AndroidX.Startup.StartupRuntime.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.1.3.1.6\build\monoandroid12.0\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.1.3.1.6\build\monoandroid12.0\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.VersionedParcelable.*******\build\monoandroid12.0\Xamarin.AndroidX.VersionedParcelable.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.VersionedParcelable.*******\build\monoandroid12.0\Xamarin.AndroidX.VersionedParcelable.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Window.Extensions.Core.Core.1.0.0.5\build\monoandroid12.0\Xamarin.AndroidX.Window.Extensions.Core.Core.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Window.Extensions.Core.Core.1.0.0.5\build\monoandroid12.0\Xamarin.AndroidX.Window.Extensions.Core.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Components.117.1.5.1\build\monoandroid12.0\Xamarin.Firebase.Components.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Components.117.1.5.1\build\monoandroid12.0\Xamarin.Firebase.Components.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Encoders.117.0.0.13\build\monoandroid12.0\Xamarin.Firebase.Encoders.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Encoders.117.0.0.13\build\monoandroid12.0\Xamarin.Firebase.Encoders.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Encoders.Proto.116.0.0.8\build\monoandroid12.0\Xamarin.Firebase.Encoders.Proto.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Encoders.Proto.116.0.0.8\build\monoandroid12.0\Xamarin.Firebase.Encoders.Proto.targets')" />
  <Import Project="..\..\packages\Xamarin.Kotlin.StdLib.Common.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Common.targets" Condition="Exists('..\..\packages\Xamarin.Kotlin.StdLib.Common.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Common.targets')" />
  <Import Project="..\..\packages\Xamarin.Kotlin.StdLib.Jdk7.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk7.targets" Condition="Exists('..\..\packages\Xamarin.Kotlin.StdLib.Jdk7.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk7.targets')" />
  <Import Project="..\..\packages\Xamarin.Kotlin.StdLib.Jdk8.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk8.targets" Condition="Exists('..\..\packages\Xamarin.Kotlin.StdLib.Jdk8.1.9.23\build\monoandroid12.0\Xamarin.Kotlin.StdLib.Jdk8.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Encoders.JSON.118.0.1.5\build\monoandroid12.0\Xamarin.Firebase.Encoders.JSON.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Encoders.JSON.118.0.1.5\build\monoandroid12.0\Xamarin.Firebase.Encoders.JSON.targets')" />
  <Import Project="..\..\packages\Xamarin.Android.Google.BillingClient.6.0.1.4\build\monoandroid12.0\Xamarin.Android.Google.BillingClient.targets" Condition="Exists('..\..\packages\Xamarin.Android.Google.BillingClient.6.0.1.4\build\monoandroid12.0\Xamarin.Android.Google.BillingClient.targets')" />
  <Import Project="..\..\packages\Xamarin.KotlinX.Coroutines.Core.Jvm.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.Jvm.targets" Condition="Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Core.Jvm.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.Jvm.targets')" />
  <Import Project="..\..\packages\Xamarin.KotlinX.Coroutines.Android.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Android.targets" Condition="Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Android.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Android.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.KotlinX.Coroutines.Core.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.targets" Condition="Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Core.1.8.0.1\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.Common.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Common.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Common.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Common.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Core.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Core.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.AsyncLayoutInflater.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.AsyncLayoutInflater.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.AsyncLayoutInflater.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.AsyncLayoutInflater.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Core.Core.Ktx.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.Core.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Core.Core.Ktx.1.12.0.4\build\monoandroid12.0\Xamarin.AndroidX.Core.Core.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.CustomView.1.1.0.22\build\monoandroid12.0\Xamarin.AndroidX.CustomView.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.CustomView.1.1.0.22\build\monoandroid12.0\Xamarin.AndroidX.CustomView.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.CoordinatorLayout.********\build\monoandroid12.0\Xamarin.AndroidX.CoordinatorLayout.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.CoordinatorLayout.********\build\monoandroid12.0\Xamarin.AndroidX.CoordinatorLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.CustomView.PoolingContainer.1.0.0.9\build\monoandroid12.0\Xamarin.AndroidX.CustomView.PoolingContainer.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.CustomView.PoolingContainer.1.0.0.9\build\monoandroid12.0\Xamarin.AndroidX.CustomView.PoolingContainer.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.DrawerLayout.1.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.DrawerLayout.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.DrawerLayout.1.2.0.7\build\monoandroid12.0\Xamarin.AndroidX.DrawerLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.Process.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Process.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Process.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Process.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Emoji2.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Emoji2.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Emoji2.ViewsHelper.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.ViewsHelper.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Emoji2.ViewsHelper.1.4.0.4\build\monoandroid12.0\Xamarin.AndroidX.Emoji2.ViewsHelper.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Loader.********\build\monoandroid12.0\Xamarin.AndroidX.Loader.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Loader.********\build\monoandroid12.0\Xamarin.AndroidX.Loader.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.Utils.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.Utils.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.Utils.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.Utils.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.DynamicAnimation.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.DynamicAnimation.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.DynamicAnimation.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.DynamicAnimation.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Media.1.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Media.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Media.1.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Media.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Palette.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Palette.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Palette.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Palette.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.RecyclerView.1.3.2.2\build\monoandroid12.0\Xamarin.AndroidX.RecyclerView.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.RecyclerView.1.3.2.2\build\monoandroid12.0\Xamarin.AndroidX.RecyclerView.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.SavedState.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.SavedState.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.2.7.0.1\build\monoandroid12.0\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Activity.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Activity.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.SavedState.SavedState.Ktx.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.SavedState.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.SavedState.SavedState.Ktx.1.2.1.7\build\monoandroid12.0\Xamarin.AndroidX.SavedState.SavedState.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Activity.Ktx.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Activity.Ktx.1.8.2.1\build\monoandroid12.0\Xamarin.AndroidX.Activity.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Navigation.Common.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Common.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Navigation.Common.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Common.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Navigation.Runtime.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Runtime.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Navigation.Runtime.*******\build\monoandroid12.0\Xamarin.AndroidX.Navigation.Runtime.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.SwipeRefreshLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SwipeRefreshLayout.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.SwipeRefreshLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SwipeRefreshLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.VectorDrawable.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.VectorDrawable.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.VectorDrawable.Animated.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.Animated.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.VectorDrawable.Animated.********\build\monoandroid12.0\Xamarin.AndroidX.VectorDrawable.Animated.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.AppCompat.AppCompatResources.*******\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.AppCompatResources.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.AppCompat.AppCompatResources.*******\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.AppCompatResources.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.ViewPager.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.ViewPager.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.ViewPager.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.ViewPager.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Fragment.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Fragment.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.AppCompat.1.6.1.7\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.AppCompat.1.6.1.7\build\monoandroid12.0\Xamarin.AndroidX.AppCompat.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.ConstraintLayout.2.1.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.ConstraintLayout.2.1.4.10\build\monoandroid12.0\Xamarin.AndroidX.ConstraintLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Fragment.Ktx.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Fragment.Ktx.1.6.2.2\build\monoandroid12.0\Xamarin.AndroidX.Fragment.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.MediaRouter.1.6.0.3\build\monoandroid12.0\Xamarin.AndroidX.MediaRouter.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.MediaRouter.1.6.0.3\build\monoandroid12.0\Xamarin.AndroidX.MediaRouter.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Transition.********\build\monoandroid12.0\Xamarin.AndroidX.Transition.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Transition.********\build\monoandroid12.0\Xamarin.AndroidX.Transition.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.ViewPager2.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.ViewPager2.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.ViewPager2.1.0.0.25\build\monoandroid12.0\Xamarin.AndroidX.ViewPager2.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Window.1.2.0.2\build\monoandroid12.0\Xamarin.AndroidX.Window.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Window.1.2.0.2\build\monoandroid12.0\Xamarin.AndroidX.Window.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.SlidingPaneLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SlidingPaneLayout.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.SlidingPaneLayout.********\build\monoandroid12.0\Xamarin.AndroidX.SlidingPaneLayout.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.UI.1.0.0.24\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.UI.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Legacy.Support.Core.UI.1.0.0.24\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.Core.UI.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Legacy.Support.V4.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.V4.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Legacy.Support.V4.1.0.0.23\build\monoandroid12.0\Xamarin.AndroidX.Legacy.Support.V4.targets')" />
  <Import Project="..\..\packages\Xamarin.AndroidX.Preference.1.2.1.4\build\monoandroid12.0\Xamarin.AndroidX.Preference.targets" Condition="Exists('..\..\packages\Xamarin.AndroidX.Preference.1.2.1.4\build\monoandroid12.0\Xamarin.AndroidX.Preference.targets')" />
  <Import Project="..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.targets" Condition="Exists('..\..\packages\Xamarin.Forms.5.0.0.2622\build\Xamarin.Forms.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Basement.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Basement.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Basement.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Basement.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Measurement.Connector.120.0.1.1\build\monoandroid12.0\Xamarin.Firebase.Measurement.Connector.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Measurement.Connector.120.0.1.1\build\monoandroid12.0\Xamarin.Firebase.Measurement.Connector.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Ads.Identifier.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Ads.Identifier.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Ads.Identifier.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Ads.Identifier.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Measurement.Base.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Base.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Base.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Base.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.Api.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.Api.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Stats.117.0.3.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Stats.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Stats.117.0.3.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Stats.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Measurement.Impl.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Impl.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Impl.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Impl.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Measurement.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Sdk.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Sdk.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Tasks.118.0.2.6\build\monoandroid12.0\Xamarin.GooglePlayServices.Tasks.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Tasks.118.0.2.6\build\monoandroid12.0\Xamarin.GooglePlayServices.Tasks.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Iid.Interop.117.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.Interop.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Iid.Interop.117.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.Interop.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Installations.InterOp.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.InterOp.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Installations.InterOp.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.InterOp.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Base.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Base.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Base.118.2.0.5\build\monoandroid12.0\Xamarin.GooglePlayServices.Base.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Auth.Api.Phone.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Api.Phone.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Auth.Api.Phone.118.0.1.8\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Api.Phone.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Auth.Base.118.0.10.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Base.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Auth.Base.118.0.10.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.Base.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.CloudMessaging.117.0.2.8\build\monoandroid12.0\Xamarin.GooglePlayServices.CloudMessaging.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.CloudMessaging.117.0.2.8\build\monoandroid12.0\Xamarin.GooglePlayServices.CloudMessaging.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Fido.120.1.0.1\build\monoandroid12.0\Xamarin.GooglePlayServices.Fido.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Fido.120.1.0.1\build\monoandroid12.0\Xamarin.GooglePlayServices.Fido.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Auth.120.7.0.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Auth.120.7.0.2\build\monoandroid12.0\Xamarin.GooglePlayServices.Auth.targets')" />
  <Import Project="..\..\packages\Xamarin.KotlinX.Coroutines.Play.Services.1.7.3.3\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Play.Services.targets" Condition="Exists('..\..\packages\Xamarin.KotlinX.Coroutines.Play.Services.1.7.3.3\build\monoandroid12.0\Xamarin.KotlinX.Coroutines.Play.Services.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Common.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Common.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Abt.121.1.1.5\build\monoandroid12.0\Xamarin.Firebase.Abt.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Abt.121.1.1.5\build\monoandroid12.0\Xamarin.Firebase.Abt.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Common.Ktx.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.Ktx.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Common.Ktx.120.4.2.1\build\monoandroid12.0\Xamarin.Firebase.Common.Ktx.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Datatransport.118.2.0.3\build\monoandroid12.0\Xamarin.Firebase.Datatransport.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Datatransport.118.2.0.3\build\monoandroid12.0\Xamarin.Firebase.Datatransport.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Installations.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Installations.117.2.0.1\build\monoandroid12.0\Xamarin.Firebase.Installations.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Config.121.5.0.1\build\monoandroid12.0\Xamarin.Firebase.Config.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Config.121.5.0.1\build\monoandroid12.0\Xamarin.Firebase.Config.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Iid.121.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Iid.121.1.0.13\build\monoandroid12.0\Xamarin.Firebase.Iid.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Messaging.123.3.1.1\build\monoandroid12.0\Xamarin.Firebase.Messaging.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Messaging.123.3.1.1\build\monoandroid12.0\Xamarin.Firebase.Messaging.targets')" />
  <Import Project="..\..\packages\Xamarin.GooglePlayServices.Measurement.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Api.targets" Condition="Exists('..\..\packages\Xamarin.GooglePlayServices.Measurement.Api.121.3.0.4\build\monoandroid12.0\Xamarin.GooglePlayServices.Measurement.Api.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.Analytics.121.3.0.4\build\monoandroid12.0\Xamarin.Firebase.Analytics.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.Analytics.121.3.0.4\build\monoandroid12.0\Xamarin.Firebase.Analytics.targets')" />
</Project>