﻿using Acr.UserDialogs;
using DrMuscle.Dependencies;
using DrMuscle.Layout;
using DrMuscle.Screens.History;
using DrMuscle.Screens.Subscription;
using DrMuscle.Screens.Workouts;
using DrMuscleWebApiSharedModel;
using SlideOverKit;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;
using System.Globalization;
using DrMuscle.Helpers;
using System.Text.RegularExpressions;
using DrMuscle.Resx;
using DrMuscle.Constants;
using System.Reflection;
using System.IO;
using Newtonsoft.Json;
using System.Threading;
using Rg.Plugins.Popup.Services;

namespace DrMuscle.Screens.Exercises
{
    public partial class ChooseYourCustomExercisePage : DrMusclePage
    {
        public ObservableRangeCollection<BodyPartSection> ExeList { get; set; }
            = new ObservableRangeCollection<BodyPartSection>();
        private List<ExerciceModel> exercises;
        public ObservableCollection<ExerciceModel> exerciseItems = new ObservableCollection<ExerciceModel>();
        public ObservableCollection<ExerciceModel> exerciseItemsResult = new ObservableCollection<ExerciceModel>();

        public ObservableCollection<ExerciceModel> favouriteItems = new ObservableCollection<ExerciceModel>();
        public ObservableCollection<ExerciceModel> customItems = new ObservableCollection<ExerciceModel>();

        AddUserExerciseModel newAddUserExercise;



        public ChooseYourCustomExercisePage()
        {
            InitializeComponent();
            BindingContext = this;

            
            ExpandableList.ItemTapped += ExerciseListView_ItemTapped;
            RefreshLocalized();
            if (LocalDBManager.Instance.GetDBSetting("ExerciseTypeList") == null)
                LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
            //Adding Messanger to subscribe localize updation
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });
        }

        private void StateImage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName.Equals("Source"))
            {
                var image = sender as Image;
                image.Opacity = 0;
                image.FadeTo(1, 1000);
            }
        }

        private void RefreshLocalized()
        {
            Title = AppResources.ChooseYourExercise;
            BtnCancel.Text = AppResources.Cancel;
            SearchEntry.Placeholder = AppResources.SearchExercises;
        }
        bool IsEntry = false;
        public override async void OnBeforeShow()
        {
            base.OnBeforeShow();
            if (IsEntry)
                return;
            IsEntry = true;
            if (LocalDBManager.Instance.GetDBSetting("ExerciseTypeList") == null)
                LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
            DependencyService.Get<IFirebase>().SetScreenName("choose_your_custom_exercise_page");
            ExeList.Clear();
            exerciseItems.Clear();
            exerciseItemsResult.Clear();
            customItems.Clear();
            CurrentLog.Instance.IsAddedNewExercise = false;
            CurrentLog.Instance.IsFavouriteUpdated = false;

            //try
            //{
            //GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetUserExercise(LocalDBManager.Instance.GetDBSetting("email").Value);
            //exercises = itemsSource.Exercises;
            //await UpdateExerciseList();
            try
            {
                
                CurrentLog.Instance.IsBodyPartUpdated = false;

                string jsonFileName = "Exercises.json";
                ExerciceModel exerciseList = new ExerciceModel();
                var assembly = typeof(AllExercisePage).GetTypeInfo().Assembly;
                Stream stream = assembly.GetManifestResourceStream($"{assembly.GetName().Name}.{jsonFileName}");
                using (var reader = new System.IO.StreamReader(stream))
                {
                    var jsonString = reader.ReadToEnd();

                    //Converting JSON Array Objects into generic list    
                    var list = JsonConvert.DeserializeObject<List<DBExerciseModel>>(jsonString);
                    exercises = new List<ExerciceModel>();
                    foreach (var item in list)
                    {
                        exercises.Add(new ExerciceModel()
                        {
                            Id = item.Id,
                            Label = item.Label,
                            BodyPartId = item.BodyPartId,
                            EquipmentId = item.EquipmentId,
                            IsBodyweight = item.IsBodyweight,
                            IsEasy = item.IsEasy,
                            IsMedium = item.IsMedium,
                            IsPlate = item.EquipmentId == 3,
                            IsSystemExercise = true,
                            VideoUrl = item.VideoUrl,
                            IsTimeBased = item.IsTimeBased,
                            IsUnilateral = item.IsUnilateral,
                            IsFlexibility = item.IsFlexibility,
                            IsAssisted = item.IsAssisted
                        });
                    }
                    exercises = exercises.OrderBy(x => x.Label).ToList();
                }
                await UpdateExerciseList();
                //exercises = itemsSource.Exercises;
                try
                {
                    GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetCustomExerciseForUser(LocalDBManager.Instance.GetDBSetting("email").Value);
                    //if (itemsSource != null && itemsSource.Exercises != null)
                    //{
                    //    exercises.AddRange(itemsSource.Exercises);
                    //}

                    //await UpdateExerciseList();
                    //if (itemsSource != null && itemsSource.Exercises != null)
                    //{

                    //    var swapContext = CurrentLog.Instance.SwapContext;
                    //    if (swapContext.SourceBodyPartId != null && itemsSource.Exercises.Count > 0)
                    //    {

                    //    }

                    //    foreach (var item in itemsSource.Exercises)
                    //    {
                    //        var bodyPartSection = ExeList.Where(x => x.Id == swapContext.SourceBodyPartId).ToList();
                    //        var nonePartSection = ExeList.Where(x => x.Id != swapContext.SourceBodyPartId).ToList();
                    //        if (bodyPartSection.Count > 0 && item.BodyPartId == swapContext.SourceBodyPartId)
                    //        {
                    //            exerciseItems.Add(item);
                    //            BodyPartSection body = bodyPartSection[0];
                    //            if (body.Expanded)
                    //                body.Add(item);
                    //            body._bodyPart.Exercices.Add(item);
                    //            body.Exercises.Add(item);
                    //        }
                    //        else if (nonePartSection.Count > 0)
                    //        {
                    //            exerciseItems.Add(item);
                    //            BodyPartSection body = nonePartSection[0];
                    //            if (body.Expanded)
                    //                body.Add(item);
                    //            body._bodyPart.Exercices.Add(item);
                    //            body.Exercises.Add(item);
                    //        }
                    //    }
                    //}

                    if (itemsSource != null && itemsSource.Exercises != null)
                    {
                        customItems.Clear();

                        foreach (var item in itemsSource.Exercises)
                        {
                            customItems.Add(item);
                            var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                            if (bodyPartSection.Count > 0)
                            {
                                BodyPartSection body = bodyPartSection[0];
                                if (body.Expanded)
                                    body.Add(item);
                                body._bodyPart.Exercices.Add(item);
                                body.Exercises.Add(item);
                            }
                            else
                            {
                                var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                                bodyPartShoulders.Exercices.Add(item);
                                BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                                ExeList.Insert(0, section);
                                ExpandableList.ItemsSource = ExeList;
                            }
                        }
                    }

                    GetUserExerciseResponseModel itemsSources = await DrMuscleRestClient.Instance.GetFavoriteExercises();
                    if (itemsSources != null && itemsSources.Exercises != null)
                    {
                        favouriteItems.Clear();

                        foreach (var item in itemsSources.Exercises)
                        {
                            favouriteItems.Add(item);
                            var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                            if (bodyPartSection.Count > 0)
                            {
                                BodyPartSection body = bodyPartSection[0];
                                if (body.Expanded)
                                    body.Add(item);
                                body._bodyPart.Exercices.Add(item);
                                body.Exercises.Add(item);
                            }
                            else
                            {
                                var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                                bodyPartShoulders.Exercices.Add(item);
                                BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                                ExeList.Insert(0, section);
                                ExpandableList.ItemsSource = ExeList;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {

                }


            }
            catch (Exception e)
            {
                ConnectionErrorPopup();

            }
            finally
            {
                IsEntry = false;
            }
            //}
            //catch (Exception e)
            //{
            
        }

        protected async override void OnAppearing()
        {
            base.OnAppearing();
            try
            {

            if (CurrentLog.Instance.IsBodyPartUpdated)
            {
                CurrentLog.Instance.IsBodyPartUpdated = false;
                await UpdateExerciseList();
            }
            if (CurrentLog.Instance.IsFavouriteUpdated)
            {
                OnBeforeShow();
            }
            if (CurrentLog.Instance.IsAddedNewExercise)
            {
                CurrentLog.Instance.IsAddedNewExercise = false;
                OnBeforeShow();
            }
            if (CurrentLog.Instance.IsRecoveredWorkout)
            {
                CurrentLog.Instance.IsRecoveredWorkout = false;
                AlertConfig ShowExplainDeloadPopUp = new AlertConfig()
                {
                    Title = "Suggested exercises",
                    Message = "The app recommends exercises for the same muscle group. Tap any to swap or scroll down for more exercises.",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Got it",

                };
                await UserDialogs.Instance.AlertAsync(ShowExplainDeloadPopUp);
                //CurrentLog.Instance.IsWalkthrough = true;
                PagesFactory.PopAsync();
            }

            }
            catch (Exception ex)
            {

            }
        }

        void OnCancelClicked(object sender, System.EventArgs e)
        {
            //StackLayout s = ((StackLayout)((Button)sender).Parent);
            //s.Children[0].IsVisible = false;
            //s.Children[1].IsVisible = false;
            //s.Children[2].IsVisible = false;
            //s.Children[3].IsVisible = false;
            //s.Children[4].IsVisible = false;
            //s.Children[5].IsVisible = true;
        }

        void OnContextMenuClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            var mi = ((Button)sender);
            ExerciceModel m = (ExerciceModel)mi.CommandParameter;
            if (!m.IsSystemExercise)
            {
                //s.Children[0].IsVisible = false;
                //s.Children[1].IsVisible = true;
                //s.Children[2].IsVisible = false;
                //s.Children[3].IsVisible = true;
                //s.Children[4].IsVisible = false;
                //s.Children[5].IsVisible = false;
            }
            else
            {
                //s.Children[0].IsVisible = true;
                //s.Children[1].IsVisible = false;
                //if (!string.IsNullOrEmpty(m.VideoUrl))
                //s.Children[2].IsVisible = true;
                //s.Children[3].IsVisible = true;
                //s.Children[4].IsVisible = false;
                //s.Children[5].IsVisible = false;
            }
        }

        public async void OnVideo(object sender, EventArgs e)
        {
            CurrentLog.Instance.VideoExercise = ((ExerciceModel)((Button)sender).CommandParameter);
            if (Device.RuntimePlatform.Equals(Device.iOS))
                DependencyService.Get<IOrientationService>().Landscape();
            await PagesFactory.PushAsync<ExerciseVideoPage>(true); OnCancelClicked(sender, e);
        }

        private async void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            try
            {

            
                if (((ExerciceModel)e.Item).Id != -1)
                {
                if (CurrentLog.Instance.IsAddingExerciseFromWorkout)
                {
                    ExerciceModel model = ((ExerciceModel)e.Item);
                    if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Where(x => x.Id == model.Id).FirstOrDefault() == null)
                    {
                        CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(model);
                        //Send message to refresh page
                        await PagesFactory.PopAsync();
                        await PagesFactory.GetPage<KenkoChooseYourWorkoutExercisePage>().UpdateExerciseList();
                            CurrentLog.Instance.IsAddingExerciseFromWorkout = false;
                            string serializedModel = JsonConvert.SerializeObject(CurrentLog.Instance.CurrentWorkoutTemplate);
                            LocalDBManager.Instance.SetDBSetting($"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}", serializedModel);
                        }
                    }
                else if (CurrentLog.Instance.SwapContext != null)
                    {
                        var swapContext = CurrentLog.Instance.SwapContext;
                        swapContext.TargetExerciseId = ((ExerciceModel)e.Item).Id;
                        ExerciceModel model = ((ExerciceModel)e.Item);
                        swapContext.Label = model.Label;
                        swapContext.IsBodyweight = model.IsBodyweight;
                        swapContext.IsSystemExercise = model.IsSystemExercise;
                        swapContext.IsEasy = model.IsEasy;
                        swapContext.BodyPartId = model.BodyPartId;
                        swapContext.VideoUrl = model.VideoUrl;
                        swapContext.IsTimeBased = model.IsTimeBased;
                        swapContext.IsPlate = model.IsPlate;
                        swapContext.IsUnilateral = model.IsUnilateral;
                        swapContext.IsMobility = model.IsFlexibility;
                        swapContext.IsWeighted = model.IsWeighted;
                        swapContext.IsOneHanded = model.IsOneHanded;
                        swapContext.IsAssisted = model.IsAssisted;
                        ((App)Application.Current).SwapExericesContexts.Swaps.Add(swapContext);
                        ((App)Application.Current).SwapExericesContexts.SaveContexts();
                        await DrMuscleRestClient.Instance.SetSwappedJson(new UserInfosModel()
                        {
                            SwappedJson = ((App)Application.Current).SwapExericesContexts.ToJson()
                        });

                        if (!Config.IsSwappedFirstTime && ((App)Application.Current).SwapExericesContexts.Swaps.Count == 1)
                        {
                            Config.IsSwappedFirstTime = true;
                            var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                            var modalPage = new Views.GeneralPopup("SwappedSuccess.png", "Awesome!", "First exercise swapped", "View exercises");
                            modalPage.Disappearing += (sender2, e2) =>
                            {
                                waitHandle.Set();
                            };
                            await PopupNavigation.Instance.PushAsync(modalPage);

                            await Task.Run(() => waitHandle.WaitOne());
                        }
                        Device.BeginInvokeOnMainThread(async () =>
                        {
                            if (Device.Android == Device.RuntimePlatform)
                            {
                                Device.BeginInvokeOnMainThread(async () =>
                                {
                                    await PagesFactory.PushAsyncWithoutBefore<KenkoChooseYourWorkoutExercisePage>();
                                    var page = PagesFactory.GetPage<KenkoChooseYourWorkoutExercisePage>();
                                    page.OnBeforeShow();
                                });
                            }
                            else
                                await PagesFactory.PushAsync<KenkoChooseYourWorkoutExercisePage>();
                            CurrentLog.Instance.SwapContext = null;
                        });
                        return;
                    }
                    //CurrentLog.Instance.IsFromExercise = true;
                    //await RunExercise((ExerciceModel)e.Item);
                }
                else
                {
                    AddMyOwnExercise();
                }
            }
            catch (Exception ex)
            {

            }
        }




        private void AddMyOwnExercise()
        {
            PromptConfig p = new PromptConfig()
            {
                InputType = InputType.Name,
                IsCancellable = true,
                Title = AppResources.NewExercise,
                Message = AppResources.LetsNameYourNewExercise,
                Placeholder = AppResources.TapHereToEnterName,
                OkText = AppResources.Create,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = new Action<PromptResult>(AddExercisesAction)
            };
            p.OnTextChanged += Name_OnTextChanged;
            UserDialogs.Instance.Prompt(p);
        }

        public void OnRename(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            ExerciceModel m = (ExerciceModel)mi.CommandParameter;
            PromptConfig p = new PromptConfig()
            {
                InputType = InputType.Default,
                IsCancellable = true,
                Title = string.Format("{0} \"{1}\"", AppResources.RenameExercise, m.Label),
                Placeholder = AppResources.EnterNewName,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = AppResources.Rename,
                OnAction = new Action<PromptResult>((PromptResult response) =>
                {
                    if (response.Ok)
                    {
                        RenameExercisesAction((Button)sender, m, response.Text);
                    }
                })
            };
            p.OnTextChanged += Name_OnTextChanged;
            UserDialogs.Instance.Prompt(p);

        }

        public async void RenameExercisesAction(Button sender, ExerciceModel model, string newLabel)
        {
            int itemIndex = exercises.IndexOf(model);
            model.Label = newLabel;
            BooleanModel result = await DrMuscleRestClient.Instance.RenameExercise(model);
            if (result.Result)
            {
                if (itemIndex >= 0)
                {
                    exercises.RemoveAt(itemIndex);
                    exercises.Insert(itemIndex, model);
                    await UpdateExerciseList();
                }
            }
        }

        public async void DeleteExercisesAction(ExerciceModel model)
        {
            int itemIndex = exerciseItemsResult.IndexOf(model);

            try
            {
                ExerciceModel m = model;
                SwapExerciseContext sec = ((App)Application.Current).SwapExericesContexts.Swaps.First(s => s.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && s.TargetExerciseId == m.Id);
                if (((App)Application.Current).SwapExericesContexts.Swaps.Contains(sec))
                {
                    ((App)Application.Current).SwapExericesContexts.Swaps.Remove(sec);
                    ((App)Application.Current).SwapExericesContexts.SaveContexts();
                }
            }
            catch (Exception ex)
            {

            }

            BooleanModel result = await DrMuscleRestClient.Instance.DeleteExercise(model);
            if (result.Result)
            {
                exerciseItemsResult.RemoveAt(itemIndex);
                exerciseItems.Remove(model);
            }
        }

        public void OnDelete(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            ExerciceModel m = (ExerciceModel)mi.CommandParameter;
            ConfirmConfig p = new ConfirmConfig()
            {
                Title = AppResources.DeleteExercise,
                Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                OkText = AppResources.Delete,
                CancelText = AppResources.Cancel,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            };
            p.OnAction = (obj) =>
            {
                if (obj)
                {
                    DeleteExercisesAction(m);
                }
            };
            UserDialogs.Instance.Confirm(p);
        }

        public async void ResetExercisesAction(ExerciceModel model)
        {
            BooleanModel result = await DrMuscleRestClient.Instance.ResetExercise(model);

            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());

        }

        public async void OnReset(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            ExerciceModel m = (ExerciceModel)mi.CommandParameter;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = m;
            if (m.IsSystemExercise)
                await PagesFactory.PushAsync<ExerciseSettingsPage>();
            else
                await PagesFactory.PushAsync<ExerciseCustomSettingsPage>();
            OnCancelClicked(sender, e);
        }

        private void OnBindingContextChanged(object sender, EventArgs e)
        {
            base.OnBindingContextChanged();

            if (((BindableObject)sender).BindingContext == null)
                return;
            ExerciceModel m = (ExerciceModel)((BindableObject)sender).BindingContext;

            var btnVideo = (DrMuscleButton)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]).Children[0];
            if (string.IsNullOrEmpty(m.VideoUrl))
                btnVideo.IsVisible = false;
            else
                btnVideo.IsVisible = true;
            if (m.IsSystemExercise)
            {

            }
        }

        void Section_Tapped(object sender, System.EventArgs e)
        {
            var obj = (BodyPartSection)((StackLayout)sender).BindingContext;
            obj.Expanded = !obj.Expanded;
        }
        //
        void NewExerciseTapped(object sender, System.EventArgs e)
        {
            AddMyOwnExercise();
        }
            private async Task UpdateExerciseList()
        {
            if (exercises == null)
                return;
            exerciseItems.Clear();
            exerciseItemsResult.Clear();
           

            ExeList.Clear();
            List<ExerciceModel> exo;
            exo = exercises.ToList();


            if (CurrentLog.Instance.SwapContext != null)
            {
                var swapContext = CurrentLog.Instance.SwapContext;
                if (swapContext.SourceBodyPartId != null)
                {
                    var bodyPart = exo.Where(x => x.BodyPartId == swapContext.SourceBodyPartId).ToList();
                    var notBodyPart = exo.Where(x => x.BodyPartId != swapContext.SourceBodyPartId).ToList();
                    var bodyPartShoulders = new BodyPartModel() { Label = $"{AppThemeConstants.GetBodyPartName(swapContext.SourceBodyPartId)} exercises" };

                    foreach (var item in bodyPart)
                    {
                        bodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                    ExeList.Add(section);

                    var otherBodyPartShoulders = new BodyPartModel() { Label = $"Other exercises" };
                    foreach (var item in notBodyPart.OrderBy(x => x.BodyPartId))
                    {
                        otherBodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection otherSection = new BodyPartSection(otherBodyPartShoulders, false);
                    ExeList.Add(otherSection);


                    ExpandableList.ItemsSource = ExeList;
                }
                else
                {
                   
                
                }
                System.Diagnostics.Debug.WriteLine($"SourceBodyPartId{swapContext.SourceBodyPartId}");
            }
            else
            {
               
            }

            if (ExeList.Count == 0)
            {
                //foreach (ExerciceModel em in exo)
                //exerciseItems.Add(em);
                var customExerAdded = false;
                foreach (var groupItem in exo.GroupBy(x => x.BodyPartId).ToList())
                {
                    var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key) };
                    foreach (var item in groupItem)
                    {
                        bodyPartShoulders.Exercices.Add(item);
                    }
                    //if (!customExerAdded)
                    //{
                    //    ExerciceModel addExercise = new ExerciceModel();
                    //    addExercise.Id = -1;
                    //    addExercise.Label = AppResources.TapToCreateNewCustomExercise;
                    //    addExercise.IsSystemExercise = true;
                    //    addExercise.BodyPartId = groupItem.Key;
                    //    bodyPartShoulders.Exercices.Add(addExercise);
                    //    customExerAdded = true;
                    //}
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                    ExeList.Add(section);
                }
                //{
                //    if (ExeList.Count ==0 && !customExerAdded)
                //    {
                //        var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(1) };
                //        ExerciceModel addExercise = new ExerciceModel();
                //        addExercise.Id = -1;
                //        addExercise.Label = AppResources.TapToCreateNewCustomExercise;
                //        addExercise.IsSystemExercise = true;
                //        addExercise.BodyPartId = 1;
                //        bodyPartShoulders.Exercices.Add(addExercise);
                //        customExerAdded = true;
                //        BodyPartSection section = new BodyPartSection(bodyPartShoulders, true);
                //        ExeList.Add(section);
                //    }
                //}
                ExpandableList.ItemsSource = ExeList;
            }
            foreach (var item in exo)
            {
                exerciseItems.Add(item);
            }

            //ExerciceModel addExerciseItem = new ExerciceModel();
            //addExerciseItem.Id = -1;
            //addExerciseItem.Label = AppResources.TapToCreateNewCustomExercise;
            //addExerciseItem.IsSystemExercise = true;
            //addExerciseItem.BodyPartId = 1;
            //exerciseItems.Add(addExerciseItem);

            exerciseItemsResult = exerciseItems;
            //ExerciseListView.ItemsSource = exerciseItemsResult;
            
        }

        private async void AddExercisesAction(PromptResult response)
        {
            if (response.Ok)
            {
                try
                {

                    ConfirmConfig IsEasyPopUp = new ConfirmConfig()
                    {
                        Title = AppResources.IsThisABodyweightExercise,
                        //Title = $"Let's set up your {response.Text}",
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = AppResources.YesBodyweight,
                        CancelText = AppResources.No,
                        OnAction = async (bool ok) =>
                        {
                            var userExercise = new AddUserExerciseModel()
                            {
                                Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                ExerciseName = response.Text,
                                IsBodyweight = ok
                            };
                            AskForIsEasy(userExercise);
                        }
                    };
                    await Task.Delay(100);
                    UserDialogs.Instance.Confirm(IsEasyPopUp);

                }
                catch (Exception e)
                {
                    ConnectionErrorPopup();

                }
            }
        }

        async void AskForIsEasy(AddUserExerciseModel newUserExercise)
        {
            newAddUserExercise = newUserExercise;
            EasyPopupStack.IsVisible = true;
        }
        void HardAction_Tapped(object sender, System.EventArgs e)
        {

            EasyPopupStack.IsVisible = false;
            CreateNewExercise();
        }
        void EasierAction_Tapped(object sender, System.EventArgs e)
        {
            EasyPopupStack.IsVisible = false;
            newAddUserExercise.IsMedium = true;
            CreateNewExercise();
        }
        void EasiestAction_Tapped(object sender, System.EventArgs e)
        {
            EasyPopupStack.IsVisible = false;
            newAddUserExercise.IsEasy = true;
            CreateNewExercise();
        }

        async void CreateNewExercise()
        {
            ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
            actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);
            actionSheetConfig.Add("Neck", () =>
            {
                newAddUserExercise.BodyPartId = 10;
                CreateExercise();
            });
            actionSheetConfig.Add("Shoulders", () =>
            {
                newAddUserExercise.BodyPartId = 2;
                CreateExercise();
            });
            actionSheetConfig.Add("Chest", () =>
            {
                newAddUserExercise.BodyPartId = 3;
                CreateExercise();
            });
            actionSheetConfig.Add("Back", () =>
            {
                newAddUserExercise.BodyPartId = 4;
                CreateExercise();
            });

            actionSheetConfig.Add("Lower back, glutes & hamstring", () =>
            {
                newAddUserExercise.BodyPartId = 14;
                CreateExercise();
            });
            actionSheetConfig.Add("Biceps", () =>
            {
                newAddUserExercise.BodyPartId = 5;
                CreateExercise();
            });
            actionSheetConfig.Add("Triceps", () =>
            {
                newAddUserExercise.BodyPartId = 6;
                CreateExercise();
            });
            actionSheetConfig.Add("Forearm", () =>
            {
                newAddUserExercise.BodyPartId = 11;
                CreateExercise();
            });
            //
            actionSheetConfig.Add("Abs", () =>
            {
                newAddUserExercise.BodyPartId = 7;
                CreateExercise();
            });
            actionSheetConfig.Add("Legs", () =>
            {
                newAddUserExercise.BodyPartId = 8;
                CreateExercise();
            });

            //
            actionSheetConfig.Add("Calves", () =>
            {
                newAddUserExercise.BodyPartId = 9;
                CreateExercise();
            });

            actionSheetConfig.SetTitle("Body part");
            UserDialogs.Instance.ActionSheet(actionSheetConfig);

        }

        async void FinalCreateExercise()
        {
            ExerciceModel newExercise = await DrMuscleRestClient.Instance.CreateNewExercise(newAddUserExercise);
            //exerciseItems.Insert(exerciseItems.Count() - 1, newExercise);
            newExercise.BodyPartId = newAddUserExercise.BodyPartId;
            exercises.Add(newExercise);
            await UpdateExerciseList();
            await RunExercise(newExercise);
        }
        async void CreateExercise()
        {
            ConfirmConfig IsEasyPopUp = new ConfirmConfig()
            {
                Title = "Left/right side separately?",
                Message = "Train your left and right sides separately, or both sides together?",
                //Title = $"Let's set up your {response.Text}",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Left/right separately",
                CancelText = "Both sides together",
                OnAction = async (bool ok) =>
                {
                    newAddUserExercise.IsUnilateral = ok;

                    AskForTimeBasedExercise();
                }
            };
            await Task.Delay(100);
            UserDialogs.Instance.Confirm(IsEasyPopUp);
        }

        async void AskForTimeBasedExercise()
        {
            ConfirmConfig TimebasePopUp = new ConfirmConfig()
            {
                Message = "Should this exercise progress in reps or seconds?",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Seconds",
                CancelText = AppResources.Reps,
            };

            var isConfirm = await UserDialogs.Instance.ConfirmAsync(TimebasePopUp);
            if (isConfirm)
            {
                newAddUserExercise.IsTimeBased = true;
                newAddUserExercise.IsBodyweight = true;
            }
            else
            {
                newAddUserExercise.IsTimeBased = false;
            }
            FinalCreateExercise();
        }

        void Handle_SearchTextChanged(object sender, Xamarin.Forms.TextChangedEventArgs e)
        {
            var list = exerciseItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
            var favList = favouriteItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
            var customList = customItems.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
            ExeList.Clear();
            
            if (CurrentLog.Instance.SwapContext != null)
            {
                var swapContext = CurrentLog.Instance.SwapContext;
                if (swapContext.SourceBodyPartId != null)
                {
                    var bodyPart = list.Where(x => x.BodyPartId == swapContext.SourceBodyPartId).ToList();
                    var notBodyPart = list.Where(x => x.BodyPartId != swapContext.SourceBodyPartId).ToList();

                    var bodyPartShoulders = new BodyPartModel() { Label = $"{AppThemeConstants.GetBodyPartName(swapContext.SourceBodyPartId)} exercises" };
                    foreach (var item in bodyPart)
                    {
                        bodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                    ExeList.Add(section);

                    var otherBodyPartShoulders = new BodyPartModel() { Label = $"Other exercises" };
                    foreach (var item in notBodyPart.OrderBy(x => x.BodyPartId))
                    {
                        otherBodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection otherSection = new BodyPartSection(otherBodyPartShoulders);
                    ExeList.Add(otherSection);
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        ExpandableList.ItemsSource = ExeList;
                    });

                    foreach (var item in customList)
                    {
                        var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                        if (bodyPartSection.Count > 0)
                        {
                            BodyPartSection body = bodyPartSection[0];
                            if (body.Expanded)
                                body.Add(item);
                            body._bodyPart.Exercices.Add(item);
                            body.Exercises.Add(item);
                        }
                        else
                        {
                            var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 26 };
                            bodyPartFavourite.Exercices.Add(item);
                            BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                            ExeList.Insert(0, sections);
                            Device.BeginInvokeOnMainThread(() =>
                            {
                                ExpandableList.ItemsSource = ExeList;
                            });
                        }
                    }

                    foreach (var item in favList)
                    {
                        var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                        if (bodyPartSection.Count > 0)
                        {
                            BodyPartSection body = bodyPartSection[0];
                            if (body.Expanded)
                                body.Add(item);
                            body._bodyPart.Exercices.Add(item);
                            body.Exercises.Add(item);
                        }
                        else
                        {
                            var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                            bodyPartFavourite.Exercices.Add(item);
                            BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                            ExeList.Insert(0, sections);
                            Device.BeginInvokeOnMainThread(() =>
                            {
                                ExpandableList.ItemsSource = ExeList;
                            });
                        }
                    }
                }
                else
                {
                   
                }
                System.Diagnostics.Debug.WriteLine($"SourceBodyPartId{swapContext.SourceBodyPartId}");
            }

            if (ExeList.Count == 0)
            {
                //foreach (ExerciceModel em in exo)
                //exerciseItems.Add(em);

                foreach (var groupItem in list.GroupBy(x => x.BodyPartId).ToList())
                {
                    var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key) };
                    foreach (var item in groupItem)
                    {
                        bodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                    ExeList.Add(section);
                }
                Device.BeginInvokeOnMainThread(() =>
                {
                    ExpandableList.ItemsSource = ExeList;
                });
            }
            if (e.NewTextValue.Length > 0)
                BtnCancel.IsVisible = true;
            else
                BtnCancel.IsVisible = false;
        }

        void Handle_CancelTapped(object sender, System.EventArgs e)
        {
            SearchEntry.Text = "";
        }
    }
}
