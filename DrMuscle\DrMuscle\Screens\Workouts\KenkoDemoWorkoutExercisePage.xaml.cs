﻿using Acr.UserDialogs;
using DrMuscle.Dependencies;
using DrMuscle.Screens.Exercises;
using DrMuscleWebApiSharedModel;
using SlideOverKit;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using System.Globalization;
using DrMuscle.Helpers;
using DrMuscle.Resx;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using DrMuscle.Message;
using DrMuscle.Screens.Me;
using DrMuscle.Layout;
using Newtonsoft.Json;
using Microsoft.AppCenter.Crashes;
using DrMuscle.Views;
using Rg.Plugins.Popup.Services;
using DrMuscle.Constants;
using DrMuscle.Cells;
using Plugin.Connectivity;
using Xamarin.Essentials;
using DrMuscle.Screens.User;

namespace DrMuscle.Screens.Workouts
{
    public partial class KenkoDemoWorkoutExercisePage : DrMusclePage, INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        protected void SetObservableProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = "")
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return;
            field = value;
            OnPropertyChanged(propertyName);
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            var changed = PropertyChanged;
            if (changed != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        private ObservableCollection<ExerciseWorkSetsModel> _exerciseItems;
        public ObservableCollection<ExerciseWorkSetsModel> exerciseItems
        {
            get { return _exerciseItems; }
            set
            {
                _exerciseItems = value;
                OnPropertyChanged("exerciseItems");
            }
        }

        public List<ObservableGroupCollection<ExerciseWorkSetsModel, WorkoutLogSerieModel>> GroupedData { get; set; }

        protected override void OnSizeAllocated(double width, double height)
        {
            base.OnSizeAllocated(width, height);
            if (Device.RuntimePlatform.Equals(Device.Android))
                StatusBarHeight.Height = 5;
            else
                StatusBarHeight.Height = App.StatusBarHeight;
            //double navigationBarHeight = Math.Abs(App.ScreenSize.Height - height - App.StatusBarHeight);
            // App.NavigationBarHeight = 146 + App.StatusBarHeight;// navigationBarHeight;
        }


        TimerPopup popup;
        bool isAppear = false;
        private GetUserProgramInfoResponseModel upi = null;
        private bool IsSettingsChanged { get; set; }
        private bool IsWelcomePopup { get; set; }
        List<long> OpenExercises = new List<long>();
        StackLayout contextMenuStack;
        private IFirebase _firebase;
        bool IsAnimate = false;
        bool IsSetWeightPopup = false;
        public KenkoDemoWorkoutExercisePage()
        {
            InitializeComponent();
            try
            {
                exerciseItems = new ObservableCollection<ExerciseWorkSetsModel>();
                //ExerciseListView.GroupHeaderTemplate = kenkoHeaderDataTemplateSelector;
                //ExerciseListView.GroupHeaderTemplate = new KenkoHeaderDataTemplateSelector
                //{
                //    RegularDateTemplate = RegularTemplate,
                //    FooterExerciseTemplate = HeaderTemplate
                //};
                ExerciseListView.ItemsSource = GroupedData;
                NavigationPage.SetHasNavigationBar(this, false);
                ExerciseListView.ItemTapped += ExerciseListView_ItemTapped;
                _firebase = DependencyService.Get<IFirebase>();

                if (LocalDBManager.Instance.GetDBSetting("PlatesKg") == null || LocalDBManager.Instance.GetDBSetting("PlatesLb") == null)
                {
                    var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                    LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);

                    var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                    LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
                }

                //SaveWorkoutButton.Clicked += SaveWorkoutButton_Clicked;
                RefreshLocalized();
                MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
                {
                    RefreshLocalized();
                });
                MessagingCenter.Subscribe<Message.ExerciseDeleteMessage>(this, "ExerciseDeleteMessage", (obj) =>
                {
                    IsSettingsChanged = false;
                });
                MessagingCenter.Subscribe<Message.SignupFinishMessage>(this, "SignupFinishMessage", (obj) =>
                {
                    WalkthroughPopup();
                });
                Timer.Instance.OnTimerChange += OnTimerChange;
                Timer.Instance.OnTimerDone += OnTimerDone;
                Timer.Instance.OnTimerStop += OnTimerStop;
            }
            catch (Exception ex)
            {

            }
        }



        private void RefreshLocalized()
        {
            Title = AppResources.ChooseExercise;
            // LblTodaysExercises.Text = AppResources.TodaYExercises;
            //  SaveWorkoutButton.Text = "Finish workout"; // AppResources.FinishAndSaveWorkout;
        }


        private async void WalkthroughPopup()
        {
            if (!IsWelcomePopup)
            {
                IsWelcomePopup = true;
                await Task.Delay(500);
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = $"{exerciseItems.Count - 1} exercises because you're {LocalDBManager.Instance.GetDBSetting("CustomExperience").Value}.",
                    Title = "Custom program ready!",
                    OkText = AppResources.GotIt
                });
                CellHeaderTapped(new Button() { BindingContext = exerciseItems.First() }, null);
            }
        }

        bool TimerBased = false;
        string timeRemain = "0";
        async void OnTimerDone()
        {
            try
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    BtnTimer.Text = null;
                    BtnTimer.Image = "stopwatch.png";
                });
                try
                {
                    if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                        LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                    if (TimerBased && LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" && isAppear)
                    {
                        await Task.Delay(100);
                        TimerBased = false;
                        popup = new TimerPopup(false);
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", timeRemain);
                        popup.popupTitle = "Work";
                        popup?.SetTimerRepsSets("");
                        popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                        Timer.Instance.Remaining = int.Parse(popup.RemainingSeconds);
                        PopupNavigation.Instance.PushAsync(popup);
                        Timer.Instance.StartTimer();

                    }
                }
                catch (Exception ex)
                {

                }
            } 
            catch (Exception ex)
            {

            }

        }

        void OnTimerStop()
        {
            try
            {
                //if (ToolbarItems.Count > 0)
                //{
                //    var index = 0;
                //    if (this.ToolbarItems.Count == 2)
                //    {
                //        index = 1;
                //    }
                //    this.ToolbarItems.RemoveAt(index);
                //    timerToolbarItem = new ToolbarItem("", "stopwatch.png", SlideTimerAction, ToolbarItemOrder.Primary, 0);
                //    this.ToolbarItems.Insert(index, timerToolbarItem);
                //}
                BtnTimer.Text = null;
                BtnTimer.Image = "stopwatch.png";
            }
            catch (Exception ex)
            {

            }
        }

        void OnTimerChange(int remaining)
        {
            Device.BeginInvokeOnMainThread(() =>
            {
                if (Timer.Instance.State == "RUNNING")
                {
                    BtnTimer.Text = remaining.ToString();
                    BtnTimer.Image = null;
                }
                else
                //if (remaining.ToString().Equals("0"))
                {
                    BtnTimer.Text = null;
                    BtnTimer.Image = "stopwatch.png";
                }
            });
        }

        private void UpdateOneRM(WorkoutLogSerieModelRef models, decimal weight, int reps)
        {
            if (contextMenuStack != null)
                HideContextButton();
            try
            {
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    //Update rest of sets from this update model
                    var index = item.IndexOf(models);

                    if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null)
                    {
                        var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                        var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg, item.RecoModel.FirstWorkSetReps);
                        var currentRM = ComputeOneRM(new MultiUnityWeight(weight, isKg ? "kg" : "lb").Kg, reps);
                        var worksets = string.Format("{0} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");

                        if (currentRM != 0)
                        {
                            var percentage = (currentRM - lastOneRM) * 100 / currentRM;
                            models.SetTitle = string.Format("Last time: {0} x {1}\nFor {2}{3:0.00}%, do:", item.RecoModel.FirstWorkSetReps, worksets, percentage >= 0 ? "+" : "", percentage);
                        }
                    }

                    break;
                }


            }
            catch (Exception ex)
            {

            }
        }


        private void UpdateWeoghtRepsMessageTapped(WorkoutLogSerieModelRef models)
        {
            if (contextMenuStack != null)
                HideContextButton();

            try
            {
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    //Update rest of sets from this update model


                    var index = item.IndexOf(models);
                    if (!item.IsPyramid)
                    {
                        var reps = models.Reps;
                        if (item.IsNormalSets)
                            reps = models.Reps;
                        else
                        {
                            if (models.IsFirstWorkSet)
                                reps = models.Reps <= 5 ? (int)Math.Ceiling((decimal)models.Reps / (decimal)3.0) : (int)models.Reps / 3;

                            if (reps <= 0)
                                reps = 1;
                        }
                        if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null)
                        {
                            var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                            var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg, item.RecoModel.FirstWorkSetReps);
                            var currentRM = ComputeOneRM(models.Weight.Kg, models.Reps);
                            var worksets = string.Format("{0} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");

                            if (currentRM != 0)
                            {
                                var percentage = (currentRM - lastOneRM) * 100 / currentRM;
                                models.SetTitle = string.Format("Last time: {0} x {1}\nFor {2}{3:0.00}%, do:", item.RecoModel.FirstWorkSetReps, worksets, percentage >= 0 ? "+" : "", percentage);
                            }
                        }

                        for (int i = index; i < item.Count; i++)
                        {
                            //if (item[i] == models)
                            //    continue;
                            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[i];
                            if (updatingItem.IsBackOffSet && !updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                            {
                                //updatingItem.Reps = (int)(reps + Math.Ceiling(reps * 1.1));
                                if (Math.Abs(updatingItem.Weight.Kg - models.Weight.Kg) > 0)
                                {
                                    var ob = ((Math.Abs(models.Weight.Kg - updatingItem.Weight.Kg) / models.Weight.Kg) > (decimal)0.3 ? (decimal)0.3 * (decimal)3.66 : Math.Abs(models.Weight.Kg - updatingItem.Weight.Kg) / models.Weight.Kg * (decimal)3.66);
                                    updatingItem.Reps = (int)reps + (int)Math.Ceiling(reps * ob);
                                }
                                else
                                {
                                    updatingItem.Reps = (int)(reps + Math.Ceiling(reps * 0.2));
                                }
                                continue;
                            }
                            if (!updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                            {
                                updatingItem.Weight = models.Weight;
                                if (reps != 0)
                                    updatingItem.Reps = reps;
                            }
                        }
                    }
                    else
                    {
                        if (models.IsFirstWorkSet && item.RecoModel != null && item.RecoModel.FirstWorkSetWeight != null)
                        {
                            var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                            var lastOneRM = ComputeOneRM(new MultiUnityWeight(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, isKg ? "kg" : "lb").Kg, item.RecoModel.FirstWorkSetReps);
                            var currentRM = ComputeOneRM(models.Weight.Kg, models.Reps);
                            var worksets = string.Format("{0} {1}", Math.Round(isKg ? item.RecoModel.FirstWorkSetWeight.Kg : item.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");

                            if (currentRM != 0)
                            {
                                var percentage = (currentRM - lastOneRM) * 100 / currentRM;
                                models.SetTitle = string.Format("Last time: {0} x {1}\nFor {2}{3:0.00}%, do:", item.RecoModel.FirstWorkSetReps, worksets, percentage >= 0 ? "+" : "", percentage);
                            }
                        }
                        for (int j = index; j < item.Count; j++)
                        {
                            //if (item[i] == models)
                            //    continue;
                            WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j];
                            if (j == index)
                            {
                                updatingItem.Weight = models.Weight;
                                updatingItem.Reps = models.Reps;
                                continue;
                            }

                            if (!updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                            {
                                var count = item.Where(x => x.IsWarmups == true).Count();
                                var rec = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j - 1];
                                var reps = rec.Reps + (j - count) + 1;
                                var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;

                                decimal weight = RecoComputation.RoundToNearestIncrement(isKg ? rec.Weight.Kg - (rec.Weight.Kg * (decimal)0.1) : rec.Weight.Lb - (rec.Weight.Lb * (decimal)0.1), item.RecoModel.Increments == null ? (decimal)1.0 : isKg ? item.RecoModel.Increments.Kg : TruncateDecimal(item.RecoModel.Increments.Lb, 5), isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb);

                                updatingItem.Weight = new MultiUnityWeight(weight, isKg ? "kg" : "lb");

                                if (reps != 0)
                                    updatingItem.Reps = reps;
                            }
                        }
                    }
                    break;
                }


            }
            catch (Exception ex)
            {

            }
        }
        public decimal TruncateDecimal(decimal value, int precision)
        {
            decimal step = (decimal)Math.Pow(10, precision);
            decimal tmp = Math.Truncate(step * value);
            return tmp / step;
        }
        private async void Load1RM(long id)
        {
            try
            {

                if (CurrentLog.Instance.Exercise1RM.ContainsKey(id))
                {

                }
                else
                {
                    //var _rm = await DrMuscleRestClient.Instance.GetOneRMForExerciseWithoutLoader(
                    //    new GetOneRMforExerciseModel()
                    //    {
                    //        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                    //        Massunit = LocalDBManager.Instance.GetDBSetting("massunit").Value,
                    //        ExerciseId = id
                    //    });
                    //if (_rm != null)
                    //    CurrentLog.Instance.Exercise1RM.Add(id, _rm);
                }


            }
            catch (Exception ex)
            {

            }
        }

        private void AddSetMessageTapped(WorkoutLogSerieModelRef models)
        {
            try
            {
                if (contextMenuStack != null)
                    HideContextButton();
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;

                    var newSet = new WorkoutLogSerieModelRef()
                    {
                        Id = item.Id,
                        IsLastSet = true,
                        IsFinished = false,
                        Weight = models.Weight,
                        Reps = models.Reps,
                        IsNext = true,
                        SetNo = $"SET {item.Count + 1}/{item.Count + 1}",
                        IsFirstSide = item.IsFirstSide,
                        ExerciseName = item.Label,
                        Increments = models.Increments,
                        SetTitle = "Last set—you can do this!",
                        IsTimeBased = models.IsTimeBased,
                        IsUnilateral = models.IsUnilateral,
                        IsBodyweight = models.IsBodyweight,
                        IsBackOffSet = models.IsBackOffSet,
                        IsNormalset = models.IsNormalset,
                    };
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                    {
                        var listOfSets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                        listOfSets.Add(newSet);
                    }
                    item.Add(newSet);
                    for (var i = 0; i < item.Count; i++)
                        ((WorkoutLogSerieModelRef)item[i]).SetNo = $"SET {i + 1}/{item.Count}";
                    if (item.First().IsWarmups)
                    {
                        var warmString = item.Where(l => l.IsWarmups).ToList().Count < 2 ? "warm-up" : "warm-ups";
                        ((WorkoutLogSerieModelRef)item.First()).SetTitle = $"{item.Where(l => l.IsWarmups).ToList().Count} {warmString}, {item.Where(l => !l.IsWarmups).ToList().Count} work sets\nLet's warm up:";
                    }
                    ScrollToActiveSet(newSet, item);
                    Device.BeginInvokeOnMainThread(async () => {
                        await Task.Delay(200);
                        ExerciseListView.ScrollTo(models, ScrollToPosition.MakeVisible, true);
                    });
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    break;
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void DeleteSetPermanentMessageTapped(WorkoutLogSerieModelRef models)
        {
            try
            {
                if (contextMenuStack != null)
                    HideContextButton();

                //TO rmeove complete set
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    if (item.Count == 1)
                        break;
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                    {
                        var listOfSets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                        var removed = listOfSets.Remove(models);
                        if (models.IsLastSet)
                            listOfSets.Last().IsLastSet = true;
                        if (models.IsFirstWorkSet && item.IsReversePyramid)
                            listOfSets.Last().IsFirstWorkSet = true;
                    }
                    item.Remove(models);
                    if (models.IsLastSet)
                        ((WorkoutLogSerieModelRef)item.Last()).IsLastSet = true;
                    if (models.IsFirstWorkSet && item.IsReversePyramid)
                        ((WorkoutLogSerieModelRef)item.Last()).IsFirstWorkSet = true;
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

                }
            }
            catch (Exception ex)
            {

            }
        }
        private void DeleteSetMessageTapped(WorkoutLogSerieModelRef models)
        {
            try
            {
                if (contextMenuStack != null)
                    HideContextButton();

                models.IsFinished = false;
                models.IsEditing = true;
                models.IsNext = true;
                //if (Timer.Instance.State != "RUNNING" && !Device.RuntimePlatform.Equals(Device.iOS))
                //    Xamarin.Forms.MessagingCenter.Send<SaveSetMessage>(new SaveSetMessage() { model = models, IsFinishExercise = false }, "SaveSetMessage");
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    models.IsFinished = false;

                    foreach (WorkoutLogSerieModelRef sets in item)
                    {
                        sets.IsEditing = false;
                        if (sets.IsNext)
                        {
                            sets.IsNext = false;
                            //if (Device.RuntimePlatform.Equals(Device.iOS))
                            //    sets.IsSizeChanged = !sets.IsSizeChanged;
                        }
                        sets.IsNext = false;
                    }
                    models.IsEditing = false;
                    models.IsNext = true;
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                        models.IsSizeChanged = !models.IsSizeChanged;

                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                        ExerciseListView.ItemsSource = exerciseItems;
                    break;
                }
                return;

                //TO rmeove complete set
                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                        continue;
                    models.IsFinished = false;
                    if (item.Count == 1)
                        break;
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(item.Id))
                    {
                        var listOfSets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[item.Id];
                        listOfSets.Remove(models);
                    }

                    item.Remove(models);
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

                }
            }
            catch (Exception ex)
            {

            }
        }

        private void UpdateSetTitleMessageTapped(WorkoutLogSerieModelRef models)
        {
            try
            {
                foreach (var item in exerciseItems)
                {
                    try
                    {

                        if (!item.Contains(models))
                            continue;

                        var index = item.IndexOf(models);
                        bool isRepsChanged = false;
                        if (item.Count > (index + 1))
                        {
                            ((WorkoutLogSerieModelRef)item[index + 1]).SetTitle = models.RIR == 0 ? "Ok, that was hard. Now let's try:" : "Got it! Now let's try:";
                            if (item.IsUnilateral && !item.IsFirstSide)
                                continue;
                            if (models.RIR == 0 && !item.IsBodyweight)
                            {
                                if (!item.IsPyramid)
                                {
                                    for (int i = index + 1; i < item.Count; i++)
                                    {
                                        var rec = (WorkoutLogSerieModelRef)item[i];
                                        decimal weight = RecoComputation.RoundToNearestIncrement(rec.Weight.Kg - (rec.Weight.Kg * (decimal)0.1), item.RecoModel.Increments == null ? (decimal)1.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                                        decimal oldWeight = RecoComputation.RoundToNearestIncrement(rec.Weight.Kg, item.RecoModel.Increments == null ? (decimal)1.0 : item.RecoModel.Increments.Kg, item.RecoModel.Min?.Kg, item.RecoModel.Max?.Kg);
                                        if (oldWeight == weight && i == index + 1)
                                        {
                                            isRepsChanged = true;
                                        }
                                        if (isRepsChanged)
                                            ((WorkoutLogSerieModelRef)item[i]).Reps -= ((WorkoutLogSerieModelRef)item[i]).Reps > 1 ? 1 : 0;
                                        else
                                            ((WorkoutLogSerieModelRef)item[i]).Weight = new MultiUnityWeight(weight, "kg");

                                        if (i == index + 1)
                                        {
                                            popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", rec.WeightDouble, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", rec.Reps).ReplaceWithDot());
                                            App.PCWeight = App.PCWeight = Convert.ToDecimal(rec.WeightDouble, CultureInfo.InvariantCulture);
                                            popup?.WeightCalculateAgain();
                                        }
                                    }
                                }
                                else
                                {
                                    for (int j = index + 1; j < item.Count; j++)
                                    {
                                        //if (item[i] == models)
                                        //    continue;
                                        WorkoutLogSerieModelRef updatingItem = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[j];

                                        if (!updatingItem.IsFinished && !updatingItem.IsWarmups && !updatingItem.IsFirstWorkSet)
                                        {
                                            var count = item.Where(x => x.IsWarmups == true).Count();
                                            var rec = updatingItem;

                                            var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;

                                            decimal weight = RecoComputation.RoundToNearestIncrement(isKg ? rec.Weight.Kg - (rec.Weight.Kg * (decimal)0.1) : rec.Weight.Lb - (rec.Weight.Lb * (decimal)0.1), item.RecoModel.Increments == null ? (decimal)1.0 : isKg ? item.RecoModel.Increments.Kg : TruncateDecimal(item.RecoModel.Increments.Lb, 5), isKg ? item.RecoModel.Min?.Kg : item.RecoModel.Min?.Lb, isKg ? item.RecoModel.Max?.Kg : item.RecoModel.Max?.Lb);

                                            var newWeight = new MultiUnityWeight(weight, isKg ? "kg" : "lb");
                                            var oldWeight = isKg ? updatingItem.Weight.Kg : updatingItem.Weight.Lb;
                                            if (oldWeight == weight && j == index + 1)
                                            {
                                                isRepsChanged = true;
                                            }
                                            if (isRepsChanged)
                                                updatingItem.Reps -= updatingItem.Reps > 1 ? 1 : 0;
                                            else
                                                updatingItem.Weight = newWeight;
                                            if (j == index + 1)
                                            {
                                                popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", updatingItem.WeightDouble, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", updatingItem.Reps).ReplaceWithDot());
                                                App.PCWeight = App.PCWeight = Convert.ToDecimal(updatingItem.WeightDouble, CultureInfo.InvariantCulture);
                                                popup?.WeightCalculateAgain();
                                            }

                                        }
                                    }
                                }
                            }
                            else if (models.RIR == 0 && item.IsBodyweight)
                            {
                                for (int i = index + 1; i < item.Count; i++)
                                {
                                    var rec = (WorkoutLogSerieModelRef)item[i];

                                    int reps = (int)Math.Floor(rec.Reps - (rec.Reps * (decimal)0.1));
                                    if (reps < 1)
                                        reps = 1;
                                    ((WorkoutLogSerieModelRef)item[i]).Reps = reps;
                                    if (i == index + 1)
                                    {
                                        if (rec.Id == 16508)
                                            popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Fast", rec.Reps).ReplaceWithDot());
                                        else
                                            popup?.SetTimerRepsSets(string.Format("{1} x {0} ", "Body", rec.Reps).ReplaceWithDot());
                                    }
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                }

            }
            catch (Exception ex)
            {

            }
        }

        private void SaveSetMessageTapped(WorkoutLogSerieModelRef models, bool IsFinished)
        {
            try
            {
                if (contextMenuStack != null)
                    HideContextButton();
                if (Device.RuntimePlatform.Equals(Device.Android))
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

                foreach (var item in exerciseItems)
                {
                    if (!item.Contains(models))
                    {
                        foreach (WorkoutLogSerieModelRef subItem in item)
                        {
                            subItem.IsActive = false;
                        }
                        continue;
                    }

                    if (IsFinished)
                    {
                        //Called SaveSet
                        Finished_Clicked(item);
                        return;
                    }

                    if (models.IsLastSet && models.IsUnilateral && models.IsFirstSide)
                    {
                        WorkoutLogSerieModelRef first = (DrMuscle.Layout.WorkoutLogSerieModelRef)item.First();
                        App.PCWeight = Convert.ToDecimal(first.WeightDouble, CultureInfo.InvariantCulture);
                        try
                        {
                            if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                                LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                            //if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true")
                            //{

                            //    popup = new TimerPopup();
                            //    popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                            //    popup.popupTitle = "";
                            //    if (first.IsBodyweight)
                            //        popup?.SetTimerRepsSets(string.Format("Body x {0} ", first.Reps).ReplaceWithDot());
                            //    else
                            //        popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2} ", first.WeightDouble, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", first.Reps).ReplaceWithDot());
                            //    popup.SetTimerText();
                            //    //if (item.IsTimeBased)
                            //    //{
                            //    //    timeRemain = Convert.ToString(first.Reps);
                            //    //    TimerBased = true;
                            //    //}
                            //    //else
                            //    TimerBased = false;
                            //    PopupNavigation.Instance.PushAsync(popup);

                            //}
                            first.IsActive = true;

                        }
                        catch (Exception ex)
                        {

                        }

                        if (item.Count > 1)
                        {
                            if (Device.RuntimePlatform.Equals(Device.Android))
                            {
                                var index = item.IndexOf(models);
                                WorkoutLogSerieModelRef before = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[index > 1 ? index - 1 : index];
                                ScrollToActiveSet(before, item);
                            }
                        }

                        ScrollToActiveSet(models, item);
                        Device.BeginInvokeOnMainThread(async () => {
                            await Task.Delay(200);
                            ExerciseListView.ScrollTo(models, ScrollToPosition.MakeVisible, true);
                        });
                    }
                    else if (models.IsLastSet)
                    {
                        if (item.Count > 1)
                        {
                            if (Device.RuntimePlatform.Equals(Device.Android))
                            {
                                var index = item.IndexOf(models);
                                WorkoutLogSerieModelRef before = (DrMuscle.Layout.WorkoutLogSerieModelRef)item[index > 1 ? index - 1 : index];
                                ScrollToActiveSet(before, item);
                            }
                        }

                        ScrollToActiveSet(models, item);
                        Device.BeginInvokeOnMainThread(async () => {
                            await Task.Delay(200);
                            ExerciseListView.ScrollTo(models, ScrollToPosition.MakeVisible, true);
                        });
                    }

                    foreach (WorkoutLogSerieModelRef logSerieModel in item)
                    {
                        if (logSerieModel.IsFinished)
                            continue;
                        if (!logSerieModel.IsNext)
                        {
                            logSerieModel.IsNext = true;
                            App.PCWeight = Convert.ToDecimal(logSerieModel.WeightDouble, CultureInfo.InvariantCulture);
                            try
                            {
                                if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                                    LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                                if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true")
                                {

                                    popup = new TimerPopup(item.IsPlate);
                                    popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                                    popup.popupTitle = "";
                                    if (logSerieModel.IsBodyweight)
                                    {
                                        if (logSerieModel.Id == 16508)
                                            popup?.SetTimerRepsSets(string.Format("{0} x {1} ", logSerieModel.Reps, logSerieModel.IsWarmups ? "Brisk" : "Fast").ReplaceWithDot());
                                        else
                                            popup?.SetTimerRepsSets(string.Format("{0} x Body ", logSerieModel.Reps).ReplaceWithDot());
                                    }
                                    else
                                        popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", logSerieModel.WeightDouble, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", logSerieModel.Reps).ReplaceWithDot());
                                    popup.SetTimerText();
                                    if (item.IsTimeBased)
                                    {
                                        timeRemain = Convert.ToString(logSerieModel.Reps);
                                        TimerBased = true;
                                    }
                                    else
                                        TimerBased = false;
                                    PopupNavigation.Instance.PushAsync(popup);

                                }
                                logSerieModel.IsActive = true;

                                if (item.IndexOf(logSerieModel) > 0)
                                {

                                    // ScrollToActiveSet(old, item);

                                    if (Device.RuntimePlatform.Equals(Device.Android))
                                    {
                                        var index = item.IndexOf(logSerieModel);
                                        WorkoutLogSerieModelRef old = (WorkoutLogSerieModelRef)item[index - 1];
                                        Device.BeginInvokeOnMainThread(async () =>
                                        {
                                            ExerciseListView.ScrollTo(old, ScrollToPosition.End, false);
                                            ScrollToActiveSet(logSerieModel, item);

                                            await Task.Delay(200);
                                            ExerciseListView.ScrollTo(logSerieModel, ScrollToPosition.MakeVisible, true);

                                        });
                                    }
                                    else
                                        ScrollToActiveSet(logSerieModel, item);


                                }
                                else
                                {
                                    ScrollToActiveSet(logSerieModel, item);
                                }
                            }
                            catch (Exception ex)
                            {

                            }
                            if (Device.RuntimePlatform.Equals(Device.iOS))
                                ExerciseListView.ScrollTo(logSerieModel, ScrollToPosition.MakeVisible, true);
                            //else
                            //    ExerciseListView.ScrollTo(logSerieModel, ScrollToPosition.MakeVisible, true);

                            break;
                        }
                    }

                }
                ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

                try
                {
                    ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                    ((App)Application.Current).WorkoutLogContext.SaveContexts();
                }
                catch (Exception ex)
                {

                }
            }
            catch (Exception ex)
            {

            }
        }

        public override async void OnBeforeShow()
        {
            base.OnBeforeShow();
            try
            {
                App.IsNUX = false;
                IsAnimate = true;
                IsWelcomePopup = false;
                IsSetWeightPopup = false;
                CurrentLog.Instance.IsFromExercise = false;
                await UpdateExerciseList();
                OpenExercises.Clear();
                CurrentLog.Instance.IsFromEndExercise = false;
                if (LocalDBManager.Instance.GetDBSetting($"Time{DateTime.Now.Year}") == null || LocalDBManager.Instance.GetDBSetting($"Time{DateTime.Now.Year}").Value == null || LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Date}") == null)
                    LocalDBManager.Instance.SetDBSetting($"Time{DateTime.Now.Year}", $"{DateTime.Now.Ticks}");


            }
            catch (Exception ex)
            {

            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            try
            {
                isAppear = false;
                try
                {
                    DependencyService.Get<IKeyboardHelper>().HideKeyboard();
                }
                catch (Exception ex)
                {

                }
                MessagingCenter.Unsubscribe<Message.SaveSetMessage>(this, "SaveSetMessage");
                MessagingCenter.Unsubscribe<Message.UpdateSetTitleMessage>(this, "UpdateSetTitleMessage");
                MessagingCenter.Unsubscribe<Message.DeleteSetMessage>(this, "DeleteSetMessage");
                MessagingCenter.Unsubscribe<Message.CellUpdateMessage>(this, "CellUpdateMessage");
                MessagingCenter.Unsubscribe<Message.WeightRepsUpdatedMessage>(this, "WeightRepsUpdatedMessage");
                MessagingCenter.Unsubscribe<Message.AddSetMessage>(this, "AddSetMessage");
            }
            catch (Exception ex)
            {

            }
        }

        private bool IsFromMePage()
        {
            var isMePage = false;
            foreach (var item in Navigation.NavigationStack)
            {
                if (item is MeCombinePage)
                {
                    isMePage = true;
                    break;
                }
            }
            return isMePage;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            _firebase.SetScreenName("kenko_workout_exercise");
            try
            {
                isAppear = true;
                MessagingCenter.Subscribe<Message.SaveSetMessage>(this, "SaveSetMessage", (obj) =>
                {
                    SaveSetMessageTapped(obj.model, obj.IsFinishExercise);
                });

                MessagingCenter.Subscribe<Message.UpdateSetTitleMessage>(this, "UpdateSetTitleMessage", (obj) =>
                {
                    UpdateSetTitleMessageTapped(obj.model);
                });



                MessagingCenter.Subscribe<Message.OneRMChangedMessage>(this, "OneRMChangedMessage", (obj) =>
                {
                    UpdateOneRM(obj.model, obj.Weight, obj.Reps);
                });

                MessagingCenter.Subscribe<Message.DeleteSetMessage>(this, "DeleteSetMessage", (obj) =>
                {
                    if (!obj.isPermenantDelete)
                        DeleteSetMessageTapped(obj.model);
                    else
                        DeleteSetPermanentMessageTapped(obj.model);
                });

                MessagingCenter.Subscribe<Message.CellUpdateMessage>(this, "CellUpdateMessage", (obj) =>
                {
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                });

                //
                MessagingCenter.Subscribe<Message.WeightRepsUpdatedMessage>(this, "WeightRepsUpdatedMessage", (obj) =>
                {
                    UpdateWeoghtRepsMessageTapped(obj.model);
                });

                MessagingCenter.Subscribe<Message.AddSetMessage>(this, "AddSetMessage", (obj) =>
                {
                    AddSetMessageTapped(obj.model);
                });

                Task.Factory.StartNew(async () =>
                {
                    ((RightSideMasterPage)SlideMenu).ResignedField();
                });

                DependencyService.Get<IKeyboardHelper>().HideKeyboard();
            }
            catch (Exception ex)
            {

            }
           
        }

        private async void ChangeWorkout()
        {
            try
            {

                ConfirmConfig ShowConfirmPopUp = new ConfirmConfig()
                {
                    Message = $"Change program and do {CurrentLog.Instance.CurrentWorkoutTemplate.Label} next?",
                    Title = "",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Select workout",
                    CancelText = AppResources.Cancel,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            ChangingWorkout();
                        }
                        else
                        {
                            await PagesFactory.PopAsync();
                        }
                    }
                };
                UserDialogs.Instance.Confirm(ShowConfirmPopUp);

            }
            catch (Exception ex)
            {

            }
        }

        private async void ChangingWorkout()
        {
            try
            {
                try
                {
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                            LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                        }
                    }


                }
                catch (Exception ex)
                {

                }
                try
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp == 999)
                    {
                        foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                        {
                            LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exerciceModel.Id}", "false");
                        }
                        await PagesFactory.PopToRootAsync();
                        return;
                    }
                }
                catch (Exception ex)
                {

                }
                bool isSystem = false;
                BooleanModel successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkoutV2(new SaveWorkoutModel() { WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id });
                try
                {
                    if (successWorkoutLog.Result)
                    {
                        Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage(), "UpdatedWorkoutMessage");
                    }
                    LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
                    LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());
                    isSystem = CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise;
                }
                catch (Exception ex)
                {

                }

                var nextworkoutName = CurrentLog.Instance.CurrentWorkoutTemplate.Label;
                CurrentLog.Instance.CurrentWorkoutTemplate = null;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
                string fname = LocalDBManager.Instance.GetDBSetting("firstname").Value;

                try
                {
                    AlertConfig p = new AlertConfig()
                    {
                        Title = $"{AppResources.GotIt} {fname}!",
                        Message = $"Your next workout will be {nextworkoutName}.",
                        OkText = AppResources.Ok,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    };
                    p.OnAction = async () =>
                    {
                        await PagesFactory.PopToRootAsync();
                    };
                    UserDialogs.Instance.Alert(p);

                }

                catch (Exception ex)
                {
                    await PagesFactory.PopToRootAsync();
                }

            }
            catch (Exception ex)
            {

            }
        }

        private async void Finished_Clicked(ExerciseWorkSetsModel model)
        {
            try
            {

                if (model.IsFirstSide && model.IsUnilateral)
                {

                    model.IsFirstSide = false;
                    List<WorkoutLogSerieModelRef> setList = new List<WorkoutLogSerieModelRef>();
                    foreach (WorkoutLogSerieModelRef item in model)
                    {
                        item.IsFirstSide = false;
                        item.IsFinished = false;
                        item.IsNext = false;
                        item.IsActive = false;
                        item.IsEditing = false;
                        setList.Add(item);
                    }
                    WorkoutLogSerieModelRef workout = ((WorkoutLogSerieModelRef)model.First());
                    workout.IsNext = true;
                    workout.IsActive = true;
                    App.PCWeight = Convert.ToDecimal(workout.WeightDouble, CultureInfo.InvariantCulture); ;
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;

                    if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                    {

                        if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" && Timer.Instance.State == "RUNNING")
                        {

                            popup = new TimerPopup(model.IsPlate);
                            popup.RemainingSeconds = Convert.ToString(Timer.Instance.Remaining);
                            popup.popupTitle = "";
                            if (workout.IsBodyweight)
                            {
                                popup?.SetTimerRepsSets(string.Format("{0} x Body", workout.Reps).ReplaceWithDot());
                                popup?.SetReadyForTitle();
                            }
                            else
                            {
                                popup?.SetTimerRepsSets(string.Format("{2} x {0:0.00} {1} ", workout.WeightDouble, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", workout.Reps).ReplaceWithDot());
                                popup?.SetReadyForTitle();
                            }
                            popup.SetTimerText();
                            //if (item.IsTimeBased)
                            //{
                            //    timeRemain = Convert.ToString(first.Reps);
                            //    TimerBased = true;
                            //}
                            //else
                            TimerBased = false;
                            PopupNavigation.Instance.PushAsync(popup);

                        }
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", App.globalTime.ToString());
                        //timeRemain = TimerEntry;
                    }
                    //}
                    //Timer.Instance.StopTimer();
                    //Timer.Instance.stopRequest = false;
                    //Timer.Instance.StartTimer();
                    ////Open fullscreen timer
                    //if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true")
                    //{

                    //    cellhepopup = new TimerPopup();
                    //    popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                    //    popup.popupTitle = "";
                    //    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2} ", workout.WeightDouble, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", workout.Reps).ReplaceWithDot());
                    //    popup.SetTimerText();
                    //    if (model.IsTimeBased)
                    //    {
                    //        timeRemain = Convert.ToString(workout.Reps);
                    //        TimerBased = true;
                    //    }
                    //    else
                    //        TimerBased = false;
                    //    PopupNavigation.Instance.PushAsync(popup);

                    //}



                    ScrollToSnap(setList, model);
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                    {
                        setList.First().IsSizeChanged = !setList.First().IsSizeChanged;
                    }
                    else
                    {
                        Device.BeginInvokeOnMainThread(() =>
                        {
                            ExerciseListView.ScrollTo(setList.First(), ScrollToPosition.Start, false);
                        });
                    }
                    if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value != "true")
                    {
                        AlertConfig ShowExplainRIRPopUp = new AlertConfig()
                        {
                            Title = "Well done! Now do all sets for side 2",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = AppResources.Ok,

                        };
                        UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
                    }

                    //if (Config.ShowAllSetPopup == false)
                    //{
                    //    if (App.ShowAllSetPopup)
                    //        return;
                    //    App.ShowAllSetPopup = true;
                    //    ConfirmConfig ShowWelcomePopUp4 = new ConfirmConfig()
                    //    {

                    //        Title = "Well done! Now do all sets for side 2",
                    //        //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //        OkText = AppResources.GotIt,
                    //        CancelText = AppResources.RemindMe,
                    //        OnAction = async (bool ok) =>
                    //        {
                    //            if (ok)
                    //            {
                    //                Config.ShowAllSetPopup = true;
                    //            }
                    //            else
                    //            {
                    //                Config.ShowAllSetPopup = false;
                    //            }
                    //        }
                    //    };
                    //    await Task.Delay(100);
                    //    UserDialogs.Instance.Confirm(ShowWelcomePopUp4);
                    //}
                    return;
                }

                if (model.IsFinished)
                {
                    foreach (WorkoutLogSerieModelRef item in model)
                    {

                        var newWorkOutLog = new WorkoutLogSerieModel()
                        {
                            Id = item.Id,
                            Reps = item.Reps,
                            Weight = item.Weight,
                            Exercice = GetExerciseModel(model)
                        };
                        BooleanModel result = await DrMuscleRestClient.Instance.EditWorkoutLogSeries(newWorkOutLog);

                    }
                    model.IsNextExercise = false;
                    model.Clear();
                    ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    return;
                }
                ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                {
                    Title = $"Finish & save {model.Label}?",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Finish & save",
                    CancelText = AppResources.Cancel,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            PushToDataServer(model);
                        }
                        else
                        {
                        }
                    }
                };
                UserDialogs.Instance.Confirm(ShowRIRPopUp);

            }
            catch (Exception ex)
            {

            }
        }

        private async void PushToDataServer(ExerciseWorkSetsModel model)
        {
            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
                    return;
                }
                try
                {
                    if (LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Date}") == null)
                        LocalDBManager.Instance.SetDBSetting($"Exercises{DateTime.Now.Date}", "1");
                    else
                    {
                        var exeCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Date}").Value);
                        exeCount += 1;
                        LocalDBManager.Instance.SetDBSetting($"Exercises{DateTime.Now.Date}", $"{exeCount}");
                    }

                }
                catch (Exception ex)
                {

                }
                bool result = true;
                try
                {
                    CurrentLog.Instance.EndExerciseActivityPage = this.GetType();
                    int? RIR = null;
                    List<WorkoutLogSerieModel> serieModelList = new List<WorkoutLogSerieModel>();
                    foreach (WorkoutLogSerieModelRef l in model)
                    {
                        if (l.IsFinished)
                        {
                            if (l.IsFirstWorkSet)
                                RIR = l.RIR;
                            WorkoutLogSerieModelEx serieModel = new WorkoutLogSerieModelEx()
                            {
                                Exercice = new ExerciceModel() { Id = model.Id },
                                Reps = l.Reps,
                                UserId = CurrentLog.Instance.ExerciseLog.UserId,
                                Weight = l.Weight,
                                RIR = RIR,
                                IsWarmups = l.IsWarmups
                            };
                            if (!l.IsWarmups)
                            {
                                if (LocalDBManager.Instance.GetDBSetting($"Sets{DateTime.Now.Date}") == null)
                                    LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", "1");
                                else
                                {
                                    var setCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"Sets{DateTime.Now.Date}").Value);
                                    setCount += 1;
                                    LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", $"{setCount}");
                                }
                            }
                            serieModelList.Add(serieModel);
                            //BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerie(serieModel);
                            //result = result && b.Result;
                        }
                    }
                    if (serieModelList.Count > 0)
                    {
                        BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerieList(serieModelList);
                        result = result && b.Result;
                        CurrentLog.Instance.LastSerieModelList = serieModelList;
                    }

                    DateTime? maxDate = null;
                    try
                    {
                        string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                        string exId = $"{model.Id}";
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                        {
                            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                            {
                                maxDate = DateTime.Now;
                            }
                        }

                        DrMuscleRestClient.Instance.AddUpdateExerciseUserLightSessionWithBodyPart(new LightSessionModel()
                        {
                            ExerciseId = model.Id,
                            IsLightSession = CurrentLog.Instance.RecommendationsByExercise[model.Id].IsLightSession,
                            MaxChallengeDate = maxDate,
                            BodypartId = (long)model.BodyPartId,
                            WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id
                        });
                    }
                    catch (Exception ex)
                    {

                    }
                }
                catch (Exception ex)
                {
                    var properties = new Dictionary<string, string>
                {
                    { "FinishExercise", $"{ex.StackTrace}" }
                };
                    Crashes.TrackError(ex, properties);
                }
                try
                {
                    if (result)
                    {
                        if (Timer.Instance.State == "RUNNING")
                        {
                            await Timer.Instance.StopTimer();
                        }

                        if ((Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == model.Id) == null)
                            (Application.Current as App)?.FinishedExercices.Add(GetExerciseModel(model));


                        //if (!CurrentLog.Instance.IsFromExercise)
                        LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{model.Id}", "true");
                        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                        DependencyService.Get<IFirebase>().LogEvent("finished_exercise", "");
                        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(model.Id))
                            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(model.Id);

                        ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef;
                        ((App)Application.Current).WorkoutLogContext.SaveContexts();
                        //foreach (var item in items)
                        //{
                        //    item.IsExerciseFinished = true;
                        //}
                        //items.First().IsNext = true;

                        CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(model);
                        CurrentLog.Instance.IsFromExercise = false;
                        model.IsNextExercise = false;
                        model.IsFinished = true;
                        model.Clear();

                        await PagesFactory.PushAsync<EndExercisePage>();
                        //Fetch next exercise reco inline if possible
                        var exModel = exerciseItems.Where(x => x.IsFinished == false).FirstOrDefault();
                        if (exModel != null && exModel.RecoModel == null)
                        {
                            FetchNextExerciseBackgroundData(exModel);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var properties = new Dictionary<string, string>
                    {
                        { "FinishExerciseResultHandle", $"{ex.StackTrace}" }
                    };
                    Crashes.TrackError(ex, properties);
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
                }

            }
            catch (Exception ex)
            {

            }
        }

        private async void FetchNextExerciseBackgroundData(ExerciseWorkSetsModel m)
        {
            try
            {
                if (m.Id == 0 && m.Id == -1)
                    return;
                NewExerciseLogResponseModel newExercise = await DrMuscleRestClient.Instance.IsNewExerciseWithSessionInfoWithoutLoader(new ExerciceModel() { Id = m.Id });
                if (newExercise != null)
                {
                    if (!newExercise.IsNewExercise)
                    {
                        try
                        {
                            long? workoutId = null;
                            try
                            {
                                //if (!CurrentLog.Instance.IsFromExercise)
                                workoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
                            }
                            catch (Exception)
                            {

                            }

                            DateTime? lastLogDate = newExercise.LastLogDate;
                            int? sessionDays = null;


                            string WeightRecommandation;
                            RecommendationModel reco = null;

                            if (lastLogDate != null)
                            {
                                var days = (int)(DateTime.Now - (DateTime)lastLogDate).TotalDays;
                                if (days >= 5 && days <= 9)
                                    sessionDays = days;
                                if (days > 9)
                                {
                                    return;
                                }
                            }

                            string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;

                            string exId = $"{m.Id}";
                            var lastTime = LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle);

                            if (lastTime != null)
                            {
                                try
                                {
                                    if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value))
                                    {
                                        var LastRecoPlus1Day = Convert.ToDateTime(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value);
                                        if (LastRecoPlus1Day > DateTime.Now)
                                        {
                                            var recommendation = RecoContext.GetReco("Reco" + exId + setStyle);
                                            if (recommendation != null)
                                                m.RecoModel = recommendation;
                                        }
                                        else
                                        {
                                            //Removed saved set if available
                                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef != null && CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);

                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"Exception is:{ex.ToString()}");
                                }
                            }
                            long? swapedExId = null;
                            try
                            {
                                if (m.IsSwapTarget)
                                {
                                    bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.TargetExerciseId == m.Id);
                                    swapedExId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.TargetExerciseId == m.Id).SourceExerciseId;
                                }
                            }
                            catch (Exception ex)
                            {

                            }
                            if (LocalDBManager.Instance.GetDBSetting("IsPyramid") == null)
                                LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                            bool? isQuick = false;
                            if (LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "null")
                                isQuick = null;
                            else
                                isQuick = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false;
                            if (m.RecoModel == null)
                            {
                                if (LocalDBManager.Instance.GetDBSetting("SetStyle").Value == "Normal" || m.Id == 16508)
                                {
                                    if (LocalDBManager.Instance.GetDBSetting("IsPyramid").Value == "true" && m.IsBodyweight && m.Id != 16508)
                                    {
                                        m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationRestPauseRIRForExerciseWithoutDeload(new GetRecommendationForExerciseModel()
                                        {
                                            Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                            ExerciseId = m.Id,
                                            IsQuickMode = isQuick,
                                            LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                            WorkoutId = workoutId,
                                            SwapedExId = swapedExId
                                        });
                                    }
                                    else
                                    {
                                        m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationNormalRIRForExerciseWithoutLoaderWithoutDeload(new GetRecommendationForExerciseModel()
                                        {
                                            Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                            ExerciseId = m.Id,
                                            IsQuickMode = isQuick,
                                            LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                            WorkoutId = workoutId,
                                            SwapedExId = swapedExId
                                        });
                                    }

                                }
                                else
                                {

                                    m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationRestPauseRIRForExerciseWithoutLoaderWithoutDeload(new GetRecommendationForExerciseModel()
                                    {
                                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                        ExerciseId = m.Id,
                                        IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false,
                                        LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                        WorkoutId = workoutId,
                                        SwapedExId = swapedExId
                                    });

                                }
                            }
                            if (m.RecoModel != null)
                            {
                                if (m.RecoModel.IsDeload)
                                    m.RecoModel.IsMaxChallenge = false;
                                m.RecoModel.IsLightSession = false;
                                if (m.RecoModel.Reps <= 0)
                                    m.RecoModel.Reps = 1;
                                if (m.RecoModel.NbRepsPauses <= 0)
                                    m.RecoModel.NbRepsPauses = 1;

                                RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                                LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + exId + setStyle, DateTime.Now.AddDays(1).ToString());


                            }
                        }
                        catch (Exception ex)
                        {

                        }

                    }

                }

            }
            catch (Exception ex)
            {

            }
        }

        private async void SaveWorkoutButton_Clicked(object sender, EventArgs e)
        {
            try
            {

                ConfirmConfig ShowConfirmPopUp = new ConfirmConfig()
                {
                    Title = $"Finish & save {CurrentLog.Instance.CurrentWorkoutTemplate.Label}?",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Finish & save",
                    CancelText = AppResources.Cancel,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            SavingExcercise();
                        }
                    }
                };
                UserDialogs.Instance.Confirm(ShowConfirmPopUp);

            }
            catch (Exception ex)
            {

            }
        }

        private async void SavingExcercise()
        {
            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
                    return;
                }
                try
                {
                    foreach (var item in exerciseItems)
                    {
                        LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{item.Id}", "false");
                    }
                }
                catch (Exception ex)
                {

                }
                try
                {
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                            LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                        }
                    }
                }
                catch (Exception ex)
                {

                }
                if (LocalDBManager.Instance.GetDBSetting("FinishWorkoutCounter") == null)
                    LocalDBManager.Instance.SetDBSetting("FinishWorkoutCounter", "1");
                if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("FinishWorkoutCounter").Value))
                {
                    var count = int.Parse(LocalDBManager.Instance.GetDBSetting("FinishWorkoutCounter").Value);
                    if (count > 5)
                    {
                        _firebase.LogEvent($"Finish workout {count}", $"{count}");
                        count += 1;
                        LocalDBManager.Instance.SetDBSetting("FinishWorkoutCounter", $"{count}");
                    }
                }
                foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                {
                    LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exerciceModel.Id}", "false");
                }

                try
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp == 999)
                    {
                        foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                        {
                            LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exerciceModel.Id}", "false");
                        }
                        await PagesFactory.PopToRootAsync();
                        return;
                    }
                }
                catch (Exception ex)
                {

                }
                var responseLog = await DrMuscleRestClient.Instance.SaveGetWorkoutInfo(new SaveWorkoutModel() { WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id });
                if (responseLog != null && responseLog.RecommendedProgram != null)
                {
                    if (responseLog.RecommendedProgram.Level != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("WorkoutLevel") != null && LocalDBManager.Instance.GetDBSetting("WorkoutLevel").Value != null)
                        {
                            if (responseLog.RecommendedProgram.Level > int.Parse(LocalDBManager.Instance.GetDBSetting("WorkoutLevel").Value))
                            {
                                if (responseLog.RecommendedProgram.Level == (int.Parse(LocalDBManager.Instance.GetDBSetting("WorkoutLevel").Value)) + 1)
                                {
                                    //Level messages
                                    if (responseLog.RecommendedProgram.Label.ToLower().Contains("bodyweight") && responseLog.RecommendedProgram.Level == 2)
                                    {
                                        MakePopup("Congratulations—you have reached level 2!", "Your program now includes new and harder exercises.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/building-muscle");
                                    }
                                    else if (responseLog.RecommendedProgram.Label.ToLower().Contains("bodyweight") && responseLog.RecommendedProgram.Level == 3)
                                    {
                                        MakePopup("Congratulations—you have reached level 3!", "Your program now includes new and harder exercises.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/building-muscle");
                                    }
                                    else if (responseLog.RecommendedProgram.Label.ToLower().Contains("bodyweight") && responseLog.RecommendedProgram.Level == 4)
                                    {
                                        MakePopup("Congratulations—you have reached level 4!", "Your program now includes new and harder exercises.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/building-muscle");
                                    }
                                    else if (responseLog.RecommendedProgram.Label.ToLower().Contains("buffed") && responseLog.RecommendedProgram.Level == 2)
                                    {
                                        MakePopup("Congratulations—you have reached level 2!", "Your program now includes new and harder exercises.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/building-muscle");
                                    }
                                    else if (responseLog.RecommendedProgram.Label.ToLower().Contains("buffed") && responseLog.RecommendedProgram.Level == 3)
                                    {
                                        MakePopup("Congratulations—you have reached level 3!", "Your program now includes new and harder exercises.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/building-muscle");
                                    }
                                    else if (responseLog.RecommendedProgram.Label.ToLower().Contains("buffed") && responseLog.RecommendedProgram.Level == 4)
                                    {
                                        MakePopup("Congratulations—you have reached level 4!", "Your program now includes new and harder exercises.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/building-muscle");
                                    }
                                    else if (responseLog.RecommendedProgram.Level == 2 && !responseLog.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                                    {
                                        MakePopup("Congratulations—you have reached level 2!", "Your program now includes A/B workouts with new exercises in rotation.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/build-muscle-faster/#3");
                                    }
                                    else if (responseLog.RecommendedProgram.Level == 3 && !responseLog.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                                    {
                                        MakePopup("Congratulations—you have reached level 3!", "Your program now includes A/B/C workouts with new exercises in rotation.", AppResources.GotIt, AppResources.LearnMore, true, "https://dr-muscle.com/build-muscle-faster/#3");
                                    }
                                    else if (responseLog.RecommendedProgram.Level == 4 && !responseLog.RecommendedProgram.Label.ToLower().Contains("bodyweight"))
                                    {
                                        MakePopup("Congratulations—you have reached level 4!", "Your program now includes easy workouts to help you recover.", AppResources.GotIt, AppResources.LearnMore, true, "http://dr-muscle.com/easy");
                                    }
                                    else if (responseLog.RecommendedProgram.Level == 5)
                                    {
                                        MakePopup("Congratulations—you have reached level 5!", "Your program now includes A/B easy workouts to help you recover.", AppResources.GotIt, AppResources.LearnMore, true, "http://dr-muscle.com/easy");
                                    }
                                    else if (responseLog.RecommendedProgram.Level == 6)
                                    {
                                        MakePopup("Congratulations—you have reached level 6!", "Your program now includes A/B/C easy workouts to help you recover.", AppResources.GotIt, AppResources.LearnMore, true, "http://dr-muscle.com/easy");
                                    }

                                    else if (responseLog.RecommendedProgram.Level == 7)
                                    {
                                        MakePopup("Congratulations—you have reached level 7!", "Your program now includes A/B/C medium workouts to prep you for new records on your normal workouts.", AppResources.GotIt, AppResources.LearnMore, true, "http://dr-muscle.com/easy");
                                    }
                                }
                            }
                        }
                        LocalDBManager.Instance.SetDBSetting("WorkoutLevel", responseLog.RecommendedProgram.Level.ToString());
                    }
                }
                LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
                LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());
                LocalDBManager.Instance.SetDBSetting("IsSystemWorkout", CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise == true ? "true" : "false");
                bool isSystem = CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise;
                foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                {
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(exerciceModel.Id))
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(exerciceModel.Id);

                    LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exerciceModel.Id}", "false");
                    try
                    {
                        bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == exerciceModel.Id);
                        if (isSwapped)
                        {
                            long targetExerciseId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == exerciceModel.Id).TargetExerciseId;

                            var obj = (Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == targetExerciseId);
                            if (obj != null)
                            {
                                LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{targetExerciseId}", "false");
                                (Application.Current as App)?.FinishedExercices.Remove(obj);
                            }
                        }
                    }
                    catch (Exception ex)
                    {

                    }

                }
                CurrentLog.Instance.CurrentWorkoutTemplate = null;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
                string fname = LocalDBManager.Instance.GetDBSetting("firstname").Value;
                string Msg = "";// $"{AppResources.Congratulations} {fname}!";
                try
                {
                    if (responseLog != null && responseLog.RecommendedProgram != null)
                    {

                        if (responseLog.RecommendedProgram.RemainingToLevelUp > 0)
                        {
                            //OLD
                            var workoutText = responseLog.RecommendedProgram.RemainingToLevelUp > 1 ? AppResources.WorkoutsFullStop : "workout.";
                            Msg += $"Well done! {AppResources.YouAre1WorkoutCloserToNewExercisesYourProgramWillLevelUpIn} {responseLog.RecommendedProgram.RemainingToLevelUp} {workoutText} I suggest at least 24 hours for recovery. You can take time off now :)";
                            //NEW
                            //Msg = $"Well done! I suggest at least 24 hours for recovery. You can take time off now :)";
                        }

                    }

                    Xamarin.Forms.MessagingCenter.Send<FinishWorkoutMessage>(new FinishWorkoutMessage() { PopupMessage = Msg }, "FinishWorkoutMessage");

                    await PagesFactory.PopToRootAsync();
                    if (App.Current.MainPage.Navigation.NavigationStack.First() is MainTabbedPage && Device.RuntimePlatform.Equals(Device.Android))
                        ((MainTabbedPage)App.Current.MainPage.Navigation.NavigationStack.First()).SelectedItem = ((MainTabbedPage)App.Current.MainPage.Navigation.NavigationStack.First()).Children[0];
                }
                catch (Exception ex)
                {
                    await PagesFactory.PopToRootAsync();
                }

            }
            catch (Exception ex)
            {

            }
        }

        private async void MakePopup(string title, string message, string cancelTitle, string OkTitle, bool isLink = false, string linkUrl = "")
        {
            ConfirmConfig supersetConfig = new ConfirmConfig()
            {
                Title = title,
                Message = message,
                OkText = OkTitle,
                CancelText = AppResources.GotIt,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (bool ok) =>
                {
                    if (ok)
                    {
                        if (isLink)
                            Device.OpenUri(new Uri(linkUrl));
                    }
                    else
                    {

                    }
                }


            };
            UserDialogs.Instance.Confirm(supersetConfig);
        }

        private async void PlateTapped(object sender, EventArgs e)
        {
            try
            {
                DependencyService.Get<IKeyboardHelper>().HideKeyboard();
            }
            catch (Exception ex)
            {

            }


            CurrentLog.Instance.CurrentWeight = App.PCWeight;

            var page = new PlateCalculatorPopup();
            await PopupNavigation.Instance.PushAsync(page);
            //Xamarin.Forms.MessagingCenter.Send<PlateCalulatorMessage>(new PlateCalulatorMessage(), "PlateCalulatorMessage");

        }
        private async void TimerTapped(object sender, EventArgs e)
        {
            SlideTimerAction();
        }

        private async void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (contextMenuStack != null)
                HideContextButton();

            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                ExerciseListView.BeginRefresh();
                ExerciseListView.EndRefresh();
            }
            try
            {

                if (ExerciseListView.SelectedItem == null)
                    return;

                Device.BeginInvokeOnMainThread(() => {
                    WorkoutLogSerieModelRef workout = (WorkoutLogSerieModelRef)e.Item;
                    if (workout != null)
                    {
                        if (!workout.IsFinished)
                        {
                            if (ExerciseListView.SelectedItem != null)
                                ExerciseListView.SelectedItem = null;
                            return;
                        }
                        workout.IsFinished = true;
                        workout.IsEditing = true;
                        workout.IsNext = true;

                        if (Device.RuntimePlatform.Equals(Device.iOS))
                        {
                            workout.IsSizeChanged = !workout.IsSizeChanged;
                            ExerciseListView.ItemsSource = exerciseItems;
                        }
                        else
                            ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                    }
                });
                if (ExerciseListView.SelectedItem != null)
                    ExerciseListView.SelectedItem = null;

            }
            catch (Exception ex)
            {

            }

        }
        private async void PicktorialTapped(object sender, EventArgs e)
        {
            try
            {

                if (contextMenuStack != null)
                    HideContextButton();
                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;
                if (!string.IsNullOrEmpty(m.VideoUrl))
                {
                    CurrentLog.Instance.VideoExercise = GetExerciseModel(m);
                    await PagesFactory.PushAsync<ExerciseVideoPage>();
                }

            }
            catch (Exception ex)
            {

            }
            // OnCancelClicked(sender, e);
        }

        private async void CellHeaderTapped(object sender, EventArgs e)
        {
            try
            {
                IsAnimate = false;
                if (!CrossConnectivity.Current.IsConnected)
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
                    return;
                }
                if (contextMenuStack != null)
                    HideContextButton();

                var currentOpenExer = exerciseItems.Where(x => x.IsNextExercise).FirstOrDefault();

                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;
                if (m.Id == 0)
                    return;
                try
                {

                    string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                    LocalDBManager.Instance.SetDBReco("RReps" + m.Id + setStyle + "challenge", $"");

                }
                catch (Exception ex)
                {

                }

                if (m.IsNextExercise && m.Count > 0)
                {
                    m.Clear();
                    m.IsNextExercise = false;
                    return;
                }
                m.IsNextExercise = true;// !m.IsNextExercise;

                if (m.IsFinished)
                {
                    try
                    {
                        if (!CrossConnectivity.Current.IsConnected)
                        {
                            await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
                            return;
                        }
                        List<WorkoutLogSerieModelRef> setList = new List<WorkoutLogSerieModelRef>();

                        List<HistoryModel> historyModel = await DrMuscleRestClient.Instance.GetLastExerciseHistory(m.Id);
                        if (historyModel.Count > 0)
                        {
                            var logSerie = historyModel.First().Exercises[0].Sets.OrderBy(x => x.LogDate).ToList();
                            var warmupList = logSerie.Where(l => l.IsWarmups).ToList();
                            for (int i = 0; i < warmupList.Count; i++)
                            {
                                setList.Add(new WorkoutLogSerieModelRef()
                                {
                                    Weight = warmupList[i].Weight,
                                    IsWarmups = warmupList[i].IsWarmups,
                                    Reps = warmupList[i].Reps,
                                    SetNo = $"SET {setList.Count + 1}",
                                    IsLastWarmupSet = i == warmupList.Where(l => l.IsWarmups).ToList().Count - 1 ? true : false,
                                    IsHeaderCell = i == 0 ? true : false,
                                    HeaderImage = "",
                                    HeaderTitle = "",
                                    ExerciseName = m.Label,
                                    SetTitle = i == 0 ? "Let's warm up:" : "",
                                    IsFinished = true,
                                    IsExerciseFinished = true,
                                    Id = logSerie[i].Id,
                                    IsTimeBased = m.IsTimeBased,
                                    IsBodyweight = m.IsBodyweight,
                                    IsNormalset = m.IsNormalSets

                                });

                            }
                            if (setList.Count > 1)
                            {
                                setList.Last().SetTitle = "Last warm-up set:";
                            }

                            var worksetsList = logSerie.Where(l => l.IsWarmups == false).ToList();
                            for (int j = 0; j < worksetsList.Count; j++)
                            {

                                var rec = new WorkoutLogSerieModelRef()
                                {
                                    Weight = worksetsList[j].Weight,
                                    IsWarmups = worksetsList[j].IsWarmups,
                                    Reps = worksetsList[j].Reps,
                                    SetNo = $"SET {setList.Count + 1}",
                                    ExerciseName = m.Label,
                                    IsFirstWorkSet = j == 0 ? true : false,
                                    SetTitle = j == 0 ? "1st work set—you got this:" : "All right! Now let's try:",
                                    IsFinished = true,
                                    IsExerciseFinished = true,
                                    Id = worksetsList[j].Id,
                                    IsTimeBased = m.IsTimeBased,
                                    IsBodyweight = m.IsBodyweight,
                                    IsNormalset = m.IsNormalSets
                                };
                                if (setList.Count == 0)
                                {
                                    rec.IsHeaderCell = true;
                                    rec.HeaderImage = "";
                                    rec.HeaderTitle = "";
                                }
                                setList.Add(rec);
                            }


                            if ((setList.Count - logSerie.Where(l => l.IsWarmups).ToList().Count) > 3)
                            {
                                setList[setList.Count - 2].SetTitle = "Almost done—keep it up!";
                                setList.Last().SetTitle = "Last set—you can do this!";
                            }
                            else if ((setList.Count - logSerie.Where(l => l.IsWarmups).ToList().Count) > 2)
                            {
                                setList.Last().SetTitle = "Last set—you can do this!";
                            }

                            if (setList.First().IsWarmups)
                            {
                                var warmString = setList.Where(l => l.IsWarmups).ToList().Count < 2 ? "warm-up" : "warm-ups";
                                setList.First().SetTitle = $"{setList.Where(l => l.IsWarmups).ToList().Count} {warmString}, {setList.Where(l => !l.IsWarmups).ToList().Count} work sets\nLet's warm up:";
                            }
                            if (setList.Count > 0)
                            {
                                setList.Last().IsLastSet = true;
                                //if (m.IsFirstSide)
                                //    setList.Last().IsFirstSide = true;
                            }
                            for (var i = 0; i < setList.Count; i++)
                                ((WorkoutLogSerieModelRef)setList[i]).SetNo = $"SET {i + 1}/{setList.Count}";
                            foreach (var item in setList)
                            {
                                m.Add(item);
                            }
                            ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                            {
                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[m.Id] = new ObservableCollection<WorkoutLogSerieModelRef>(setList);
                            }
                            else
                            {
                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Add(m.Id, new ObservableCollection<WorkoutLogSerieModelRef>(setList));
                            }
                            ScrollToSnap(setList, m);

                        }

                    }
                    catch (Exception ex)
                    {

                    }

                    return;
                }

                if (m.RecoModel != null)
                {
                    FetchReco(m, null);
                    return;
                }
                NewExerciseLogResponseModel newExercise = await DrMuscleRestClient.Instance.IsNewExerciseWithSessionInfo(new ExerciceModel() { Id = m.Id });
                if (newExercise != null)
                {
                    if (!newExercise.IsNewExercise)
                    {
                        try
                        {
                            DateTime? lastLogDate = newExercise.LastLogDate;
                            int? sessionDays = null;


                            string WeightRecommandation;
                            RecommendationModel reco = null;

                            //Todo: clean up on 2019 01 18
                            if (LocalDBManager.Instance.GetDBSetting("SetStyle") == null)
                                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                            if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
                                LocalDBManager.Instance.SetDBSetting("QuickMode", "false");
                            var bodyPartname = "";


                            switch (m.BodyPartId)
                            {
                                case 1:
                                    break;
                                case 2:
                                    bodyPartname = "Shoulders";
                                    break;
                                case 3:
                                    bodyPartname = "Chest";
                                    break;
                                case 4:
                                    bodyPartname = "Back";
                                    break;
                                case 5:
                                    bodyPartname = "Biceps";
                                    break;
                                case 6:
                                    bodyPartname = "Triceps";
                                    break;
                                case 7:
                                    bodyPartname = "Abs";
                                    break;
                                case 8:
                                    bodyPartname = "Legs";
                                    break;
                                case 9:
                                    bodyPartname = "Calves";
                                    break;
                                case 10:
                                    bodyPartname = "Neck";
                                    break;
                                case 11:
                                    bodyPartname = "Forearm";
                                    break;
                                default:
                                    //
                                    break;
                            }

                            if (lastLogDate != null)
                            {
                                var days = (int)(DateTime.Now - (DateTime)lastLogDate).TotalDays;
                                if (days >= 5 && days <= 9)
                                    sessionDays = days;
                                if (days > 9)
                                {
                                    ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                                    {
                                        Title = "Light session?",
                                        Message = string.IsNullOrEmpty(bodyPartname) == false ? $"The last time you trained {bodyPartname.ToLower()} was {days} {AppResources.DaysAgoDoALightSessionToRecover}" : string.Format("{0} {1} {2} {3} {4}", "The last time you trained", m.Label, AppResources.was, days, AppResources.DaysAgoDoALightSessionToRecover),
                                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                        OkText = AppResources.LightSession,
                                        CancelText = AppResources.Cancel,
                                    };
                                    try
                                    {
                                        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                                        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                                    }
                                    catch (Exception ex)
                                    {

                                    }

                                    CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                                    CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);

                                    var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                                    if (isConfirm)
                                    {
                                        if (days > 50)
                                            days = 50;
                                        sessionDays = days;
                                        App.WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
                                        App.BodypartId = (int)CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId;
                                        App.Days = days;
                                        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                                            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                                        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                                        {
                                            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                                            m.RecoModel = null;
                                        }
                                        //reco.Weight = new MultiUnityWeight(reco.Weight.Kg - ((reco.Weight.Kg * (decimal)1.5 * days) / 100), "kg");
                                        //reco.WarmUpWeightSet1 = new MultiUnityWeight(reco.WarmUpWeightSet1.Kg - ((reco.WarmUpWeightSet1.Kg * (decimal)1.5 * days) / 100), "kg");
                                        //reco.WarmUpWeightSet2 = new MultiUnityWeight(reco.WarmUpWeightSet2.Kg - ((reco.WarmUpWeightSet2.Kg * (decimal)1.5 * days) / 100), "kg");
                                    }
                                    else
                                    {
                                        sessionDays = null;
                                        App.Days = 0;
                                        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                                            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                                        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                                        {
                                            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                                            m.RecoModel = null;
                                        }
                                    }
                                }
                            }
                            try
                            {
                                if (App.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && App.BodypartId == (int)m.BodyPartId && App.Days > 9)
                                {
                                    sessionDays = App.Days;
                                }

                            }
                            catch (Exception ex)
                            {

                            }

                            await FetchReco(m, sessionDays);
                        }
                        catch (Exception ex)
                        {

                        }

                    }
                    else
                    {
                        if (m.Id != 0)
                            RunExercise(m);
                    }
                }
                else
                    await FetchReco(m);

            }
            catch (Exception ex)
            {

            }
        }

        private async Task FetchReco(ExerciseWorkSetsModel m, int? sessionDays = null)
        {
            try
            {
                if (m.IsNextExercise)
                {
                    long? workoutId = null;
                    try
                    {
                        //if (!CurrentLog.Instance.IsFromExercise)
                        workoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
                    }
                    catch (Exception)
                    {

                    }

                    if (m.Count > 0)
                    {
                        m.Clear();
                        return;
                    }

                    string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;

                    string exId = $"{m.Id}";
                    var lastTime = LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle);


                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                    {
                        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                    }
                    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                    {
                        var sets = CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[m.Id];


                        if (m.RecoModel == null)
                        {
                            if (lastTime != null)
                            {
                                try
                                {
                                    if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value))
                                    {
                                        var LastRecoPlus1Day = Convert.ToDateTime(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value);
                                        if (LastRecoPlus1Day > DateTime.Now)
                                        {
                                            var recommendation = RecoContext.GetReco("Reco" + exId + setStyle);
                                            if (recommendation != null)
                                            {
                                                m.RecoModel = recommendation;
                                                m.RecoModel.IsDeload = false;
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"Exception is:{ex.ToString()}");
                                }
                            }
                        }

                        if (m.RecoModel != null)
                        {
                            m.IsPyramid = m.RecoModel.IsPyramid;
                            if (CurrentLog.Instance.ExerciseLog == null)
                            {
                                CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                            }
                            CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);
                            if (CurrentLog.Instance.RecommendationsByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                                CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id] = m.RecoModel;
                            else
                                CurrentLog.Instance.RecommendationsByExercise.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, m.RecoModel);

                        }
                        foreach (var cacheSet in sets)
                        {
                            m.Add(cacheSet);
                        }
                        if (m.IsUnilateral && sets.Where(x => x.IsFinished).FirstOrDefault() == null)
                        {
                            m.IsFirstSide = true;

                            AlertConfig ShowExplainRIRPopUp = new AlertConfig()
                            {
                                Title = "Side 1",
                                Message = "Do all sets for side 1.",
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = "Side 1",
                            };
                            await Task.Factory.StartNew(() => {
                                Device.BeginInvokeOnMainThread(() =>
                                {
                                    UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
                                });
                            });

                        }
                        if (Device.RuntimePlatform.Equals(Device.iOS))
                            ExerciseListView.ScrollTo(sets.First(), ScrollToPosition.Start, true);
                        await Task.Delay(300);

                        //ExerciseListView.ItemPosition = exerciseItems.IndexOf(m);
                        //ExerciseListView.IsScrolled = !ExerciseListView.IsScrolled;

                        //Scroll To position

                        if (m.RecoModel != null)
                        {
                            ScrollToSnap(sets.ToList(), m);
                            return;
                        }
                    }


                    if (lastTime != null)
                    {
                        try
                        {
                            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value))
                            {
                                var LastRecoPlus1Day = Convert.ToDateTime(LocalDBManager.Instance.GetDBReco("NbRepsGeneratedTime" + exId + setStyle).Value);
                                if (LastRecoPlus1Day > DateTime.Now)
                                {
                                    var recommendation = RecoContext.GetReco("Reco" + exId + setStyle);
                                    if (recommendation != null)
                                    {
                                        m.RecoModel = recommendation;
                                        m.RecoModel.IsDeload = false;


                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Exception is:{ex.ToString()}");
                        }
                    }
                    long? swapedExId = null;
                    try
                    {
                        if (m.IsSwapTarget)
                        {
                            bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.TargetExerciseId == m.Id);
                            swapedExId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.TargetExerciseId == m.Id).SourceExerciseId;
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                    if (LocalDBManager.Instance.GetDBSetting("IsPyramid") == null)
                        LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                    bool? isQuick = false;
                    if (LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "null")
                        isQuick = null;
                    else
                        isQuick = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false;
                    if (m.RecoModel == null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("SetStyle").Value == "Normal" || m.Id == 16508)
                        {
                            if (LocalDBManager.Instance.GetDBSetting("IsPyramid").Value == "true" && m.IsBodyweight && m.Id != 16508)
                            {
                                m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationRestPauseRIRForExerciseWithoutDeload(new GetRecommendationForExerciseModel()
                                {
                                    Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                    ExerciseId = m.Id,
                                    IsQuickMode = isQuick,
                                    LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                    WorkoutId = workoutId,
                                    SwapedExId = swapedExId
                                });
                            }
                            else
                            {
                                m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationNormalRIRForExerciseWithoutDeload(new GetRecommendationForExerciseModel()
                                {
                                    Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                    ExerciseId = m.Id,
                                    IsQuickMode = isQuick,
                                    LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                    WorkoutId = workoutId,
                                    SwapedExId = swapedExId
                                });
                            }
                        }
                        else
                        {

                            m.RecoModel = await DrMuscleRestClient.Instance.GetRecommendationRestPauseRIRForExerciseWithoutDeload(new GetRecommendationForExerciseModel()
                            {
                                Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                                ExerciseId = m.Id,
                                IsQuickMode = isQuick,
                                LightSessionDays = sessionDays != null ? sessionDays > 9 ? sessionDays : null : null,
                                WorkoutId = workoutId,
                                SwapedExId = swapedExId
                            });

                        }
                    }
                    if (m.RecoModel != null)
                    {
                        RecoComputation.ComputeWarmups(m.RecoModel, m.Id,m);
                        m.RecoModel.IsLightSession = sessionDays == null ? false : sessionDays > 9 ? true : false;

                        if (m.RecoModel.IsDeload)
                            m.RecoModel.IsMaxChallenge = false;
                        if (m.IsBodyweight)
                            m.RecoModel.IsPyramid = false;
                        if (m.RecoModel.IsPyramid)
                        {
                            if ((m.RecoModel.Increments != null && m.RecoModel.Increments.Kg >= m.RecoModel.Weight.Kg) || (m.RecoModel.Increments == null && m.RecoModel.Weight.Kg <= 2) || (m.RecoModel.Min != null && m.RecoModel.Min.Kg >= m.RecoModel.Weight.Kg))
                            {
                                m.RecoModel.IsPyramid = false;
                                m.IsPyramid = false;
                                if (m.RecoModel.IsNormalSets)
                                {
                                    m.IsNormalSets = false;
                                    m.RecoModel.IsNormalSets = false;
                                    m.RecoModel.NbPauses = m.RecoModel.Series - 1;
                                    m.RecoModel.Series = 1;
                                    m.RecoModel.NbRepsPauses = m.RecoModel.Reps <= 5 ? (int)Math.Ceiling((decimal)m.RecoModel.Reps / (decimal)3) : (int)m.RecoModel.Reps / 3;
                                }
                            }
                            else
                            {

                                m.IsPyramid = true;
                                if (m.RecoModel.IsNormalSets == true)
                                {
                                    if (m.RecoModel.Series < 3)
                                        m.RecoModel.Series = 3;
                                }
                                else
                                {
                                    m.RecoModel.NbPauses = m.RecoModel.NbPauses + m.RecoModel.Series;
                                    m.RecoModel.Series = 0;
                                    if (m.RecoModel.NbPauses < 3)
                                        m.RecoModel.NbPauses = 3;
                                }

                            }
                        }
                        if (m.RecoModel.Reps <= 0)
                            m.RecoModel.Reps = 1;
                        if (m.RecoModel.NbRepsPauses <= 0)
                            m.RecoModel.NbRepsPauses = 1;

                        RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + exId + setStyle, DateTime.Now.AddDays(1).ToString());
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                        {
                            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                            {
                                m.RecoModel.IsDeload = true;
                                m.RecoModel.RecommendationInKg = m.RecoModel.Weight.Kg - (m.RecoModel.Weight.Kg * (decimal)0.1);
                                //if (m.RecoModel.IsNormalSets)
                                //{
                                //    m.RecoModel = RecoComputation.GetNormalDeload(m.RecoModel);
                                //}
                                //else
                                //{
                                //    m.RecoModel = RecoComputation.GetRestPauseDeload(m.RecoModel);
                                //}
                                m.RecoModel.IsLightSession = true;
                                m.RecoModel.IsMaxChallenge = false;
                            }
                        }
                        if (m.RecoModel.IsDeload && !m.RecoModel.IsLightSession)
                        {
                            var per = string.Format("{0:0.00}%", m.RecoModel.OneRMPercentage * 100);
                            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                            {
                                Title = "Deload?",
                                Message = $"Your 1RM has gone down {per} last time you did {m.Label.ToLower()}. Deload to recover?",
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = "Deload",
                                CancelText = AppResources.Cancel,
                            };

                            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                            if (isConfirm)
                            {
                                LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");
                                //Create new Reco for deload
                                //if (m.RecoModel.IsNormalSets)
                                //{
                                //    m.RecoModel = RecoComputation.GetNormalDeload(m.RecoModel);
                                //}
                                //else
                                //{
                                //    m.RecoModel = RecoComputation.GetRestPauseDeload(m.RecoModel);
                                //}
                            }
                            else
                            {
                                m.RecoModel.IsDeload = false;
                                RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                            }
                        }

                        m.IsNormalSets = m.RecoModel.IsNormalSets;

                        string lbl3text = "";
                        string iconOrange = "";

                        lbl3text = "";

                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                        {
                            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                            {
                                m.RecoModel.Reps = (int)Math.Ceiling((decimal)m.RecoModel.Reps + ((decimal)m.RecoModel.Reps * (decimal)0.1));
                                m.RecoModel.NbRepsPauses = (int)Math.Ceiling((decimal)m.RecoModel.NbRepsPauses + ((decimal)m.RecoModel.NbRepsPauses * (decimal)0.1));
                                m.RecoModel.IsMaxChallenge = false;
                            }
                        }


                        try
                        {


                            if (LocalDBManager.Instance.GetDBSetting($"{CurrentLog.Instance.CurrentWorkoutTemplate.Id}Challenge{m.BodyPartId}") != null && LocalDBManager.Instance.GetDBSetting($"{CurrentLog.Instance.CurrentWorkoutTemplate.Id}Challenge{m.BodyPartId}").Value == $"{m.BodyPartId}")
                            {
                                m.RecoModel.IsMaxChallenge = false;
                            }

                        }
                        catch (Exception ex)
                        {

                        }
                        if (m.RecoModel.IsMaxChallenge)
                        {
                            ConfirmConfig supersetConfig = new ConfirmConfig()
                            {
                                Title = "Challenge time!",
                                Message = $"Are you ready to set a new record on your {m.Label}?",
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = AppResources.GiveMeAChallenge,
                                CancelText = AppResources.Cancel,
                            };

                            bool isMaxChallenge = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
                            if (isMaxChallenge)
                            {
                                LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                                //LocalDBManager.Instance.SetDBSetting("RReps" + exId + setStyle, $"{reco.Reps}");
                                try
                                {

                                    m.RecoModel.Reps = (int)Math.Ceiling((decimal)m.RecoModel.Reps + ((decimal)m.RecoModel.Reps * (decimal)0.1));
                                    m.RecoModel.NbRepsPauses = (int)Math.Ceiling((decimal)m.RecoModel.NbRepsPauses + ((decimal)m.RecoModel.NbRepsPauses * (decimal)0.1));


                                    LocalDBManager.Instance.SetDBSetting($"{CurrentLog.Instance.CurrentWorkoutTemplate.Id}Challenge{m.BodyPartId}", $"{m.BodyPartId}");
                                }
                                catch (Exception ex)
                                {

                                }
                                _firebase.LogEvent("challenge_time", "Yes");
                            }
                            else
                            {
                                m.RecoModel.IsMaxChallenge = false;
                                RecoContext.SaveContexts("Reco" + exId + setStyle, m.RecoModel);
                                LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                                if (LocalDBManager.Instance.GetDBSetting("timer_autoset") == null)
                                    LocalDBManager.Instance.SetDBSetting("timer_autoset", "true");
                                //if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                                //    LocalDBManager.Instance.SetDBSetting("timer_remaining", CurrentLog.Instance.GetRecommendationRestTime(m.Id, false, CurrentLog.Instance.RecommendationsByExercise[m.Id].IsNormalSets).ToString());
                                // _firebase.LogEvent("challenge_time", "No");
                            }

                        }
                        if (sessionDays != null)
                        {

                            if (sessionDays >= 5 && sessionDays <= 9)
                            {
                                var bodyPartname = m.BodyPartId == 1 ? "" : Constants.AppThemeConstants.GetBodyPartName(m.BodyPartId);
                                ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                                {
                                    Message = string.Format("{0} {1} {2} {3} {4}", AppResources.TheLastTimeYouDid, string.IsNullOrEmpty(bodyPartname) == false ? bodyPartname : m.Label, AppResources.was, sessionDays, AppResources.DaysAgoYouShouldBeFullyRecoveredDoExtraSet),
                                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                    OkText = "+1 set",
                                    CancelText = AppResources.Cancel,
                                };

                                var isAddMoreSet = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                                if (isAddMoreSet)
                                {
                                    if (m.RecoModel.NbPauses == 0)
                                        m.RecoModel.Series += 1;
                                    else
                                        m.RecoModel.NbPauses += 1;
                                }
                            }
                        }
                        if (CurrentLog.Instance.ExerciseLog == null)
                        {
                            CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                        }
                        CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);
                        if (CurrentLog.Instance.RecommendationsByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                            CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id] = m.RecoModel;
                        else
                            CurrentLog.Instance.RecommendationsByExercise.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, m.RecoModel);

                        if (sessionDays > 9)
                        {
                            lbl3text = "This a light session";
                            iconOrange = "orange.png";
                        }
                        else
                            iconOrange = "";

                        if (m.RecoModel.IsDeload)
                        {
                            LocalDBManager.Instance.SetDBSetting("RecoDeload", "true");
                            lbl3text = AppResources.AttentionTodayIsADeload;
                            iconOrange = "orange.png";
                        }
                        else if (m.RecoModel.IsMaxChallenge)
                        {
                            LocalDBManager.Instance.SetDBSetting("RecoDeload", "false");
                            lbl3text = "Today is a challenge";
                            iconOrange = "done2.png";
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting("RecoDeload", "false");
                            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                            {
                                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                                {
                                    lbl3text = "Today is a challenge";
                                    iconOrange = "done2.png";
                                }
                            }
                        }
                        if (m.Count > 0)
                        {
                            m.Clear();
                            return;
                        }
                        List<WorkoutLogSerieModelRef> setList = new List<WorkoutLogSerieModelRef>();


                        if (m.IsUnilateral)
                            m.IsFirstSide = true;
                        if (m.RecoModel.WarmUpsList.Count > 0)
                        {
                            for (int i = 0; i < m.RecoModel.WarmUpsList.Count; i++)
                            {

                                setList.Add(new WorkoutLogSerieModelRef()
                                {
                                    Id = m.Id,
                                    Weight = m.RecoModel.WarmUpsList[i].WarmUpWeightSet.Kg < 1 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)1 * (m.RecoModel.Increments == null ? 1 : m.RecoModel.Increments.Kg), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg), "kg") : m.RecoModel.WarmUpsList[i].WarmUpWeightSet,
                                    IsWarmups = true,
                                    Reps = m.RecoModel.WarmUpsList[i].WarmUpReps,
                                    SetNo = $"SET {setList.Count + 1}",
                                    IsLastWarmupSet = i == m.RecoModel.WarmUpsList.Count - 1 ? true : false,
                                    IsHeaderCell = i == 0 ? true : false,
                                    HeaderImage = iconOrange,
                                    HeaderTitle = lbl3text,
                                    ExerciseName = m.Label,
                                    Increments = m.RecoModel.Increments,
                                    SetTitle = i == 0 ? "Let's warm up:" : "",
                                    IsTimeBased = m.IsTimeBased,
                                    IsUnilateral = m.IsUnilateral,
                                    IsBodyweight = m.IsBodyweight,
                                    IsNormalset = m.IsNormalSets


                                }); ;

                            }
                            if (setList.Count > 1)
                            {
                                setList.Last().SetTitle = "Last warm-up set:";
                            }
                        }
                        bool isMarkFirstSet = false;
                        for (int j = 0; j < m.RecoModel.Series; j++)
                        {

                            isMarkFirstSet = true;
                            var rec = new WorkoutLogSerieModelRef()
                            {
                                Id = m.Id,
                                Weight = m.RecoModel.Weight.Kg < 1 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)1 * (m.RecoModel.Increments == null ? 1 : m.RecoModel.Increments.Kg), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg), "kg") : m.RecoModel.Weight,
                                IsWarmups = false,
                                Reps = m.RecoModel.Reps,
                                SetNo = $"SET {setList.Count + 1}",
                                ExerciseName = m.Label,
                                IsFirstWorkSet = j == 0 ? true : false,
                                Increments = m.RecoModel.Increments,
                                SetTitle = j == 0 ? "1st work set—You got this:" : "All right! Now let's try:",
                                IsTimeBased = m.IsTimeBased,
                                IsUnilateral = m.IsUnilateral,
                                IsBodyweight = m.IsBodyweight,
                                IsNormalset = m.IsNormalSets
                            };
                            if (j == 0 && m.RecoModel.FirstWorkSetWeight != null)
                            {
                                var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                                var worksets = string.Format("{0} {1}", Math.Round(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : m.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                                var days = 0;
                                if (m.RecoModel.LastLogDate != null)
                                    days = (DateTime.Now - ((DateTime)m.RecoModel.LastLogDate).ToLocalTime()).Days;
                                var dayString = days == 0 ? "Today" : days == 1 ? "1 day ago" : $"{days} days ago";
                                if (m.RecoModel.IsBodyweight)
                                    worksets = "body";

                                //rec.SetTitle = $"{dayString}: {m.RecoModel.FirstWorkSetReps} x {worksets}—let's try:";
                                var lastOneRM = ComputeOneRM(m.RecoModel.FirstWorkSetWeight.Kg, m.RecoModel.FirstWorkSetReps);
                                var currentRM = ComputeOneRM(m.RecoModel.Weight.Kg, rec.Reps);
                                if (currentRM != 0)
                                {
                                    var percentage = (currentRM - lastOneRM) * 100 / currentRM;
                                    rec.SetTitle = string.Format("Last time: {0} x {1}\nFor {2}{3:0.00}%, do:", m.RecoModel.FirstWorkSetReps, worksets, percentage >= 0 ? "+" : "", percentage);
                                }
                            }
                            if (j > 0 && m.RecoModel.IsPyramid)
                            {
                                rec.Reps = setList.Last().Reps + j + 1;
                                decimal weight = RecoComputation.RoundToNearestIncrement(rec.Weight.Kg - (rec.Weight.Kg * ((decimal)j * (decimal)0.1)), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                rec.Weight = new MultiUnityWeight(weight, "kg");
                            }
                            if (setList.Count == 0)
                            {
                                rec.IsHeaderCell = true;
                                rec.HeaderImage = iconOrange;
                                rec.HeaderTitle = lbl3text;
                            }
                            setList.Add(rec);
                        }

                        for (int j = 0; j < m.RecoModel.NbPauses; j++)
                        {

                            var rec = new WorkoutLogSerieModelRef()
                            {
                                Id = m.Id,
                                Weight = m.RecoModel.Weight.Kg < 1 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)1 * (m.RecoModel.Increments == null ? 1 : m.RecoModel.Increments.Kg), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg), "kg") : m.RecoModel.Weight,
                                IsWarmups = false,
                                Reps = m.RecoModel.NbRepsPauses,
                                SetNo = $"SET {setList.Count + 1}",
                                ExerciseName = m.Label,
                                Increments = m.RecoModel.Increments,
                                SetTitle = "All right! Now let's try:",
                                IsTimeBased = m.IsTimeBased,
                                IsUnilateral = m.IsUnilateral,
                                IsBodyweight = m.IsBodyweight,
                                IsNormalset = m.IsNormalSets

                            };
                            if (!isMarkFirstSet && j == 0)
                            {
                                rec.IsFirstWorkSet = true;
                                rec.SetTitle = "1st work set—you got this:";
                            }

                            if (!isMarkFirstSet && j == 0 && m.RecoModel.FirstWorkSetWeight != null)
                            {
                                var isKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? true : false;
                                var worksets = string.Format("{0} {1}", Math.Round(isKg ? m.RecoModel.FirstWorkSetWeight.Kg : m.RecoModel.FirstWorkSetWeight.Lb, 2), isKg ? "kg" : "lbs");
                                var days = 0;
                                if (m.RecoModel.LastLogDate != null)
                                    days = (DateTime.Now - ((DateTime)m.RecoModel.LastLogDate).ToLocalTime()).Days;
                                var dayString = days == 0 ? "Today" : days == 1 ? "1 day ago" : $"{days} days ago";
                                if (m.RecoModel.IsBodyweight)
                                    worksets = "body";
                                rec.SetTitle = $"{dayString}: {m.RecoModel.FirstWorkSetReps} x {worksets}\nLet's try:";
                                //Last time: 7 x 150 lbs\nFor +2.1%, do:
                                var lastOneRM = ComputeOneRM(m.RecoModel.FirstWorkSetWeight.Kg, m.RecoModel.FirstWorkSetReps);
                                var currentRM = ComputeOneRM(m.RecoModel.Weight.Kg, rec.Reps);
                                if (currentRM != 0)
                                {
                                    var percentage = (currentRM - lastOneRM) * 100 / currentRM;
                                    rec.SetTitle = string.Format("Last time: {0} x {1}\nFor {2}{3:0.00}%, do:", m.RecoModel.FirstWorkSetReps, worksets, percentage >= 0 ? "+" : "", percentage);
                                }
                            }
                            if (j > 0 && m.RecoModel.IsPyramid)
                            {
                                rec.Reps = setList.Last().Reps + j + 1;
                                decimal weight = RecoComputation.RoundToNearestIncrement(rec.Weight.Kg - (rec.Weight.Kg * ((decimal)j * (decimal)0.1)), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg);
                                rec.Weight = new MultiUnityWeight(weight, "kg");
                            }
                            if (setList.Count == 0)
                            {
                                rec.IsHeaderCell = true;
                                rec.HeaderImage = iconOrange;
                                rec.HeaderTitle = lbl3text;
                            }
                            setList.Add(rec);
                        }
                        var worksetcount = (setList.Count - m.RecoModel.WarmUpsList.Count);
                        if (worksetcount > 2 && !m.IsBodyweight)
                        {
                            if (m.RecoModel.BackOffSetWeight != null && !m.RecoModel.IsPyramid)
                            {
                                if (Math.Abs(m.RecoModel.Weight.Kg - m.RecoModel.BackOffSetWeight.Kg) > 0)
                                {
                                    var ob = ((Math.Abs(m.RecoModel.Weight.Kg - m.RecoModel.BackOffSetWeight.Kg) / m.RecoModel.Weight.Kg) > (decimal)0.3 ? (decimal)0.3 * (decimal)3.66 : Math.Abs(m.RecoModel.Weight.Kg - m.RecoModel.BackOffSetWeight.Kg) / m.RecoModel.Weight.Kg * (decimal)3.66);
                                    setList.Last().Reps = (int)setList.Last().Reps + (int)Math.Ceiling(setList.Last().Reps * ob);
                                }
                                else
                                {
                                    setList.Last().Reps = (int)(setList.Last().Reps + Math.Ceiling(setList.Last().Reps * 0.2));
                                }
                                //setList.Last().Reps = setList.Last().Reps = (int)(setList.Last().Reps + Math.Ceiling(setList.Last().Reps * 1.1)); ;
                                setList.Last().IsBackOffSet = true;
                                setList.Last().Weight = m.RecoModel.BackOffSetWeight.Kg < 1 ? new MultiUnityWeight(RecoComputation.RoundToNearestIncrement((decimal)1 * (m.RecoModel.Increments == null ? 1 : m.RecoModel.Increments.Kg), m.RecoModel.Increments == null ? (decimal)1.0 : m.RecoModel.Increments.Kg, m.RecoModel.Min?.Kg, m.RecoModel.Max?.Kg), "kg") : m.RecoModel.BackOffSetWeight;
                                setList[setList.Count - 2].IsNextBackOffSet = true;
                            }
                        }
                        if (worksetcount > 3)
                        {
                            setList[setList.Count - 2].SetTitle = "Almost done—keep it up!";
                            setList.Last().SetTitle = "Last set—you can do this!";
                        }
                        else if (worksetcount > 2)
                        {
                            setList.Last().SetTitle = "Last set—you can do this!";
                        }
                        if (setList.First().IsWarmups)
                        {
                            var warmString = setList.Where(l => l.IsWarmups).ToList().Count < 2 ? "warm-up" : "warm-ups";
                            setList.First().SetTitle = $"{setList.Where(l => l.IsWarmups).ToList().Count} {warmString}, {setList.Where(l => !l.IsWarmups).ToList().Count} work sets\nLet's warm up:";
                        }
                        var selected = setList.Where(x => x.IsNext == true).FirstOrDefault();

                        if (selected == null && setList.Count > 0)
                            setList.First().IsNext = true;
                        else
                        {
                            //Get index and set
                        }
                        if (setList.Count > 0)
                        {
                            setList.Last().IsLastSet = true;
                            if (m.IsFirstSide)
                                setList.Last().IsFirstSide = true;
                        }
                        for (var i = 0; i < setList.Count; i++)
                            ((WorkoutLogSerieModelRef)setList[i]).SetNo = $"SET {i + 1}/{setList.Count}";
                        foreach (var item in setList)
                        {
                            m.Add(item);
                        }
                        ExerciseListView.IsCellUpdated = !ExerciseListView.IsCellUpdated;
                        if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                        {
                            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef[m.Id] = new ObservableCollection<WorkoutLogSerieModelRef>(setList);
                        }
                        else
                        {
                            CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Add(m.Id, new ObservableCollection<WorkoutLogSerieModelRef>(setList));
                        }
                        //lblResult4.Text = string.Format("Do {0} {1} for {2} sets of {3} reps ({4} rest)", WeightRecommandation, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", reco.Series, reco.Reps, restTime);

                        if (m.RecoModel.IsDeload)
                        {
                            LocalDBManager.Instance.SetDBSetting("RecoDeload", "true");
                        }
                        else
                            LocalDBManager.Instance.SetDBSetting("RecoDeload", "false");
                        Device.BeginInvokeOnMainThread(async () =>
                        {
                            if (setList.Count > 0)
                            {
                                ScrollToSnap(setList, m);

                            }
                        });
                        if (m.IsFirstSide)
                        {
                            if (!m.IsPopup)
                            {
                                //AlertConfig ShowExplainRIRPopUp = new AlertConfig()
                                //{
                                //    Title = "Side 1",
                                //    Message = "Do all sets for side 1.",
                                //    //  //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                //    OkText = "Side 1",

                                //};
                                //await Task.Factory.StartNew(() => {
                                //    Device.BeginInvokeOnMainThread(() =>
                                //    {
                                //        UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
                                //    });
                                //});
                                m.IsPopup = true;
                            }


                        }

                        //if (Config.ShowWelcomePopUp4 == false)
                        //{
                        //    if (App.IsWelcomePopup4)
                        //        return;
                        //    App.IsWelcomePopup4 = true;
                        //    ConfirmConfig ShowWelcomePopUp4 = new ConfirmConfig()
                        //    {
                        //        Message = AppResources.ShowWelcomePopUp4Message,
                        //        Title = AppResources.ShowWelcomePopUp4Title,
                        //        //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //        OkText = AppResources.GotIt,
                        //        CancelText = AppResources.RemindMe,
                        //        OnAction = async (bool ok) =>
                        //        {
                        //            if (ok)
                        //            {
                        //                Config.ShowWelcomePopUp4 = true;
                        //            }
                        //            else
                        //            {
                        //                Config.ShowWelcomePopUp4 = false;
                        //            }
                        //        }
                        //    };
                        //    await Task.Delay(100);
                        //    UserDialogs.Instance.Confirm(ShowWelcomePopUp4);
                        //}

                        if (!IsSetWeightPopup)
                        {
                            IsSetWeightPopup = true;
                            var s = setList.Where(x => x.IsFirstWorkSet).FirstOrDefault();
                            var min = LocalDBManager.Instance.GetDBSetting("repsminimum").Value;
                            var max = LocalDBManager.Instance.GetDBSetting("repsmaximum").Value;
                            var text = "";
                            try
                            {
                                if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscle")
                                {
                                    text = "build muscle";
                                }
                                else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscleBurnFat")
                                {
                                    text = "build muscle and burn fat";
                                }
                                else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "FatBurning")
                                {
                                    text = "burn fat";
                                }
                            }
                            catch (Exception ex)
                            {

                            }
                            await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = $"Because your goal is to {text}.",
                                Title = $"2-6 work sets of {min}-{max} reps",
                                OkText = AppResources.GotIt
                            });
                            
                            //CurrentLog.Instance.WalkThroughCustomTipsPopup = true;
                            //await PagesFactory.PushAsync<LearnPage>();
                            PagesFactory.PopAsync();
                        }
                    }
                }
                

            }
            catch (Exception ex)
            {

            }
        }

        public decimal ComputeOneRM(decimal weight, int reps)
        {
            return (decimal)(AppThemeConstants.Coeficent * reps) * weight + weight;
        }

        private async void ScrollToActiveSet(WorkoutLogSerieModelRef set, ExerciseWorkSetsModel m)
        {
            try
            {

                if (set != null)
                {
                    //if (Device.RuntimePlatform.Equals(Device.iOS))
                    Device.BeginInvokeOnMainThread(async () =>
                    {
                        if (Device.RuntimePlatform.Equals(Device.Android))
                            ExerciseListView.SelectedItem = set;

                        ExerciseListView.ScrollTo(set, ScrollToPosition.End, true);

                        await Task.Delay(200);
                        ExerciseListView.ScrollTo(set, ScrollToPosition.MakeVisible, true);



                    });
                    return;
                    await Task.Delay(300);
                    int position = 0;
                    foreach (var itemGood in exerciseItems)
                    {
                        if (itemGood == m)
                            break;
                        position += 1;
                        position += itemGood.Count;
                    }
                    var index = m.IndexOf(set);

                    //ExerciseListView.ItemPosition = position+index;
                    //ExerciseListView.IsScrolled = !ExerciseListView.IsScrolled;

                    ExerciseListView.ScrollTo(set, ScrollToPosition.MakeVisible, true);
                }



            }
            catch (Exception ex)
            {

            }
        }

        private void ScrollToSnap(List<WorkoutLogSerieModelRef> setList, ExerciseWorkSetsModel m)
        {
            try
            {

                Device.BeginInvokeOnMainThread(async () =>
                {
                    if (setList.Count > 0)
                    {
                        if (Device.RuntimePlatform.Equals(Device.iOS))
                            ExerciseListView.ScrollTo(setList.First(), ScrollToPosition.Start, true);
                        // await Task.Delay(300);
                        int position = 0;
                        foreach (var itemGood in exerciseItems)
                        {
                            if (itemGood == m)
                                break;
                            position += 1;
                            position += itemGood.Count;
                        }
                        if (Device.RuntimePlatform.Equals(Device.iOS))
                        {
                            ExerciseListView.ItemPosition = exerciseItems.IndexOf(m);
                            ExerciseListView.IsScrolled = !ExerciseListView.IsScrolled;
                            ExerciseListView.ScrollTo(setList.First(), ScrollToPosition.Start, true);
                        }
                        else
                        {
                            ExerciseListView.ItemPosition = position;
                            ExerciseListView.IsScrolled = !ExerciseListView.IsScrolled;
                        }

                    }
                });

            }
            catch (Exception ex)
            {

            }
        }
        async void animate(View grid)
        {
            try
            {
                if (Battery.EnergySaverStatus == EnergySaverStatus.On && Device.RuntimePlatform.Equals(Device.Android))
                    return;
                var a = new Animation();
                a.Add(0, 0.5, new Animation((v) =>
                {
                    grid.Scale = v;
                }, 1.0, 0.8, Easing.CubicInOut, () => { System.Diagnostics.Debug.WriteLine("ANIMATION A"); }));
                a.Add(0.5, 1, new Animation((v) =>
                {
                    grid.Scale = v;
                }, 0.8, 1.0, Easing.CubicInOut, () => { System.Diagnostics.Debug.WriteLine("ANIMATION B"); }));
                a.Commit(grid, "animation", 16, 2000, null, (d, f) =>
                {
                    grid.Scale = 1.0;
                    System.Diagnostics.Debug.WriteLine("ANIMATION ALL");
                    if (IsAnimate)
                        animate(grid);
                });

            }
            catch (Exception ex)
            {

            }
        }


        private void OnBindingContextChanged(object sender, EventArgs e)
        {
            base.OnBindingContextChanged();
            try
            {

                ((ViewCell)sender).Height = 115;

                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;
                if (m != null && m.IsFinishWorkoutExe)
                    ((ViewCell)sender).Height = 90;
                var btnVideo = (DrMuscleButton)((StackLayout)((StackLayout)((StackLayout)((Frame)((Grid)((ViewCell)sender).View).Children[2]).Children[0]).Children[0]).Children[5]).Children[5];
                if (string.IsNullOrEmpty(m.VideoUrl))
                    btnVideo.IsVisible = false;
                else
                    btnVideo.IsVisible = true;
              

                if (IsAnimate)
                {
                    animate(((ViewCell)sender).View);
                }

            }
            catch (Exception ex)
            {

            }
            //Image swapImage = (Image)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[0]).Children[2];
            //if (m.IsSwapTarget)
            //{
            //    swapImage.IsVisible = true;
            //}
            //else
            //{
            //    swapImage.IsVisible = false;
            //}
        }

        private async void OnDeload(object sender, System.EventArgs e)
        {
            try
            {
                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;

                //ConfirmConfig supersetConfig = new ConfirmConfig()
                //{
                //    Title = "Deload?",
                //    Message = "2 work sets and 5-10% less weight. Helps to recover.",
                //    OkText = "Deload",
                //    CancelText = AppResources.Cancel,
                //    OnAction = async (bool ok) =>
                //    {
                //        string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                //        string exId = $"{m.Id}";
                //        if (ok)
                //        {
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");
                //            m.RecoModel = null;
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                //            {
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                //                m.Clear();
                //                try
                //                {

                //                }
                //                catch (Exception ex)
                //                {

                //                }
                //            }
                //            FetchReco(m, null);
                //        }
                //        else
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                //    }
                //};
                //UserDialogs.Instance.Confirm(supersetConfig);

                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                string exId = $"{m.Id}";
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") == null || LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "")
                {
                    ConfirmConfig supersetConfig = new ConfirmConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = "Deload",
                        Message = m.IsBodyweight ? $"2 work sets and 15-20% fewer reps. Helps you recover. Deload?" : "2 work sets and 5-10% less weight. Helps you recover. Deload?",
                        OkText = "Deload",
                        CancelText = AppResources.Cancel,

                    };
                    var res = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
                    if (res)
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"deload");
                    else
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"");

                }
                else
                {
                    m.RecoModel.IsDeload = false;
                    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"");
                }

                LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                {
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                    m.Clear();
                }
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                        contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#ECFF92");
                }

                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        contextMenuStack.Children[4].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        contextMenuStack.Children[4].BackgroundColor = Color.FromHex("#ECFF92");
                }
                FetchReco(m, null);

            }
            catch (Exception ex)
            {

            }
        }

        private async void OnChallenge(object sender, System.EventArgs e)
        {
            try
            {

                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;

                //ConfirmConfig supersetConfig = new ConfirmConfig()
                //{
                //    Title = "Feeling strong?",
                //    Message = "We'll shoot for 10% more reps. Be safe: stop before your form breaks down.",
                //    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    OkText = AppResources.Challenge,
                //    CancelText = AppResources.Cancel,
                //    OnAction = async (bool ok) =>
                //    {
                //        string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                //        string exId = $"{m.Id}";
                //        if (ok)
                //        {
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                //            m.RecoModel = null;
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                //            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                //            {
                //                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                //                m.Clear();
                //            }
                //            FetchReco(m, null);
                //        }
                //        else
                //            LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                //    }
                //};
                //UserDialogs.Instance.Confirm(supersetConfig);

                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                string exId = $"{m.Id}";
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") == null || LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "")
                {
                    ConfirmConfig supersetConfig = new ConfirmConfig()
                    {
                        Title = "Challenge",
                        Message = "Feeling strong? 10-15% more reps. Be safe: stop before your form breaks down.",
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = AppResources.Challenge,
                        CancelText = AppResources.Cancel,

                    };
                    var res = await UserDialogs.Instance.ConfirmAsync(supersetConfig);
                    if (res)
                    {
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"max");
                    }
                    else
                        LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");
                }
                else
                    LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "challenge", $"");

                LocalDBManager.Instance.SetDBReco("RReps" + exId + setStyle + "Deload", $"");
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(m.Id))
                {
                    CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(m.Id);
                    m.Clear();
                }
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                        contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        contextMenuStack.Children[3].BackgroundColor = Color.FromHex("#ECFF92");
                }

                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        contextMenuStack.Children[4].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        contextMenuStack.Children[4].BackgroundColor = Color.FromHex("#ECFF92");
                }
                FetchReco(m, null);


            }
            catch (Exception ex)
            {

            }
        }

        private async void OnVideo(object sender, System.EventArgs e)
        {
            if (contextMenuStack != null)
                HideContextButton();
            CurrentLog.Instance.VideoExercise = GetExerciseModel(((ExerciseWorkSetsModel)((Button)sender).CommandParameter));
            await PagesFactory.PushAsync<ExerciseVideoPage>();
            OnCancelClicked(sender, e);
        }

        private async void OnSwap(object sender, System.EventArgs e)
        {
            try
            {
                SwapExerciseContext context = new SwapExerciseContext();
                context.WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
                context.SourceExerciseId = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter).Id;
                context.SourceBodyPartId = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter).BodyPartId;
                ExerciseWorkSetsModel model = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter);
                context.Label = model.Label;
                context.IsBodyweight = model.IsBodyweight;
                context.IsSystemExercise = model.IsSystemExercise;
                context.IsEasy = model.IsEasy;
                context.VideoUrl = model.VideoUrl;
                context.BodyPartId = model.BodyPartId;
                context.IsUnilateral = model.IsUnilateral;
                context.IsTimeBased = model.IsTimeBased;
                CurrentLog.Instance.SwapContext = context;
                await PagesFactory.PushAsync<ChooseYourCustomExercisePage>();
                OnCancelClicked(sender, e);


            }
            catch (Exception ex)
            {

            }
        }

        private async void OnRestore(object sender, System.EventArgs e)
        {
            try
            {

                ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)((BindableObject)sender).BindingContext;
                SwapExerciseContext sec = ((App)Application.Current).SwapExericesContexts.Swaps.First(s => s.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && s.TargetExerciseId == m.Id);
                ((App)Application.Current).SwapExericesContexts.Swaps.Remove(sec);
                ((App)Application.Current).SwapExericesContexts.SaveContexts();
                OnCancelClicked(sender, e);

                await UpdateExerciseList();


            }
            catch (Exception ex)
            {

            }
        }

        public async void ResetExercisesAction(ExerciceModel model)
        {
            BooleanModel result = await DrMuscleRestClient.Instance.ResetExercise(model);
            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
        }

        public async void OnReset(object sender, EventArgs e)
        {
            var mi = ((Button)sender);

            ExerciseWorkSetsModel m = (ExerciseWorkSetsModel)mi.CommandParameter;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = GetExerciseModel(m);
            if (!string.IsNullOrEmpty(m.VideoUrl) || m.IsSystemExercise)
                await PagesFactory.PushAsync<ExerciseSettingsPage>();
            else
                await PagesFactory.PushAsync<ExerciseCustomSettingsPage>();
            IsSettingsChanged = true;
            OnCancelClicked(sender, e);
            //ConfirmConfig p = new ConfirmConfig()
            //{
            //    Title = AppResources.ResetExercise, 
            //    Message = AppResources.AreYouSureYouWantToResetThisExerciseAndDeleteAllItsHistoryThisCannotBeUndone,// string.Format("Are you sure you want to reset this exercise and delete all its history? This cannot be undone.", m.Label),
            //    OkText = AppResources.Reset,
            //    CancelText = AppResources.Cancel,
            //    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed)
            //};
            //p.OnAction = (obj) =>
            //{
            //    if (obj)
            //    {
            //        ResetExercisesAction(m);
            //        OnCancelClicked(sender, e);
            //    }
            //};
            //UserDialogs.Instance.Confirm(p);
        }

        void HideContextButton()
        {
            try
            {

                StackLayout s1 = (StackLayout)contextMenuStack.Parent;
                s1.Children[1].IsVisible = true;
                s1.Children[2].IsVisible = true;

                contextMenuStack.Children[0].IsVisible = false;
                contextMenuStack.Children[1].IsVisible = false;
                contextMenuStack.Children[2].IsVisible = false;
                contextMenuStack.Children[3].IsVisible = false;
                contextMenuStack.Children[4].IsVisible = false;
                contextMenuStack.Children[5].IsVisible = false;
                contextMenuStack.Children[6].IsVisible = true;
                contextMenuStack = null;
            }
            catch (Exception ex)
            {

            }
        }

        void OnCancelClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);

            StackLayout s1 = (StackLayout)s.Parent;
            s1.Children[1].IsVisible = true;
            s1.Children[2].IsVisible = true;
            s.Children[0].IsVisible = false;
            s.Children[1].IsVisible = false;
            s.Children[2].IsVisible = false;
            s.Children[3].IsVisible = false;
            s.Children[4].IsVisible = false;
            s.Children[5].IsVisible = false;
            s.Children[6].IsVisible = true;
        }

        void OnContextMenuClicked(object sender, System.EventArgs e)
        {
            if (contextMenuStack != null)
                HideContextButton();
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            ExerciseWorkSetsModel m = ((ExerciseWorkSetsModel)((Button)sender).CommandParameter);
            if (m.IsNextExercise)
            {
                StackLayout s1 = (StackLayout)s.Parent;
                s1.Children[1].IsVisible = false;
                s1.Children[2].IsVisible = false;

                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                string exId = $"{m.Id}";

                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "Deload").Value == "deload")
                        s.Children[3].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        s.Children[3].BackgroundColor = Color.FromHex("#ECFF92");
                }

                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        s.Children[4].BackgroundColor = Color.FromHex("#72DF40");
                    else
                        s.Children[4].BackgroundColor = Color.FromHex("#ECFF92");
                }
            }


            s.Children[0].IsVisible = !string.IsNullOrEmpty(m.VideoUrl);
            s.Children[1].IsVisible = !m.IsSwapTarget;
            s.Children[2].IsVisible = m.IsSwapTarget;
            s.Children[3].IsVisible = m.IsNextExercise;
            s.Children[4].IsVisible = m.IsNextExercise;



            s.Children[5].IsVisible = true;
            s.Children[6].IsVisible = false;
            contextMenuStack = s;
        }

        private async Task UpdateExerciseList()
        {
            var exercises = new ObservableCollection<ExerciseWorkSetsModel>();
            exerciseItems = new ObservableCollection<ExerciseWorkSetsModel>();

            try
            {
                try
                {
                    if (CurrentLog.Instance.WorkoutsByExercise != null && CurrentLog.Instance.WorkoutsByExercise.Count == 0)
                    {

                        var listExercises = ((App)Application.Current).WorkoutListContexts.WorkoutsByExercise;
                        if (listExercises != null)
                            CurrentLog.Instance.WorkoutsByExercise = listExercises;

                    }
                    LblWorkoutName.Text = CurrentLog.Instance.CurrentWorkoutTemplate.Label;

                    if (CurrentLog.Instance.WorkoutsByExercise.ContainsKey(CurrentLog.Instance.CurrentWorkoutTemplate.Id))
                        CurrentLog.Instance.CurrentWorkoutTemplate.Exercises = CurrentLog.Instance.WorkoutsByExercise[CurrentLog.Instance.CurrentWorkoutTemplate.Id];

                }
                catch (Exception ex)
                {

                }
                var count = 1;
                foreach (ExerciceModel ee in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                {

                    ExerciseWorkSetsModel e = new ExerciseWorkSetsModel()
                    {
                        Id = ee.Id,
                        IsBodyweight = ee.IsBodyweight,
                        IsEasy = ee.IsEasy,
                        IsNextExercise = OpenExercises.Contains(ee.Id) ? true : false,
                        IsSwapTarget = ee.IsSwapTarget,
                        IsFinished = ee.IsFinished,
                        IsSystemExercise = ee.IsSystemExercise,
                        IsNormalSets = ee.IsNormalSets,
                        IsUnilateral = ee.IsUnilateral,
                        IsTimeBased = ee.IsTimeBased,
                        IsMedium = ee.IsMedium,
                        BodyPartId = ee.BodyPartId,
                        Label = ee.Label,
                        VideoUrl = ee.VideoUrl,
                        WorkoutGroupId = ee.WorkoutGroupId,
                        RepsMaxValue = ee.RepsMaxValue,
                        RepsMinValue = ee.RepsMinValue,
                        IsPlate = ee.IsPlate,
                        Timer = ee.Timer,
                        IsSelected = false,
                        CountNo = $"{count} of {CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Count}"

                    };
                    string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                    LocalDBManager.Instance.SetDBReco("RReps" + ee.Id + setStyle + "challenge", $"");
                    bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == e.Id);

                    if (!isSwapped)
                    {
                        e.IsSwapTarget = false;
                        if (LocalDBManager.Instance.GetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{e.Id}") != null)
                        {
                            if (LocalDBManager.Instance.GetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{e.Id}").Value == "true")
                            {
                                e.IsFinished = true;
                                e.IsNextExercise = false;
                            }
                        }

                        exercises.Add(e);
                    }
                    else
                    {
                        try
                        {
                            long targetExerciseId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == e.Id).TargetExerciseId;
                            GetExerciseRequest req = new GetExerciseRequest();
                            req.ExerciseId = targetExerciseId;
                            ExerciceModel emm = await DrMuscleRestClient.Instance.GetExercise(req);

                            //var seriParent = JsonConvert.SerializeObject(emm);
                            //ExerciseWorkSetsModel em = JsonConvert.DeserializeObject<ExerciseWorkSetsModel>(seriParent);
                            ExerciseWorkSetsModel em = GetExerciseWorkSetModel(emm);
                            em.CountNo = $"{count} of {CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Count}";
                            //if ((Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == em.Id) != null)
                            //{
                            //    em.IsFinished = true;
                            //    em.IsNextExercise = false;
                            //}
                            if (LocalDBManager.Instance.GetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{em.Id}") != null)
                            {
                                if (LocalDBManager.Instance.GetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{em.Id}").Value == "true")
                                {
                                    em.IsFinished = true;
                                    em.IsNextExercise = false;
                                }
                            }
                            em.IsSwapTarget = true;
                            exercises.Add(em);
                        }
                        catch (Exception ex)
                        {
                            SwapExerciseContext context = ((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && c.SourceExerciseId == e.Id);
                            if (!string.IsNullOrEmpty(context.Label))
                            {
                                ExerciseWorkSetsModel model = new ExerciseWorkSetsModel()
                                {
                                    Id = (long)context.TargetExerciseId,
                                    Label = context.Label,
                                    IsBodyweight = context.IsBodyweight,
                                    IsSwapTarget = true,
                                    IsSystemExercise = context.IsSystemExercise,
                                    VideoUrl = context.VideoUrl,
                                    IsEasy = context.IsEasy,
                                    BodyPartId = context.BodyPartId,
                                    IsUnilateral = context.IsUnilateral,
                                    IsTimeBased = context.IsTimeBased,
                                    IsPlate = context.IsPlate,
                                    CountNo = $"{count} of {CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Count}",
                                    IsNextExercise = OpenExercises.Contains((long)context.TargetExerciseId) ? true : false

                                };
                                model.IsSwapTarget = true;
                                //if ((Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == model.Id) != null)
                                //{
                                //    model.IsFinished = true;
                                //    model.IsNextExercise = false;
                                //}
                                if (LocalDBManager.Instance.GetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{model.Id}") != null)
                                {
                                    if (LocalDBManager.Instance.GetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{model.Id}").Value == "true")
                                    {
                                        model.IsFinished = true;
                                        model.IsNextExercise = false;
                                    }
                                }
                                exercises.Add(model);

                            }

                        }

                    }
                    try
                    {
                        LocalDBManager.Instance.SetDBSetting($"{CurrentLog.Instance.CurrentWorkoutTemplate.Id}Challenge{(int)ee.BodyPartId}", $"");
                    }
                    catch (Exception ex)
                    {

                    }
                    count += 1;
                }
                var isSelected = exercises.Where(x => x.IsFinished == true).FirstOrDefault();
                var exModel = exercises.Where(x => x.IsFinished == false).FirstOrDefault();
                if (isSelected != null && exModel != null)
                {
                    exModel.IsNextExercise = true;
                    ResetButtons();
                }


                exerciseItems = exercises;
                //try
                //{
                //    if (CurrentLog.Instance.WorkoutsByExercise == null)
                //        CurrentLog.Instance.WorkoutsByExercise = new Dictionary<long, List<ExerciceModel>>();
                //    if (CurrentLog.Instance.WorkoutsByExercise.ContainsKey(CurrentLog.Instance.CurrentWorkoutTemplate.Id))
                //        CurrentLog.Instance.WorkoutsByExercise[CurrentLog.Instance.CurrentWorkoutTemplate.Id] = exercises.ToList();
                //    else
                //        CurrentLog.Instance.WorkoutsByExercise.Add(CurrentLog.Instance.CurrentWorkoutTemplate.Id, exercises.ToList());

                //}
                //catch (Exception ex)
                //{

                //}


                var nextExer = exerciseItems.Where(x => x.IsNextExercise).FirstOrDefault();
                if (nextExer != null && nextExer.Id != 0 && nextExer.Id != -1)
                {


                    NewExerciseLogResponseModel newExercise = await DrMuscleRestClient.Instance.IsNewExerciseWithSessionInfo(new ExerciceModel() { Id = nextExer.Id });
                    if (newExercise != null)
                    {
                        if (!newExercise.IsNewExercise)
                        {

                            //await FetchReco(nextExer);
                            try
                            {
                                DateTime? lastLogDate = newExercise.LastLogDate;
                                int? sessionDays = null;


                                string WeightRecommandation;
                                RecommendationModel reco = null;

                                //Todo: clean up on 2019 01 18
                                if (LocalDBManager.Instance.GetDBSetting("SetStyle") == null)
                                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                                if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
                                    LocalDBManager.Instance.SetDBSetting("QuickMode", "false");
                                var bodyPartname = "";


                                switch (nextExer.BodyPartId)
                                {
                                    case 1:
                                        break;
                                    case 2:
                                        bodyPartname = "Shoulders";
                                        break;
                                    case 3:
                                        bodyPartname = "Chest";
                                        break;
                                    case 4:
                                        bodyPartname = "Back";
                                        break;
                                    case 5:
                                        bodyPartname = "Biceps";
                                        break;
                                    case 6:
                                        bodyPartname = "Triceps";
                                        break;
                                    case 7:
                                        bodyPartname = "Abs";
                                        break;
                                    case 8:
                                        bodyPartname = "Legs";
                                        break;
                                    case 9:
                                        bodyPartname = "Calves";
                                        break;
                                    case 10:
                                        bodyPartname = "Neck";
                                        break;
                                    case 11:
                                        bodyPartname = "Forearm";
                                        break;
                                    default:
                                        //
                                        break;
                                }

                                if (lastLogDate != null)
                                {
                                    var days = (int)(DateTime.Now - (DateTime)lastLogDate).TotalDays;
                                    if (days >= 5 && days <= 9)
                                        sessionDays = days;
                                    if (days > 9)
                                    {
                                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                                        {
                                            Title = "Light session?",
                                            Message = string.IsNullOrEmpty(bodyPartname) == false ? $"The last time you trained {bodyPartname.ToLower()} was {days} {AppResources.DaysAgoDoALightSessionToRecover}" : string.Format("{0} {1} {2} {3} {4}", "The last time you trained", nextExer.Label, AppResources.was, days, AppResources.DaysAgoDoALightSessionToRecover),
                                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                            OkText = AppResources.LightSession,
                                            CancelText = AppResources.Cancel,
                                        };
                                        try
                                        {
                                            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                                            LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                                        }
                                        catch (Exception ex)
                                        {

                                        }

                                        CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
                                        CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(nextExer);

                                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                                        if (isConfirm)
                                        {
                                            if (days > 50)
                                                days = 50;
                                            sessionDays = days;
                                            App.WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
                                            App.BodypartId = (int)CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId;
                                            App.Days = days;
                                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(nextExer.Id))
                                            {
                                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(nextExer.Id);
                                                nextExer.RecoModel = null;
                                            }
                                            //reco.Weight = new MultiUnityWeight(reco.Weight.Kg - ((reco.Weight.Kg * (decimal)1.5 * days) / 100), "kg");
                                            //reco.WarmUpWeightSet1 = new MultiUnityWeight(reco.WarmUpWeightSet1.Kg - ((reco.WarmUpWeightSet1.Kg * (decimal)1.5 * days) / 100), "kg");
                                            //reco.WarmUpWeightSet2 = new MultiUnityWeight(reco.WarmUpWeightSet2.Kg - ((reco.WarmUpWeightSet2.Kg * (decimal)1.5 * days) / 100), "kg");
                                        }
                                        else
                                        {
                                            sessionDays = null;
                                            App.Days = 0;
                                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef == null)
                                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                                            if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(nextExer.Id))
                                            {
                                                CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(nextExer.Id);
                                                nextExer.RecoModel = null;
                                            }
                                        }
                                    }
                                }
                                try
                                {
                                    if (App.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && App.BodypartId == (int)nextExer.BodyPartId && App.Days > 9)
                                    {
                                        sessionDays = App.Days;
                                    }

                                }
                                catch (Exception ex)
                                {

                                }

                                await FetchReco(nextExer, sessionDays);
                            }
                            catch (Exception ex)
                            {
                                await FetchReco(nextExer);
                            }
                        }
                        else
                        {
                            RunExercise(nextExer);
                        }
                    }
                }
                else
                {
                    if (exerciseItems.Count > 0 && !exerciseItems.First().IsFinished)
                        FetchNextExerciseBackgroundData(exerciseItems.First());
                }

                //Finish workout button
                //var allFinished = exerciseItems.Where(x => x.IsFinished == false).FirstOrDefault();
                //if (allFinished != null)
                //{
                //    if (exerciseItems.Where(x => x.IsFinishWorkoutExe == true).FirstOrDefault() == null)
                //        exerciseItems.Add(new ExerciseWorkSetsModel() { IsFinishWorkoutExe = true });
                //}
                //else
                //{

                //    if (exerciseItems.Where(x => x.IsFinishWorkoutExe == true).FirstOrDefault() == null)
                //    {

                //    exerciseItems.Add(new ExerciseWorkSetsModel() { IsFinishWorkoutExe = true, IsFinished = true });
                //        //Nice workpopup
                //        try
                //        {

                //            ConfirmConfig ShowConfirmPopUp = new ConfirmConfig()
                //            {
                //                Message = "All exercises complete. Finish & save workout?",
                //                Title = "Nice work! ",
                //                //  //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //                OkText = "Finish workout",
                //                CancelText = AppResources.Cancel,
                //                OnAction = async (bool ok) =>
                //                {
                //                    if (ok)
                //                    {
                //                        SavingExcercise();
                //                    }
                //                }
                //            };
                //            UserDialogs.Instance.Confirm(ShowConfirmPopUp);

                //        }
                //        catch (Exception ex)
                //        {

                //        }
                //    }
                //}
                if (exerciseItems.Where(x => x.IsFinishWorkoutExe == true).FirstOrDefault() == null)
                    exerciseItems.Add(new ExerciseWorkSetsModel() { IsFinishWorkoutExe = true });

                ExerciseListView.ItemsSource = exerciseItems;

            }
            catch (Exception e)
            {
                //await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = AppResources.PleaseCheckInternetConnection,
                    Title = AppResources.ConnectionError
                });
            }
        }

        public ExerciseWorkSetsModel GetExerciseWorkSetModel(ExerciceModel ee)
        {
            return new ExerciseWorkSetsModel()
            {
                Id = ee.Id,
                IsBodyweight = ee.IsBodyweight,
                IsEasy = ee.IsEasy,
                IsNextExercise = ee.IsNextExercise,
                IsSwapTarget = ee.IsSwapTarget,
                IsFinished = ee.IsFinished,
                IsSystemExercise = ee.IsSystemExercise,
                IsNormalSets = ee.IsNormalSets,
                IsUnilateral = ee.IsUnilateral,
                IsTimeBased = ee.IsTimeBased,
                IsMedium = ee.IsMedium,
                BodyPartId = ee.BodyPartId,
                Label = ee.Label,
                VideoUrl = ee.VideoUrl,
                WorkoutGroupId = ee.WorkoutGroupId,
                RepsMaxValue = ee.RepsMaxValue,
                RepsMinValue = ee.RepsMinValue,
                Timer = ee.Timer,
                IsPlate = ee.IsPlate,
                IsSelected = false,
            };
        }

        public ExerciceModel GetExerciseModel(ExerciseWorkSetsModel ee)
        {
            return new ExerciceModel()
            {
                Id = ee.Id,
                IsBodyweight = ee.IsBodyweight,
                IsEasy = ee.IsEasy,
                IsNextExercise = ee.IsNextExercise,
                IsSwapTarget = ee.IsSwapTarget,
                IsFinished = ee.IsFinished,
                IsSystemExercise = ee.IsSystemExercise,
                IsNormalSets = ee.IsNormalSets,
                IsUnilateral = ee.IsUnilateral,
                IsTimeBased = ee.IsTimeBased,
                IsMedium = ee.IsMedium,
                BodyPartId = ee.BodyPartId,
                Label = ee.Label,
                VideoUrl = ee.VideoUrl,
                WorkoutGroupId = ee.WorkoutGroupId,
                RepsMaxValue = ee.RepsMaxValue,
                RepsMinValue = ee.RepsMinValue,
                Timer = ee.Timer,
                IsPlate = ee.IsPlate,
            };
        }


        private async void ListTapped(object sender, EventArgs args)
        {
            if (contextMenuStack != null)
                HideContextButton();
        }

        private async void NewTapped(object sender, EventArgs args)
        {
            try
            {
                if (Config.AddExercisesPopUp == false)
                {
                    if (App.IsAddExercisesPopUp)
                    {
                        AddExercises();
                        return;
                    }
                    App.IsAddExercisesPopUp = true;
                    ConfirmConfig ShowAddExePopUp = new ConfirmConfig()
                    {
                        Message = "Add or remove exercises and reorder on the fly.",
                        Title = "Edit workout",
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = AppResources.GotIt,
                        CancelText = AppResources.RemindMe,
                        OnAction = async (bool ok) =>
                        {
                            if (ok)
                            {
                                Config.AddExercisesPopUp = true;
                                AddExercises();
                            }
                            else
                            {
                                Config.AddExercisesPopUp = false;
                                AddExercises();
                            }
                        }
                    };
                    await Task.Delay(100);
                    UserDialogs.Instance.Confirm(ShowAddExePopUp);
                }
                else
                {
                    CurrentLog.Instance.IsAddingExerciseLocally = true;
                    await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
                }

            }
            catch (Exception ex)
            {

            }
        }

        private async void AddExercises()
        {
            CurrentLog.Instance.IsAddingExerciseLocally = true;
            await PagesFactory.PushAsync<AddExercisesToWorkoutPage>();
        }

        private void ResetButtons()
        {
            //SaveWorkoutButton.TextColor = Color.White;
            //SaveWorkoutButton.BackgroundColor = Color.Transparent;
            //SaveWorkoutButton.FontAttributes = FontAttributes.None;
        }

        private void ChangeButtonsEmphasis()
        {
            //SaveWorkoutButton.TextColor = Color.Black;
            //SaveWorkoutButton.BackgroundColor = Color.White;
            //SaveWorkoutButton.FontAttributes = FontAttributes.Bold;
        }

        //New Exercise Setup
        protected async Task RunExercise(ExerciseWorkSetsModel m)
        {
            if (m.Id == 0)
                return;
            CurrentLog.Instance.EndExerciseActivityPage = this.GetType();
            CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
            CurrentLog.Instance.ExerciseLog.Exercice = GetExerciseModel(m);

            try
            {

                if (m.IsBodyweight && LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                {
                    decimal weight1 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                    if (m.Id == 16508)
                    {
                        SetUpCompletePopup(weight1, m.Label, m, 29, true);
                        return;
                    }
                    KenkoAskForReps(weight1, m.Label, m);
                    return;
                }
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = false,
                    Title = string.Format("{0} {1}", CurrentLog.Instance.ExerciseLog.Exercice.Label, AppResources.Setup),
                    //Message = m.IsBodyweight ? string.Format("{0} ({1} {2})?", AppResources.WhatsYourBodyWeight, AppResources._in,
                    //LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg") :
                    //m.IsEasy ?
                    //string.Format("{0} {1} {2}",
                    //AppResources.HowMuchCanYou, m.Label, AppResources.VeryVeryVeryEasily6TimesIwillImproveOnYourGuessAfterYourFirstWorkout) :
                    Message = m.IsBodyweight ? m.IsTimeBased ? $"How many seconds can you {m.Label} very easily? I'll improve on your guess after your first workout." : $"How many {m.Label} can you do easily?" : m.IsTimeBased ? $"How many seconds can you {m.Label} very easily? I'll improve on your guess after your first workout." :
                        string.Format("{0} {1} {2}",
                          AppResources.HowMuchCanYou, m.Label, AppResources.VeryEasily6TimesIWillImproveOnYourGuessAfterYourFirstWorkout),
                    Placeholder = "Tap to enter your weight",
                    OkText = AppResources.Ok,
                    MaxLength = 4,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = async (weightResponse) =>
                    {
                        if (weightResponse.Ok)
                        {
                            if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                            {
                                RunExercise(m);
                                //await UserDialogs.Instance.AlertAsync(m.IsTimeBased? "Please enter valid seconds." : "Please enter valid reps.", AppResources.Error);
                                return;
                            }
                            var weightText = weightResponse.Value.Replace(",", ".");
                            decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                            if (m.IsBodyweight)
                            {
                                LocalDBManager.Instance.SetDBSetting("BodyWeight", weight1.ToString());
                                await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                                {
                                    BodyWeight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                                });
                                KenkoAskForReps(new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg, m.Label, m);
                                return;
                            }
                            SetUpCompletePopup(new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg, m.Label, m);
                        }
                        else
                            m.IsNextExercise = false;
                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);

            }
            catch (Exception ex)
            {
                //try
                //{
                //    if (!CurrentLog.Instance.WorkoutLogSeriesByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                //    {
                //        await PagesFactory.PushAsync<ExerciseChartPage>();
                //    }
                //    else
                //    {
                //        if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                //            LocalDBManager.Instance.SetDBSetting("timer_remaining", CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id).ToString());
                //        await PagesFactory.PushAsync<SaveSetPage>();
                //    }
                //}
                //catch (Exception e)
                //{
                //    var properties = new Dictionary<string, string>
                //    {
                //        { "DrMusclePage_RunExercise", $"{e.StackTrace}" }
                //    };
                //    Crashes.TrackError(e, properties);
                //    await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
                //}

            }
        }

        protected async void KenkoAskForReps(decimal weight1, string exerciseName, ExerciseWorkSetsModel m)
        {
            try
            {
                if (m.Id == 16508)
                {
                    SetUpCompletePopup(weight1, exerciseName, m, 29, true);
                    return;
                }
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = false,
                    Title = $"{m.Label}",
                    Message = "How many can you do easily?",
                    Placeholder = "Enter how many",
                    OkText = AppResources.Save,
                    MaxLength = 4,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = async (weightResponse) =>
                    {
                        if (weightResponse.Ok)
                        {
                            if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                            {
                                //await UserDialogs.Instance.AlertAsync(m.IsTimeBased ? "Please enter valid seconds." : "Please enter valid reps.", AppResources.Error);
                                KenkoAskForReps(weight1, exerciseName, m);
                                return;
                            }
                            int reps = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                            SetUpCompletePopup(weight1, exerciseName, m, reps, true);
                        }
                        else
                            m.IsNextExercise = false;
                    }
                };
                firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);

            }
            catch (Exception ex)
            {

            }
        }
        //HIIT Cardio Id: #16508
        protected async void SetUpCompletePopup(decimal weight1, string exerciseName, ExerciseWorkSetsModel exe, int reps = 6, bool IsBodyweight = false)
        {
            try
            {
                NewExerciceLogModel model = new NewExerciceLogModel();
                model.ExerciseId = (int)CurrentLog.Instance.ExerciseLog.Exercice.Id;
                model.Username = LocalDBManager.Instance.GetDBSetting("email").Value;

                if (IsBodyweight)
                {
                    model.Weight1 = new MultiUnityWeight(weight1, "kg");
                    model.Reps1 = reps.ToString();
                    model.Weight2 = new MultiUnityWeight(weight1, "kg");
                    model.Reps2 = (reps - 1).ToString();
                    model.Weight3 = new MultiUnityWeight(weight1, "kg");
                    model.Reps3 = (reps - 2).ToString();
                    model.Weight4 = new MultiUnityWeight(weight1, "kg");
                    model.Reps4 = (reps - 3).ToString();
                }
                else
                {
                    weight1 = weight1 + (weight1 / 100);
                    decimal weight2 = weight1 - (2 * weight1 / 100);
                    decimal weight3 = weight2 - (2 * weight2 / 100);
                    decimal weight4 = weight3 - (2 * weight3 / 100);
                    model.Weight1 = new MultiUnityWeight(weight1, "kg");
                    model.Reps1 = reps.ToString();
                    model.Weight2 = new MultiUnityWeight(weight2, "kg");
                    model.Reps2 = reps.ToString();
                    model.Weight3 = new MultiUnityWeight(weight3, "kg");
                    model.Reps3 = reps.ToString();
                    model.Weight4 = new MultiUnityWeight(weight4, "kg");
                    model.Reps4 = reps.ToString();
                }

                await DrMuscleRestClient.Instance.AddNewExerciseLogWithMoreSet(model);
                FetchReco(exe);
                //ConfirmConfig confirmExercise = new ConfirmConfig()
                //{
                //    Title = AppResources.SetupComplete,
                //    Message = string.Format("{0} {1}", exerciseName, AppResources.SetupCompleteExerciseNow),
                //    OkText = string.Format("{0}", exerciseName),
                //    CancelText = AppResources.Cancel,
                //    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomRed),
                //    OnAction = async (bool obj) => {
                //        if (obj)
                //        {
                //            //await PagesFactory.PushAsync<ExerciseChartPage>();

                //        }
                //    }
                //};

                //UserDialogs.Instance.Confirm(confirmExercise);

            }
            catch (Exception ex)
            {

            }
        }

    }
}