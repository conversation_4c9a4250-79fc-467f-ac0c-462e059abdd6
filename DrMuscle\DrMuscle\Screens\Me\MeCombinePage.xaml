﻿<?xml version="1.0" encoding="UTF-8" ?>
<t:DrMusclePage
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:t="clr-namespace:DrMuscle.Layout"
    xmlns:effects="clr-namespace:DrMuscle.Effects"
                xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
    xmlns:oxy="clr-namespace:OxyPlot.Xamarin.Forms;assembly=OxyPlot.Xamarin.Forms"
    xmlns:control="clr-namespace:DrMuscle.Controls"
    xmlns:constnats="clr-namespace:DrMuscle.Constants"
    x:Class="DrMuscle.Screens.Me.MeCombinePage">
    <Grid>
    <StackLayout HorizontalOptions="FillAndExpand"
                 Padding="20,0,20,10" x:Name="ContentStack">

        <StackLayout x:Name="ChartStack"
                     VerticalOptions="FillAndExpand">
            <ScrollView>
                <StackLayout>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <control:ZoomableScrollview x:Name="plotScroll1"
                                                    Grid.Column="0"
                                                    Orientation="Horizontal">
                            <oxy:PlotView  x:Name="plotView"
                                           IsEnabled="false"
                                           IsVisible="true"
                                           VerticalOptions="Start"
                                           HeightRequest="150">
                            </oxy:PlotView>
                        </control:ZoomableScrollview>
                        <control:ZoomableScrollview x:Name="plotScroll2"
                                                    Grid.Row="1"
                                                    
                                                    Orientation="Horizontal">
                            <oxy:PlotView x:Name="plotViewVolume"
                                          Grid.Row="2"
                                          IsEnabled="false"
                                          IsVisible="true"
                                          VerticalOptions="Start"
                                          HeightRequest="150">
                            </oxy:PlotView>
                        </control:ZoomableScrollview>

                        <StackLayout Grid.Row="2" IsEnabled="False">
            <oxy:PlotView
                x:Name="plotView3"
                IsVisible="true"
                VerticalOptions="Start"
                HeightRequest="150">
            </oxy:PlotView>
        </StackLayout>
                    </Grid>
                     
                                <Button
                            Margin="7,0,7,10"
                            Text="Update body weight"
                            HorizontalOptions="FillAndExpand"
                            VerticalOptions="Center"
                            Clicked="UpdateBodyweightClicked"
                            Style="{StaticResource buttonStyle}"
                            TextColor="{x:Static constnats:AppThemeConstants.BlueColor}" />
                    <Label x:Name="LblProgress"
                           Text=""
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />
                    <Label x:Name="LblSetsProgress"
                           Text=""
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />

                    <Label Margin="0,0,0,0"
                           x:Name="lblWorkoutsDone"
                           IsVisible="false"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />
                   
                    <Label x:Name="lblLevel"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />
                     <Label x:Name="lblLiftedCount"
                           IsVisible="false"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />
                    <StackLayout Orientation="Horizontal" HorizontalOptions="Center">

                        <!--<Image Source="edit_plate_blue.png"
                               HeightRequest="50"
                               Aspect="AspectFit" VerticalOptions="Center" />-->
                        <Label
                            Text="View more stats on the Web"
                            HeightRequest="45"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            Style="{StaticResource OnBoardingLabelStyle}"
                            TextColor="{x:Static constnats:AppThemeConstants.BlueColor}" />
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="ViewMoreStats_Clicked" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>
                    <Label x:Name="lblWorkout"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />

                    <!--<Label x:Name="lblProgram"
                           Text=""
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />

                    <StackLayout Orientation="Horizontal" HorizontalOptions="Center">
                        <Label
                            Text="Change program"
                            HeightRequest="45"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            Style="{StaticResource OnBoardingLabelStyle}"
                            TextColor="{x:Static constnats:AppThemeConstants.BlueColor}" />
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="ChangeWorkoutClicked" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>-->
                    
                </StackLayout>
            </ScrollView>
        </StackLayout>
        <Label x:Name="LblTimeFrame"
               Margin="20,0"
               Style="{StaticResource BoldLabelStyle}" />
        <StackLayout x:Name="PickerStack"
                     VerticalOptions="End">
            <control:DropDownPicker x:Name="ExericsesPicker"
                    Margin="20,0,20,0"
                    HeightRequest="40"
                    Style="{StaticResource PickerStyle}" >
                <control:DropDownPicker.Image>
                    <OnPlatform x:TypeArguments="x:String" Android="white_down_arrow.png" iOS="black_down_arrow.png" />
                </control:DropDownPicker.Image>
                </control:DropDownPicker>
            <control:DropDownPicker x:Name="DatePicker"
                    Margin="20,0,20,0"
                                    HeightRequest="40"
                    Style="{StaticResource PickerStyle}" >

                <control:DropDownPicker.Image>
                    <OnPlatform x:TypeArguments="x:String" Android="white_down_arrow.png" iOS="black_down_arrow.png" />
                </control:DropDownPicker.Image>
                </control:DropDownPicker>
        </StackLayout>
    </StackLayout>
        <StackLayout HorizontalOptions="FillAndExpand" x:Name="NoDataLabel" VerticalOptions="FillAndExpand" Grid.Row="0" BackgroundColor="#D4D4D4" Spacing="10" IsVisible="false">

<StackLayout VerticalOptions="CenterAndExpand" HorizontalOptions="Center"  BackgroundColor="Transparent" Margin="0,30,0,0"  Spacing="10" Padding="0,45,0,2">

                <Image WidthRequest="150" HeightRequest="150" HorizontalOptions="Center" VerticalOptions="Start" Source="Trophy.png" />

                    <Label Text="Nothing to chart yet!" Margin="0,30,0,0" HorizontalOptions="Center" FontSize="Medium" Font="Bold,20" TextColor="Black" />
                    <Label Text="Save an exercise to see your charts here." HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>




            
                
            </StackLayout>
            <pancakeView:PancakeView
                x:Name="ToolTipButton"
            Padding="0"
            Margin="25,0,25,25"
            IsClippedToBounds="true"
            OffsetAngle="90"
            CornerRadius="0"
                VerticalOptions="EndAndExpand"
                        HorizontalOptions="FillAndExpand" 
                        HeightRequest="66" effects:TooltipEffect.Text="Tap me"
                               effects:TooltipEffect.BackgroundColor="{x:Static constnats:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Top"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="False">
                 <pancakeView:PancakeView.BackgroundGradientStops>
                <pancakeView:GradientStopCollection>
                    
                </pancakeView:GradientStopCollection>
            </pancakeView:PancakeView.BackgroundGradientStops>
                    
              <t:DrMuscleButton x:Name="ButtonStartWorkout" BackgroundColor="#195377" Text="Start workout" HeightRequest="66" 
                        BorderColor="Transparent"
                                
                        TextColor="White" Style="{StaticResource buttonStyle}" HorizontalOptions="FillAndExpand"
                                >
              </t:DrMuscleButton>
                        </pancakeView:PancakeView>
            </StackLayout>
</Grid>
</t:DrMusclePage>
