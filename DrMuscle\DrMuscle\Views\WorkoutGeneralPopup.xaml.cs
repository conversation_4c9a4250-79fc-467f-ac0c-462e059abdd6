﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMuscle.Dependencies;
using DrMuscle.Helpers;
using DrMuscle.Message;
using DrMuscle.Screens.Subscription;
using DrMuscle.Screens.User;
using DrMuscleWebApiSharedModel;
using Rg.Plugins.Popup.Pages;
using Rg.Plugins.Popup.Services;
using Xamarin.Essentials;
using Xamarin.Forms;

namespace DrMuscle.Views
{
    public partial class WorkoutGeneralPopup : PopupPage
    {
        TapGestureRecognizer okGuesture;
        string buttonText = "";
        public event EventHandler OkButtonPress;
        public bool _isHide { get; set; }
        public WorkoutGeneralPopup(string image, string title, string subtitle, string buttonText, Thickness? thickness = null, bool isTips = false, bool isSummary = false, string isShowLearnMore = "false", string isShowSettings = "false", string ismealPlan = "false", string isNotNow = "false", string isAutoHide = "false")
        {
            InitializeComponent();
            okGuesture = new TapGestureRecognizer();
            okGuesture.Tapped += DrMuscleButtonCancel_Clicked;
            OkAction.GestureRecognizers.Add(okGuesture);
            ImgName.Source = image;
            if (thickness != null)
                ImgName.Margin = (Thickness)thickness;
            LblHeading.Text = title;
            LblSubHead.Text = $"{subtitle}";
            OkButton.Text = buttonText;
            this.buttonText = buttonText;
            LblTipText.IsVisible = false;
            if (isShowLearnMore == "true")
            {
                BtnLearnMore.IsVisible = true;
                LblSubHead.Margin = new Thickness(15, 0, 15, 5);


            }
            else if (isShowSettings == "true")
            {
                BtnLearnMore.IsVisible = true;
                LblSubHead.Margin = new Thickness(15, 0, 15, 5);
                BtnLearnMore.Text = "Open Settings";

            }
            else if (ismealPlan == "true")
            {
                //BtnCancel.IsVisible = true;
                //LblSubHead.Margin = new Thickness(15, 0, 15, 5);
                BtnLearnMore.IsVisible = false;
            }
            else if (isNotNow == "true")
            {
               // BtnCancel.IsVisible = true;
                BtnLearnMore.IsVisible = false;
               // BtnCancel.Text = "Not now";
            }
            else
            {
                BtnLearnMore.IsVisible = false;
            }

            if (isTips)
            {
                SetLoading(title);
                //OkButton.Clicked -= OkButton_Clicked;
                OkAction.GestureRecognizers.Remove(okGuesture);
                //OkButton.Text = "Customizing workout...";
                OkAction.IsVisible = false;
                MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
                {
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        LblTipText.IsVisible = false;
                        OkAction.IsVisible = true;

                        //OkButton.Text = "Start workout";
                        //OkButton.Clicked += OkButton_Clicked;
                        OkAction.GestureRecognizers.Add(okGuesture);
                    });
                });
            }
            if (isSummary)
            {

                //OkButton.Clicked -= OkButton_Clicked;
                OkAction.GestureRecognizers.Remove(okGuesture);
                SetLoadingSummary(okGuesture);
                //MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
                //{
                //Device.BeginInvokeOnMainThread(() =>
                //{

                //OkButton.Clicked += OkButton_Clicked;

                //});
                //});
            }
            if (isAutoHide == "true")
            {


            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            _isHide = true;
            MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");
        }
        private async Task SetLoadingSummary(TapGestureRecognizer okGuesture)
        {
            //await Task.Delay(250);

            //OkButton.Text = "Loading.";

            //await Task.Delay(700);
            //OkButton.Text = "Loading..";

            //await Task.Delay(700);

            //OkButton.Text = "Loading...";
            //await Task.Delay(700);
            OkButton.Text = this.buttonText;
            OkAction.GestureRecognizers.Add(okGuesture);

        }

        private async void SetLoading(string title)
        {
            //LblHeading.FontAttributes = LblSubHead.FontAttributes;
            //LblHeading.FontSize = LblSubHead.FontSize;
            //LblHeading.TextColor = LblSubHead.TextColor;

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                LblTipText.Text = "";
                LblTipText.IsVisible = true;
                await Task.Factory.StartNew(async () =>
                {

                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading sets...";
                    });


                    await Task.Delay(500);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {

                        LblTipText.Text = "Loading reps...";
                    });
                    await Task.Delay(750);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {

                        LblTipText.Text = "Loading weights...";
                    });
                    await Task.Delay(500);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading a big pump...";
                    });
                    await Task.Delay(500);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Let's go!";
                    });
                });
            }
            else
            {
                LblTipText.IsVisible = true;

                //ImgLoader.IsVisible = true;
                Device.BeginInvokeOnMainThread(async () =>
                {

                    LblTipText.Text = "Loading sets...";
                    await Task.Delay(700);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading reps...";
                    await Task.Delay(750);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading weights...";
                    await Task.Delay(800);
                    if (LblTipText.Text == " ")
                        return;
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading a big pump...";
                    await Task.Delay(800);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Let's go!";

                });
            }
        }

        void OkButton_Clicked(System.Object sender, System.EventArgs e)
        {
            PopupNavigation.Instance.PopAsync();
            
                if (OkButtonPress != null)
                    OkButtonPress.Invoke(sender, EventArgs.Empty);
            

        }

        void DrMuscleButton_Clicked(System.Object sender, System.EventArgs e)
        {
            if (BtnLearnMore.Text.Equals("Open Settings"))
            {
                PopupNavigation.Instance.PopAsync();
                PagesFactory.PushAsync<SettingsPage>();
            }
            else if (BtnLearnMore.Text.Equals("Cancel"))
            {
                PopupNavigation.Instance.PopAsync();
                // PagesFactory.PopAsync();
            }
            else
            {
                if (CheckTrialUser())
                    return;
                PopupNavigation.Instance.PopAsync();
                PagesFactory.PushAsync<LearnPage>();
            }
        }

        private bool CheckTrialUser()
        {
            if (App.IsFreePlan)
            {
                ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                {
                    Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
                    Title = "You discovered a premium feature!",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Upgrade",
                    CancelText = "Maybe later",
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            PagesFactory.PushAsync<SubscriptionPage>();
                        }
                        else
                        {

                        }
                    }
                };
                UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
            return App.IsFreePlan;
        }

        void DrMuscleButtonCancel_Clicked(System.Object sender, System.EventArgs e)
        {
            PopupNavigation.Instance.PopAsync();
            LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", "good");
            SetupLastWorkoutWas("good");
            MessagingCenter.Send<HowWasWorkoutMessage>(new HowWasWorkoutMessage() { HowWasWorkout = "good" }, "HowWasWorkoutMessage");
        }

        void DrMuscleButtonTooHard_Clicked(System.Object sender, System.EventArgs e)
        {
            PopupNavigation.Instance.PopAsync();
            LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", "too hard");
            SetupLastWorkoutWas("too hard");
            MessagingCenter.Send<HowWasWorkoutMessage>(new HowWasWorkoutMessage() { HowWasWorkout = "too hard" }, "HowWasWorkoutMessage");
        }

        void DrMuscleButtonEasy_Clicked(System.Object sender, System.EventArgs e)
        {
            PopupNavigation.Instance.PopAsync();

            LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", "too easy");
            SetupLastWorkoutWas("too easy");
            MessagingCenter.Send<HowWasWorkoutMessage>(new HowWasWorkoutMessage() { HowWasWorkout = "too easy" }, "HowWasWorkoutMessage");
        }

        private async void SetupLastWorkoutWas(string workoutwas)
        {
            await DrMuscleRestClient.Instance.SetUserLastWorkoutWas(new UserInfosModel()
            {
                LastWorkoutWas = workoutwas
            });           
        }
    }
}
