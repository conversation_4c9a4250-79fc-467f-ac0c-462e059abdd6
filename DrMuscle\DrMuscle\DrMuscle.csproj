﻿<?xml version="1.0" encoding="utf-8"?>

<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <BuildWithMSBuildOnMono>true</BuildWithMSBuildOnMono>
  </PropertyGroup>

  <ItemGroup>
    <!--<PackageReference Include="" Version=""/>-->
    <PackageReference Include="Acr.UserDialogs" Version="7.2.0.564" />
  <PackageReference Include="Branch-Xamarin-Linking-SDK" Version="7.0.7" />
  <PackageReference Include="Microsoft.AppCenter" Version="5.0.3" />
  <PackageReference Include="Microsoft.AppCenter.Analytics" Version="5.0.3" />
  <PackageReference Include="Microsoft.AppCenter.Crashes" Version="5.0.3" />
  <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
  <PackageReference Include="Microsoft.Net.Http" Version="2.2.29" />
  <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
  <PackageReference Include="Microsoft.NETCore.Targets" Version="3.1.0" />
  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  <PackageReference Include="OxyPlot.Core" Version="2.1.2" />
  <PackageReference Include="OxyPlot.Xamarin.Forms" Version="2.1.0" />
  <PackageReference Include="Plugin.FirebasePushNotification" Version="3.4.35" />
  <PackageReference Include="Plugin.GoogleClient" Version="2.1.12" />
  <PackageReference Include="Plugin.Toast" Version="2.2.0" />
  <PackageReference Include="Rg.Plugins.Popup" Version="2.1.0" />
  <PackageReference Include="runtime.native.System" Version="4.3.1" />
  <PackageReference Include="SegmentedControl.FormsPlugin" Version="2.0.1" />
  <PackageReference Include="Sentry.Xamarin.Forms" Version="2.0.0" />
  <PackageReference Include="SlideOverKit" Version="2.1.6.2" />
  <PackageReference Include="Splat" Version="14.8.12" />
  <PackageReference Include="sqlite-net-pcl" Version="1.8.116" />
  <PackageReference Include="SQLitePCLRaw.core" Version="2.1.8" />
  <PackageReference Include="System.Collections" Version="4.3.0" />
  <PackageReference Include="System.Collections.Concurrent" Version="4.3.0" />
  <PackageReference Include="System.Diagnostics.Debug" Version="4.3.0" />
  <PackageReference Include="System.Diagnostics.Tools" Version="4.3.0" />
  <PackageReference Include="System.Diagnostics.Tracing" Version="4.3.0" />
  <PackageReference Include="System.Globalization" Version="4.3.0" />
  <PackageReference Include="System.IO" Version="4.3.0" />
  <PackageReference Include="System.IO.Compression" Version="4.3.0" />
  <PackageReference Include="System.Linq" Version="4.3.0" />
  <PackageReference Include="System.Linq.Expressions" Version="4.3.0" />
  <PackageReference Include="System.Net.Http" Version="4.3.4" />
  <PackageReference Include="System.Net.Primitives" Version="4.3.1" />
  <PackageReference Include="System.Numerics.Vectors" Version="4.5.0" />
  <PackageReference Include="System.ObjectModel" Version="4.3.0" />
  <PackageReference Include="System.Reflection" Version="4.3.0" />
  <PackageReference Include="System.Reflection.Extensions" Version="4.3.0" />
  <PackageReference Include="System.Reflection.Primitives" Version="4.3.0" />
  <PackageReference Include="System.Resources.ResourceManager" Version="4.3.0" />
  <PackageReference Include="System.Runtime" Version="4.3.1" />
  <PackageReference Include="System.Runtime.Extensions" Version="4.3.1" />
  <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
  <PackageReference Include="System.Runtime.InteropServices.RuntimeInformation" Version="4.3.0" />
  <PackageReference Include="System.Runtime.Numerics" Version="4.3.0" />
  <PackageReference Include="System.Text.Encoding" Version="4.3.0" />
  <PackageReference Include="System.Text.Encoding.Extensions" Version="4.3.0" />
  <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  <PackageReference Include="System.Threading" Version="4.3.0" />
  <PackageReference Include="System.Threading.Tasks" Version="4.3.0" />
  <PackageReference Include="System.Threading.Timer" Version="4.3.0" />
  <PackageReference Include="System.ValueTuple" Version="4.5.0" />
  <PackageReference Include="System.Xml.ReaderWriter" Version="4.3.1" />
  <PackageReference Include="System.Xml.XDocument" Version="4.3.0" />
  <PackageReference Include="VG.XFShapeView" Version="1.0.5" />
  <PackageReference Include="Xam.Plugin.Connectivity" Version="3.2.0" />
  <PackageReference Include="Xam.Plugin.LatestVersion" Version="2.1.0" />
  <PackageReference Include="Xam.Plugins.Settings" Version="3.1.1" />
  <PackageReference Include="Xam.Plugins.Vibrate" Version="4.0.0.5" />
  <PackageReference Include="Xamarin.Essentials" Version="1.8.1" />
  <PackageReference Include="Xamarin.FFImageLoading" Version="2.4.11.982" />
  <PackageReference Include="Xamarin.FFImageLoading.Forms" Version="2.4.11.982" />
  <PackageReference Include="Xamarin.FFImageLoading.Transformations" Version="2.4.11.982" />				
  <PackageReference Include="Xamarin.Forms" Version="5.0.0.2515" />
  <PackageReference Include="Xamarin.Forms.PancakeView" Version="2.3.0.759" />
  <PackageReference Include="Microcharts.Forms" Version="1.0.0-preview1" />
  <PackageReference Include="Xamarin.FFImageLoading.Svg.Forms" Version="2.4.11.982" />
  <PackageReference Include="Xamarin.Plugin.Calendar" Version="2.0.9699" />
  <PackageReference Include="particle.forms" Version="1.0.0" />
  <PackageReference Include="ImageFromXamarinUI" Version="1.0.0" />
  <PackageReference Include="Plugin.StoreReview" Version="6.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Remove="Views\WelcomePage.xaml" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Exercises.json" />
    <None Remove="ProgramList.json" />
    <None Remove="LocalReco.json" />
    <None Remove="Xamarin.FFImageLoading.Svg.Forms" />
    <None Remove="Screens\Eve\" />
    <None Remove="Xamarin.Plugin.Calendar" />
    <None Remove="OnBoarding\" />
    <None Remove="particle.forms" />
    <None Remove="Image\typing_loader.gif" />
    <None Remove="Utility\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Exercises.json" />
    <EmbeddedResource Include="ProgramList.json" />
    <EmbeddedResource Include="LocalReco.json" />
    <EmbeddedResource Include="Image\typing_loader.gif" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Screens\Eve\" />
    <Folder Include="OnBoarding\" />
    <Folder Include="Utility\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Screens\User\RegistrationPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\CustomPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>
</Project>
