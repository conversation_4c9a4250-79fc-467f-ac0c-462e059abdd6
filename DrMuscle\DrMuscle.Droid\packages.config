﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Acr.UserDialogs" version="7.2.0.564" targetFramework="monoandroid12.0" />
  <package id="AndHUD" version="1.4.3" targetFramework="monoandroid13.0" />
  <package id="Bolts" version="1.4.0.1" targetFramework="monoandroid10.0" />
  <package id="Branch-Xamarin-Linking-SDK" version="7.0.7" targetFramework="monoandroid10.0" />
  <package id="ImageFromXamarinUI" version="1.0.0" targetFramework="monoandroid13.0" />
  <package id="K4os.Compression.LZ4" version="1.3.6" targetFramework="monoandroid13.0" />
  <package id="Microcharts.Android" version="1.0.0-preview1" targetFramework="monoandroid10.0" />
  <package id="Microcharts.Forms" version="1.0.0-preview1" targetFramework="monoandroid10.0" />
  <package id="Microsoft.AppCenter" version="5.0.3" targetFramework="monoandroid13.0" />
  <package id="Microsoft.AppCenter.Analytics" version="5.0.3" targetFramework="monoandroid13.0" />
  <package id="Microsoft.AppCenter.Crashes" version="5.0.3" targetFramework="monoandroid13.0" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="monoandroid13.0" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="monoandroid10.0" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="monoandroid81" />
  <package id="Naxam.TargetTooltip.Droid" version="1.3.15" targetFramework="monoandroid90" />
  <package id="NETStandard.Library" version="2.0.3" targetFramework="monoandroid90" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="monoandroid13.0" />
  <package id="OxyPlot.Core" version="2.1.2" targetFramework="monoandroid13.0" />
  <package id="OxyPlot.Xamarin.Android" version="1.1.0-unstable0011" targetFramework="monoandroid81" />
  <package id="OxyPlot.Xamarin.Forms" version="2.1.0" targetFramework="monoandroid13.0" />
  <package id="particle.forms" version="1.0.0" targetFramework="monoandroid12.1" />
  <package id="Plugin.CurrentActivity" version="*******" targetFramework="monoandroid90" />
  <package id="Plugin.FirebasePushNotification" version="3.4.35" targetFramework="monoandroid12.1" />
  <package id="Plugin.GoogleClient" version="2.1.12" targetFramework="monoandroid10.0" />
  <package id="Plugin.InAppBilling" version="7.1.0" targetFramework="monoandroid13.0" />
  <package id="Plugin.StoreReview" version="6.2.0" targetFramework="monoandroid13.0" />
  <package id="Plugin.Toast" version="2.2.0" targetFramework="monoandroid90" />
  <package id="Rg.Plugins.Popup" version="2.1.0" targetFramework="monoandroid13.0" />
  <package id="SegmentedControl.FormsPlugin" version="2.0.1" targetFramework="monoandroid81" />
  <package id="Sentry" version="4.2.1" targetFramework="monoandroid13.0" />
  <package id="Sentry.Android.AssemblyReader" version="4.2.1" targetFramework="monoandroid13.0" />
  <package id="Sentry.Xamarin" version="2.0.0" targetFramework="monoandroid13.0" />
  <package id="Sentry.Xamarin.Forms" version="2.0.0" targetFramework="monoandroid13.0" />
  <package id="SkiaChart.Forms" version="1.2.50" targetFramework="monoandroid10.0" />
  <package id="SkiaSharp" version="2.88.7" targetFramework="monoandroid13.0" />
  <package id="SkiaSharp.NativeAssets.Android" version="2.88.7" targetFramework="monoandroid13.0" />
  <package id="SkiaSharp.Views" version="2.88.7" targetFramework="monoandroid13.0" />
  <package id="SkiaSharp.Views.Forms" version="2.88.7" targetFramework="monoandroid13.0" />
  <package id="SlideOverKit" version="2.1.6.2" targetFramework="monoandroid90" />
  <package id="Splat" version="14.8.12" targetFramework="monoandroid13.0" />
  <package id="sqlite-net-pcl" version="1.8.116" targetFramework="monoandroid13.0" />
  <package id="SQLitePCLRaw.bundle_green" version="2.1.8" targetFramework="monoandroid13.0" />
  <package id="SQLitePCLRaw.core" version="2.1.8" targetFramework="monoandroid13.0" />
  <package id="SQLitePCLRaw.lib.e_sqlite3.android" version="2.1.8" targetFramework="monoandroid13.0" />
  <package id="SQLitePCLRaw.provider.e_sqlite3" version="2.1.8" targetFramework="monoandroid13.0" />
  <package id="SQLitePCLRaw.provider.e_sqlite3.android" version="1.1.14" targetFramework="monoandroid13.0" />
  <package id="System.AppContext" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Buffers" version="4.5.1" targetFramework="monoandroid13.0" />
  <package id="System.Collections" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Collections.Immutable" version="8.0.0" targetFramework="monoandroid13.0" />
  <package id="System.ComponentModel" version="4.3.0" targetFramework="monoandroid90" />
  <package id="System.ComponentModel.TypeConverter" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Console" version="4.3.1" targetFramework="monoandroid90" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Dynamic.Runtime" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Globalization" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.IO" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Linq" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Memory" version="4.5.5" targetFramework="monoandroid13.0" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="monoandroid90" />
  <package id="System.Net.Primitives" version="4.3.1" targetFramework="monoandroid90" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="monoandroid90" />
  <package id="System.ObjectModel" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Reflection" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Reflection.Metadata" version="8.0.0" targetFramework="monoandroid13.0" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Runtime" version="4.3.1" targetFramework="monoandroid90" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="monoandroid13.0" />
  <package id="System.Runtime.Extensions" version="4.3.1" targetFramework="monoandroid90" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Runtime.Serialization.Formatters" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Runtime.Serialization.Primitives" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="monoandroid81" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="monoandroid81" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="monoandroid13.0" />
  <package id="System.Text.Json" version="8.0.3" targetFramework="monoandroid13.0" />
  <package id="System.Text.RegularExpressions" version="4.3.1" targetFramework="monoandroid90" />
  <package id="System.Threading" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="monoandroid13.0" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="monoandroid90" />
  <package id="System.Xml.ReaderWriter" version="4.3.1" targetFramework="monoandroid90" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="monoandroid81" />
  <package id="System.Xml.XmlDocument" version="4.3.0" targetFramework="monoandroid81" />
  <package id="VG.XFShapeView" version="1.0.5" targetFramework="monoandroid81" />
  <package id="Xam.Plugin.Connectivity" version="3.2.0" targetFramework="monoandroid90" />
  <package id="Xam.Plugin.LatestVersion" version="2.1.0" targetFramework="monoandroid10.0" />
  <package id="Xam.Plugins.Settings" version="3.1.1" targetFramework="monoandroid81" />
  <package id="Xam.Plugins.Vibrate" version="4.0.0.5" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Arch.Core.Common" version="1.1.1.3" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Arch.Core.Runtime" version="1.1.1.3" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Arch.Lifecycle.Common" version="1.1.1.3" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Arch.Lifecycle.LiveData" version="1.1.1.3" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Arch.Lifecycle.LiveData.Core" version="1.1.1.3" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Arch.Lifecycle.Runtime" version="1.1.1.3" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Arch.Lifecycle.ViewModel" version="1.1.1.3" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Binding.InstallReferrer" version="2.2.0" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Android.Google.BillingClient" version="6.0.1.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Android.Support.Animated.Vector.Drawable" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Annotations" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.AsyncLayoutInflater" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Collections" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Compat" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.CoordinaterLayout" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Core.UI" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Core.Utils" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.CursorAdapter" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.CustomTabs" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.CustomView" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Design" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.DocumentFile" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.DrawerLayout" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Fragment" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Interpolator" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Loader" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.LocalBroadcastManager" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Media.Compat" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Print" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.SlidingPaneLayout" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.SwipeRefreshLayout" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Transition" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.v4" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.v7.AppCompat" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.v7.CardView" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.v7.MediaRouter" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.v7.Palette" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.v7.RecyclerView" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.Vector.Drawable" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.VersionedParcelable" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.Android.Support.ViewPager" version="********" targetFramework="monoandroid90" />
  <package id="Xamarin.AndroidX.Activity" version="1.8.2.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Activity.Ktx" version="1.8.2.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Annotation" version="1.7.1.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Annotation.Experimental" version="1.4.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Annotation.Jvm" version="1.7.1.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.AppCompat" version="1.6.1.7" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.AppCompat.AppCompatResources" version="1.6.1.8" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Arch.Core.Common" version="2.2.0.7" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Arch.Core.Runtime" version="2.2.0.7" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.AsyncLayoutInflater" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Browser" version="1.3.0.6" targetFramework="monoandroid12.1" />
  <package id="Xamarin.AndroidX.CardView" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Collection" version="1.4.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Collection.Jvm" version="1.4.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Collection.Ktx" version="1.4.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Concurrent.Futures" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.ConstraintLayout" version="2.1.4.10" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.ConstraintLayout.Core" version="1.0.4.10" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.CoordinatorLayout" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Core" version="1.12.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Core.Core.Ktx" version="1.12.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.CursorAdapter" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.CustomView" version="1.1.0.22" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.CustomView.PoolingContainer" version="1.0.0.9" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.DocumentFile" version="1.0.1.23" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.DrawerLayout" version="1.2.0.7" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.DynamicAnimation" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Emoji2" version="1.4.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Emoji2.ViewsHelper" version="1.4.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Fragment" version="1.6.2.2" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Fragment.Ktx" version="1.6.2.2" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Interpolator" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Legacy.Support.Core.UI" version="1.0.0.24" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Legacy.Support.Core.Utils" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Legacy.Support.V4" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.Common" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.LiveData" version="2.3.1.1" targetFramework="monoandroid12.1" />
  <package id="Xamarin.AndroidX.Lifecycle.LiveData.Core" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.Process" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.Runtime" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.Runtime.Ktx" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.ViewModel" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.ViewModel.Ktx" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Lifecycle.ViewModelSavedState" version="2.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Loader" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.LocalBroadcastManager" version="1.1.0.11" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Media" version="1.7.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.MediaRouter" version="1.6.0.3" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Migration" version="1.0.10" targetFramework="monoandroid12.0" />
  <package id="Xamarin.AndroidX.MultiDex" version="2.0.1.23" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Navigation.Common" version="2.7.7.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Navigation.Runtime" version="2.7.7.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Navigation.UI" version="2.3.5.5" targetFramework="monoandroid12.1" />
  <package id="Xamarin.AndroidX.Palette" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Preference" version="1.2.1.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Print" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.ProfileInstaller.ProfileInstaller" version="1.3.1.6" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.RecyclerView" version="1.3.2.2" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.ResourceInspection.Annotation" version="1.0.1.11" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.SavedState" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.SavedState.SavedState.Ktx" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.SlidingPaneLayout" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Startup.StartupRuntime" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.SwipeRefreshLayout" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Tracing.Tracing" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Transition" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.VectorDrawable" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.VectorDrawable.Animated" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.VersionedParcelable" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.ViewPager" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.ViewPager2" version="********" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Window" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.AndroidX.Window.Extensions.Core.Core" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Build.Download" version="0.11.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Essentials" version="1.8.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Facebook.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Facebook.AppLinks.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Facebook.Common.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Facebook.Core.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Facebook.GamingServices.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Facebook.Login.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Facebook.Messenger.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Facebook.Places.Android" version="7.1.0" targetFramework="monoandroid90" />
  <package id="Xamarin.Facebook.Share.Android" version="1*******" targetFramework="monoandroid10.0" />
  <package id="Xamarin.FFImageLoading" version="2.4.11.982" targetFramework="monoandroid81" />
  <package id="Xamarin.FFImageLoading.Forms" version="2.4.11.982" targetFramework="monoandroid81" />
  <package id="Xamarin.FFImageLoading.Svg" version="2.4.11.982" targetFramework="monoandroid10.0" />
  <package id="Xamarin.FFImageLoading.Svg.Forms" version="2.4.11.982" targetFramework="monoandroid10.0" />
  <package id="Xamarin.FFImageLoading.Transformations" version="2.4.11.982" targetFramework="monoandroid81" />
  <package id="Xamarin.Firebase.Abt" version="121.1.1.5" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Analytics" version="121.3.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Annotations" version="116.2.0.5" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Common" version="120.4.2.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Common.Ktx" version="120.4.2.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Components" version="117.1.5.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Config" version="121.5.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Datatransport" version="118.2.0.3" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Encoders" version="117.0.0.13" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Encoders.JSON" version="118.0.1.5" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Encoders.Proto" version="116.0.0.8" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Iid" version="121.1.0.13" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Iid.Interop" version="117.1.0.13" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Installations" version="117.2.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Installations.InterOp" version="117.2.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Measurement.Connector" version="120.0.1.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Firebase.Messaging" version="123.3.1.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Forms" version="5.0.0.2622" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Forms.CarouselView" version="2.3.0-pre2" targetFramework="monoandroid81" />
  <package id="Xamarin.Forms.PancakeView" version="2.3.0.759" targetFramework="monoandroid10.0" />
  <package id="Xamarin.Google.Android.DataTransport.TransportApi" version="3.0.0.10" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.Android.DataTransport.TransportBackendCct" version="3.1.9.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.Android.DataTransport.TransportRuntime" version="3.1.9.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.Android.Material" version="1.4.0.6" targetFramework="monoandroid12.0" />
  <package id="Xamarin.Google.Android.Play.Core" version="1.10.3.9" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.AutoValue.Annotations" version="1.10.4.3" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.Dagger" version="2.48.1.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.ErrorProne.Annotations" version="2.23.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.Guava.ListenableFuture" version="1.0.0.18" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Google.ZXing.Core" version="3.5.2.2" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Ads.Identifier" version="118.0.1.8" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Auth" version="120.7.0.2" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Auth.Api.Phone" version="118.0.1.8" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Auth.Base" version="118.0.10.2" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Base" version="118.2.0.5" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Basement" version="118.2.0.5" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.CloudMessaging" version="117.0.2.8" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Fido" version="120.1.0.1" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Measurement" version="121.3.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Measurement.Api" version="121.3.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Measurement.Base" version="121.3.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Measurement.Impl" version="121.3.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Measurement.Sdk" version="121.3.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Measurement.Sdk.Api" version="121.3.0.4" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Stats" version="117.0.3.8" targetFramework="monoandroid13.0" />
  <package id="Xamarin.GooglePlayServices.Tasks" version="118.0.2.6" targetFramework="monoandroid13.0" />
  <package id="Xamarin.JavaX.Inject" version="1.0.0.11" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Jetbrains.Annotations" version="24.1.0.2" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Kotlin.StdLib" version="1.9.23" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Kotlin.StdLib.Common" version="1.9.23" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Kotlin.StdLib.Jdk7" version="1.9.23" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Kotlin.StdLib.Jdk8" version="1.9.23" targetFramework="monoandroid13.0" />
  <package id="Xamarin.KotlinX.Coroutines.Android" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.KotlinX.Coroutines.Core" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.KotlinX.Coroutines.Core.Jvm" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.KotlinX.Coroutines.Play.Services" version="*******" targetFramework="monoandroid13.0" />
  <package id="Xamarin.Plugin.Calendar" version="2.0.9699" targetFramework="monoandroid13.0" />
  <package id="XamarinAndroidTooltip" version="3.0.0" targetFramework="monoandroid90" />
</packages>