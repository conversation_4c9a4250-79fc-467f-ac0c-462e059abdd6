﻿using System;
using System.IO;
using Android.App;
using Android.Content;
using Android.Graphics;
using Android.Support.V4.Content;
using DrMuscle.Dependencies;
using DrMuscle.Droid.Service;
using Xamarin.Essentials;
using Xamarin.Forms;
using Environment = Android.OS.Environment;

[assembly: Dependency(typeof(DroidShareService))]
namespace DrMuscle.Droid.Service
{
    public class DroidShareService : Activity, IShareService
    {
        public async void Share(string subject, string message, Stream stream)
        {
            var intent = new Intent(Intent.ActionSend);
            //intent.PutExtra(Intent.ExtraSubject, subject);
            intent.SetFlags(ActivityFlags.NewTask);
            intent.PutExtra(Intent.ExtraText, message);
            intent.SetType("image/png");

            Bitmap bitmap = BitmapFactory.DecodeStream(stream);
            MemoryStream ms = new MemoryStream();
            /*
            PermissionStatus status = await Permissions.CheckStatusAsync<Permissions.StorageWrite>();
            if(status != PermissionStatus.Granted)
            {
                status = await Permissions.RequestAsync<Permissions.StorageWrite>();

                if(status != PermissionStatus.Granted && !Permissions.ShouldShowRationale<Permissions.StorageWrite>())
                {
                    bool userChoice = await App.Current.MainPage.DisplayAlert("Permission Needed", "Please allow storage permission from settings.", "Open Settings", "Cancel");
                    if (userChoice)
                        AppInfo.ShowSettingsUI();

                    return;
                }
            }
            */
            //Environment.DirectoryDownloads
            var path = Environment.GetExternalStoragePublicDirectory(Environment.DirectoryPictures
                + Java.IO.File.Separator + $"DrMuscle_stats_{DateTime.Now:yyyyMMdd_hhmmss}.png");
            if (bitmap != null) { 
            using (var os = new System.IO.FileStream(path.AbsolutePath, System.IO.FileMode.Create))
            {
                bitmap.Compress(Bitmap.CompressFormat.Png, 100, os);
            }

            var imageUri = Android.Support.V4.Content.FileProvider.GetUriForFile(MainActivity.Instance, "com.drmaxmuscle.dr_max_muscle.fileprovider", path);
            intent.PutExtra(Intent.ExtraStream, imageUri);
            }
            MainActivity.Instance.StartActivity(Intent.CreateChooser(intent, "Share Image"));
        }
    }
}