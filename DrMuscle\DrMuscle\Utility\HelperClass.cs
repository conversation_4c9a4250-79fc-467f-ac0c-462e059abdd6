﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DrMuscle.Constants;
using DrMuscle.Dependencies;
using Plugin.StoreReview;
using Xamarin.Essentials;
using Xamarin.Forms;

namespace DrMuscle.Utility
{
    public class HelperClass
	{
        #region private fields
        private static bool _isRatePopupOpened = false;
        #endregion


        /// <summary>
        /// Share the app link.
        /// </summary>
        /// <returns></returns>
        public async static Task ShareApp(string compaign = "sidebar", string firebaseEventName = "App_Share", string additionInfomation = "")
        {
            var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                if (!string.IsNullOrEmpty(additionInfomation))
                {
                    await Xamarin.Essentials.Share.RequestAsync(new Xamarin.Essentials.ShareTextRequest
                    {
                        Uri = $"{additionInfomation}\n\nhttps://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}",
                        Subject = $"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence"
                    });
                }
                else
                {
                    await Xamarin.Essentials.Share.RequestAsync(new Xamarin.Essentials.ShareTextRequest
                    {
                        Uri = $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}",
                        Subject = $"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence"
                    });
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(additionInfomation))
                    await Xamarin.Essentials.Share.RequestAsync($"{additionInfomation}\n\n{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence \nhttps://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}");
                else
                    await Xamarin.Essentials.Share.RequestAsync($"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence \nhttps://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ", "_")}");
            }

            DependencyService.Get<IFirebase>().LogEvent(firebaseEventName, $"Compaign {compaign}");
        }

        /// <summary>
        /// To share image with some caption.
        /// </summary>
        /// <param name="imageStream">Image as stream</param>
        /// <param name="compaign">Caption to send</param>
        public static void ShareImage(Stream imageStream, string compaign = "exercise", string firebaseEventName = "App_Share", string additionMessage = "")
        {
            var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            var appUrl = $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign={compaign}&utm_content={firstname?.Replace(" ","_")}";

            string message = string.IsNullOrEmpty(additionMessage) ? appUrl : $"{appUrl}\n\n{additionMessage}";
            DependencyService.Get<IShareService>().Share("Dr.Muscle Stats", message, imageStream);
            DependencyService.Get<IFirebase>().LogEvent(firebaseEventName, $"Image and Compaign {compaign}");
        }

        /// <summary>
        /// Used to send mail to the support team (<EMAIL>) from user. It is async method so need to await.
        /// </summary>
        /// <param name="subject">Define the predefined subject with the mail will be sent</param>
        /// <param name="body">It is optional part that represents some predefine body text</param>
        /// <returns></returns>
        public async static Task SendMail(string subject, string body = null, string firebaseEventName = "App_Email")
        {
            await Launcher.OpenAsync(new Uri($"mailto:<EMAIL>?subject={subject}&body={body}"));
            DependencyService.Get<IFirebase>().LogEvent(firebaseEventName, $"Subject : {subject}");
        }

        /// <summary>
        /// Opens the store to provide rating and review.
        /// </summary>
        /// <returns></returns>
        public async static Task RateApp(string firebaseEventName = "App_Rate")
        {
            if (_isRatePopupOpened)
                return;

            try
            {
                DependencyService.Get<IFirebase>().LogEvent(firebaseEventName, "Rate_App");

                _isRatePopupOpened = true;
#if RELEASE
            await CrossStoreReview.Current.RequestReview(false);
#elif DEBUG
                await CrossStoreReview.Current.RequestReview(true);
#endif
                _isRatePopupOpened = false;
            }
            catch(Exception ex)
            {
                _isRatePopupOpened = false;
                System.Diagnostics.Debug.WriteLine($"Exception While rating app : {ex.Message}");
            }
        }

        public static String GetBandsColor(double weight, bool isKg)
        {
            try
            {
                var finals = weight;
                var availableBands = "";
                var bandsItems = new List<BandsModel>();
                // calculating total weight and the difference
                if (isKg)
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsKg").Value;
                    }
                }
                else
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsLb").Value;
                    }
                }
                var keyVal = availableBands;

                string[] items = keyVal.Split('|');
                foreach (var item in items)
                {
                    string[] pair = item.Split('_');
                    var model = new BandsModel();
                    if (pair.Length == 4)
                    {
                        model.BandColor = pair[0].FirstCharToUpper();
                        model.Key = pair[1];
                        try
                        {
                            model.Weight = double.Parse(pair[1]);
                        }
                        catch (Exception ex)
                        {
                            model.Weight = 0;
                        }
                        model.Value = Int32.Parse(pair[2]);
                        if (model.Value > 1)
                            model.Value = 1;
                        model.IsSystemPlates = pair[3] == "True" ? true : false;
                        if (model.Value != 0)
                            bandsItems.Add(model);
                    }
                }
                bandsItems.Sort(delegate (BandsModel c1, BandsModel c2) { return c2.Weight.CompareTo(c1.Weight); });

                var matchBands = bandsItems.FirstOrDefault(x => x.Weight == weight);
                if (matchBands != null)
                    return matchBands.BandColor;
                
            }
            catch (Exception ex)
            {

            }
            return "";
        }

        public static List<BandsModel> GetAvailableBands( bool isKg)
        {
            try
            {
               
                var availableBands = "";
                var bandsItems = new List<BandsModel>();
                // calculating total weight and the difference
                if (isKg)
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;

                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsKg").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsKg").Value;
                    }
                }
                else
                {
                    availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("BandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("HomeBandsLb").Value;
                    }
                    if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        availableBands = LocalDBManager.Instance.GetDBSetting("OtherBandsLb").Value;
                    }
                }
                var keyVal = availableBands;

                string[] items = keyVal.Split('|');
                foreach (var item in items)
                {
                    string[] pair = item.Split('_');
                    var model = new BandsModel();
                    if (pair.Length == 4)
                    {
                        model.BandColor = pair[0].FirstCharToUpper();
                        model.Key = pair[1];
                        try
                        {
                            model.Weight = double.Parse(pair[1]);
                        }
                        catch (Exception ex)
                        {
                            model.Weight = 0;
                        }
                        model.Value = Int32.Parse(pair[2]);
                        if (model.Value > 1)
                            model.Value = 1;
                        model.IsSystemPlates = pair[3] == "True" ? true : false;
                        if (model.Value != 0)
                            bandsItems.Add(model);
                    }
                }
                bandsItems.Sort(delegate (BandsModel c1, BandsModel c2) { return c1.Weight.CompareTo(c2.Weight); });
                return bandsItems;



            }
            catch (Exception ex)
            {

            }
            return new List<BandsModel>();
        }
    }
}

