﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMuscle.Constants;
using DrMuscle.Controls;
using DrMuscle.Dependencies;
using DrMuscle.Helpers;
using DrMuscle.Layout;
using DrMuscle.Resx;
using DrMuscleWebApiSharedModel;
using Plugin.Connectivity;
using Rg.Plugins.Popup.Services;
using Xamarin.Forms;

namespace DrMuscle.Screens.User
{
    public partial class SupportPage : DrMusclePage
    {

        bool IsLoading = false;
        bool IsLoadMore = false;
        bool IsAdmin = false;
        public ObservableCollection<Messages> messageList = new ObservableCollection<Messages>();


        public SupportPage()
        {
            InitializeComponent();
            RefreshLocalized();
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });
            Timer.Instance.OnTimerChange -= OnTimerChange;
            Timer.Instance.OnTimerDone -= OnTimerDone;
            Timer.Instance.OnTimerStop -= OnTimerStop;
            
        }
        void RefreshLocalized()
        {
            Title = AppResources.Support;
        }

        public override void OnBeforeShow()
        {
            messageList.Clear();
            StackInfo.Children.Clear();
        }
        public async void ArrowAction()
        {

        }
        public async void GroupChatAction()
        {
            await PagesFactory.PushAsync<GroupChatPage>();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            DependencyService.Get<IFirebase>().SetScreenName("support_page");
            IsLoadMore = false;

            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");
            StackInfo.IsVisible = true;
            StackInfo.Children.Clear();

            //TODO: Remove below comments when Group chat activate
            //var groupChatItem = new ToolbarItem("Group chat", "", GroupChatAction, ToolbarItemOrder.Primary, 0);
            this.ToolbarItems.Clear();
            //if (!App.IsV1User)
            //    this.ToolbarItems.Add(groupChatItem);

            
            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                {
                    ConnectionErrorPopup();

                    return;
                }
                Connect_Handler();

            }
            catch (Exception ex)
            {

            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            try
            {

            }
            catch (Exception ex)
            {

            }
        }

        private async void CheckUserIsSubscribed(string email)
        {
            var result = await DrMuscleRestClient.Instance.IsV1UserWithoutLoaderWithEmail(email);
            //Set toolbar
            if (result != null && (result.Result || result.IsMealPlan))
            {
                try
                {
                    mainGrid.BackgroundColor = AppThemeConstants.GreenTransparentColor;

                //var groupChatItem = new ToolbarItem("✓", null, ArrowAction, ToolbarItemOrder.Primary, 0);
                //this.ToolbarItems.Clear();
                    
                //this.ToolbarItems.Add(groupChatItem);

                }
                catch (Exception ex)
                {

                }
            } else
            {
                mainGrid.BackgroundColor = Color.FromHex("#f4f4f4");
            }
        }
        void Connect_Handler()
        {
            UserDialogs.Instance.HideLoading();
            
            if (IsAdmin)
                    {
                mainGrid.BackgroundColor = Color.FromHex("#f4f4f4");
                CheckUserIsSubscribed(CurrentLog.Instance.ReceiverEmail);
                Device.BeginInvokeOnMainThread(() => {
                            StackInfo.IsVisible = true;
                            var email = new ExtendedLabel()
                            {
                                TextColor = AppThemeConstants.BlueColor,
                                HorizontalOptions = LayoutOptions.FillAndExpand,
                                Text =$"{CurrentLog.Instance.ReceiverName} ({CurrentLog.Instance.ReceiverEmail})"
                            };
                            StackInfo.Children.Add(email);

                            var BoxBorder = new BoxView()
                            {
                                BackgroundColor = Color.LightGray,
                                HeightRequest = 0.5
                            };
                            StackInfo.Children.Add(BoxBorder);
                        });
                    }
                    else
                    {
                        Device.BeginInvokeOnMainThread(() => {

                            StackInfo.IsVisible = false;

                        });
                    }
                    messageList.Clear();
                    GetMessages();
        }


        private async void GetMessages()
        {
            try
            {

            
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
            {
                messageList.Clear();
                return;
            }
            if (IsLoading)
                return;
            IsLoading = true;

            //mPrevMessageListQuery.Load(25, true, (List<BaseMessage> messages, SendBirdException e) =>
            //{
            //if (IsAdmin)
            //openChannel.MarkAsRead();
            if (CurrentLog.Instance.SupportChats == null)
                CurrentLog.Instance.SupportChats = new List<ChatModel>();
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");

            if (IsAdmin)
                CurrentLog.Instance.SupportChats = new List<ChatModel>();
            if (CurrentLog.Instance.SupportChats.Count == 0 && messageList?.Count == 0)
                UserDialogs.Instance.ShowLoading();
            var chatMessages = CurrentLog.Instance.SupportChats.Count != 0 && messageList?.Count == 0 ? CurrentLog.Instance.SupportChats : await DrMuscleRestClient.Instance.FetchChatBoxWithoutLoader(new ChatModel() { CreatedAt = messageList.Count == 0 ? DateTime.UtcNow : messageList.Last().CreatedDate,

                    ChatRoomId = CurrentLog.Instance.RoomId,
                    ReceiverId = AppThemeConstants.ChatReceiverId
                });
            CurrentLog.Instance.SupportChats = chatMessages;
                LoadJustSentSupportMessage();
            UserDialogs.Instance.HideLoading();
                

            if (chatMessages != null)
                {
                    foreach (ChatModel msg in chatMessages)
                    {
                        messageList.Add(new Messages()
                        {
                            Message = msg.Message,
                            UserId = msg.SenderEmail,
                            ProfileUrl = "",
                            Nickname = msg.SenderName,
                            CreatedDate = msg.CreatedAt,
                            ChatType = ChannelType.Group,
                            IsFromAI = msg.IsFromAI
                        });
                    }
                }
            IsLoading = false;
            IsLoadMore = false;

            if (chatMessages == null )
                {
                return;
                }

            //if (messages.Count == 0)
            //return;

            //foreach (UserMessage msg in messages)
            //{
            //    messageList.Add(new Messages()
            //    {
            //        Message = msg.Message,
            //        UserId = msg.Sender.UserId,
            //        Nickname = msg.Sender.Nickname,
            //        ProfileUrl = msg.Sender.ProfileUrl,
            //        CreatedAt = msg.CreatedAt
            //    });
            //}

            if (chatMessages.Count == 0)
                {
                    if (messageList.Count>0)
                    {
                        if (string.IsNullOrEmpty(messageList.Last().UserId))
                            return;
                    }
                    if (messageList.Count <= 3 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.First().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                    {
                        var msg = "";
                        if (messageList.Any(x => x.UserId.ToLower().Equals(LocalDBManager.Instance.GetDBSetting("email").Value)))
                        {
                            if (DateTime.Now.DayOfWeek == DayOfWeek.Saturday || DateTime.Now.DayOfWeek == DayOfWeek.Sunday)
                                msg = $"Thanks {LocalDBManager.Instance.GetDBSetting("firstname").Value}. I'm not online at the moment, but you can expect a response by Monday evening.";
                            else
                                msg = $"Thanks { LocalDBManager.Instance.GetDBSetting("firstname").Value}. I'm not online at the moment, but you can expect a response by {AppThemeConstants.ChatTimeAgo(messageList.First().CreatedAt)} tomorrow.";
                        
                        messageList.Insert(0, new Messages()
                        {
                            Message = msg,
                            ProfileUrl = "",// messageList.First().ProfileUrl,
                            Nickname = "Carl Juneau",
                            UserId = "<EMAIL>",
                            CreatedDate = DateTime.UtcNow
                        });
                        lstChats.ItemsSource = messageList;
                        }
                    }
                    messageList.Add(new Messages()
                    {
                        UserId = ""
                    });
                    
                }

                Device.BeginInvokeOnMainThread(() =>
                {
                    lstChats.ItemsSource = messageList;
                    
                    if (messageList.Count < 26 && chatMessages.Count != 0)
                        lstChats.ScrollToFirst();
                    //else
                    //  lstChats.ScrollTo(messageList[messages.Count - 2], ScrollToPosition.Start, false);

                });
                IsLoading = false;
                IsLoadMore = false;
                //});
            }
            catch (Exception ex)
            {
                IsLoading = false;
                IsLoadMore = false;
            }
            finally
            {
                UserDialogs.Instance.HideLoading();

            }

        }
        private async void BtnSendTapGestureRecognizer_Tapped(object sender, EventArgs ea)
        {
            //if (openChannel == null)
            //    return;
            if (string.IsNullOrEmpty(chatInput.MessageText) || string.IsNullOrEmpty(chatInput.MessageText.Trim()))
                return;

            //openChannel.SendUserMessage(chatInput.MessageText.Trim(), null, (UserMessage userMessage, SendBirdException e) =>
            //{
                //if (e != null)
                //{
                //    // Error.
                //    System.Diagnostics.Debug.WriteLine($"Send message error:{e.StackTrace}");
                //    return;
                //}
            LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);
            var txt = chatInput.MessageText.Trim();
            chatInput.UnFocusEntry();
            await Task.Delay(100);
            var isadded = await DrMuscleRestClient.Instance.SendMessage(new ChatModel()
            {
                
                ChatRoomId = CurrentLog.Instance.RoomId,
                ReceiverId = CurrentLog.Instance.RoomId == 0 ? AppThemeConstants.ChatReceiverId : "",
                Message = txt
            });
            LoadJustSentSupportMessage();
            LocalDBManager.Instance.SetDBSetting("IsFirstMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsSecondMessage", null);
            LocalDBManager.Instance.SetDBSetting("IsFirstMessageSend", null);
            if (!isadded.Result)
                return;
            messageList.Insert(0, new Messages()
            {
                Message = txt,
                ProfileUrl = "",
                Nickname = IsAdmin ? "Carl Juneau" : LocalDBManager.Instance.GetDBSetting("firstname")?.Value,
                UserId = IsAdmin ? "<EMAIL>" : LocalDBManager.Instance.GetDBSetting("email")?.Value,
                CreatedDate = DateTime.UtcNow,
                
            });
           
                Device.BeginInvokeOnMainThread(() =>
                {
                    if (messageList.Count == 1)
                    {
                        lstChats.ItemsSource = messageList;
                        lstChats.ScrollToFirst();
                    }
                    if (messageList.Count <= 4 && !IsAdmin && messageList.Where(x => x.UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value)).ToList().Count == 1 && messageList.First().UserId.Equals(LocalDBManager.Instance.GetDBSetting("email").Value))
                    {
                        var msg = "";
                        if (messageList.Any(x => x.UserId.ToLower().Equals(LocalDBManager.Instance.GetDBSetting("email").Value)))
                        {
                            if (DateTime.Now.DayOfWeek == DayOfWeek.Saturday || DateTime.Now.DayOfWeek == DayOfWeek.Sunday)
                                msg = $"Thanks {LocalDBManager.Instance.GetDBSetting("firstname").Value}. I'm not online at the moment, but you can expect a response by Monday evening.";
                            else
                                msg = $"Thanks { LocalDBManager.Instance.GetDBSetting("firstname").Value}. I'm not online at the moment, but you can expect a response by {DateTime.Now.ToString("hh:mm tt")} tomorrow.";
                        
                        messageList.Insert(0, new Messages()
                        {
                            Message = msg,
                            ProfileUrl = "",
                            Nickname = "Carl Juneau",
                            UserId = "<EMAIL>",
                            CreatedDate = DateTime.UtcNow//userMessage.CreatedAt
                        });
                        lstChats.ItemsSource = messageList;
                        lstChats.ScrollToFirst();
                        }
                    }
                });
                lstChats.ScrollToFirst();
            //});
            chatInput.MessageText = "";
            if (Device.RuntimePlatform.Equals(Device.Android) && messageList.Count > 1)
            {
                await Task.Delay(300);
                lstChats.ScrollTo(messageList[1], ScrollToPosition.End, false);
                lstChats.ScrollToFirst();

            }
        }
        private async void LoadJustSentSupportMessage()
        {
            CurrentLog.Instance.SupportChats = await DrMuscleRestClient.Instance.FetchChatBoxWithoutLoader(new ChatModel() { CreatedAt = DateTime.UtcNow, ReceiverId = AppThemeConstants.ChatReceiverId });
        }

        private void TxtMsg_TextChanged(object sender, TextChangedEventArgs e)
        {
            //btnSend.IsEnabled = e.NewTextValue.Length > 0 && CrossConnectivity.Current.IsConnected;
        }

        void Handle_ItemAppearing(object sender, Xamarin.Forms.ItemVisibilityEventArgs e)
        {
            var itemTypeObject = e.Item as Messages;
            try
            {
                if (messageList.Count == 0)
                    return;
                if (IsLoading == true)
                    return;
                if (IsLoadMore)
                    return;
                //if (messageList?.Count < 25)
                //    return;
                if (messageList.Last() == itemTypeObject)
                {
                    IsLoadMore = true;
                    GetMessages();
                    //Now you are at bottom of list. Add more items to the ObservableCollection.
                }

            }
            catch (Exception ex)
            {

            }
        }

        void Handle_ItemDisappearing(object sender, Xamarin.Forms.ItemVisibilityEventArgs e)
        {

        }

        public void OnListTapped(object sender, ItemTappedEventArgs e)
        {
            chatInput.UnFocusEntry();
        }
    }
}
