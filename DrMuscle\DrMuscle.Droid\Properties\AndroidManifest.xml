﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.drmaxmuscle.dr_max_muscle" android:versionName="2.7" android:installLocation="auto" android:largeHeap="true" xmlns:tools="http://schemas.android.com/tools" android:versionCode="2490">
	<uses-sdk android:minSdkVersion="21" android:targetSdkVersion="33" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
	<uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<uses-permission android:name="android.permission.REBOOT" />
	<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.BIND_CHOOSER_TARGET_SERVICE" />
	<uses-permission android:name="android.permission.BIND_DREAM_SERVICE" />
	<uses-permission android:name="android.permission.ACCESS_SURFACE_FLINGER" />
	<uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	<application android:label="Dr. Muscle" android:icon="@drawable/icon" android:resizeableActivity="false" android:allowBackup="false" android:usesCleartextTraffic="true" android:largeHeap="true" android:supportsRtl="false" tools:replace="android:supportsRtl">
		<meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@drawable/icon_notification" />
		<meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id" />
		<activity android:name="com.facebook.FacebookActivity" android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation" android:theme="@android:style/Theme.NoTitleBar.Fullscreen" tools:replace="android:theme" android:label="@string/app_name" />
		<activity android:name="com.facebook.CustomTabActivity" android:exported="true">
			<intent-filter>
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="@string/fb_login_protocol_scheme" />
			</intent-filter>
		</activity>
		<provider android:name="com.facebook.FacebookContentProvider" android:authorities="com.facebook.app.FacebookContentProvider1865252523754972" android:exported="true" />
		<receiver android:name="com.google.firebase.iid.FirebaseInstanceIdInternalReceiver" android:exported="true" />
		<receiver android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver" android:exported="true" android:permission="com.google.android.c2dm.permission.SEND">
			<intent-filter>
				<action android:name="com.google.android.c2dm.intent.RECEIVE" />
				<action android:name="com.google.android.c2dm.intent.REGISTRATION" />
				<category android:name="${applicationId}" />
			</intent-filter>
		</receiver>
		<provider android:name="android.support.v4.content.FileProvider" android:authorities="${applicationId}.fileprovider" android:exported="false" android:grantUriPermissions="true">
			<meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_paths" />
		</provider>
		<!--<meta-data android:name="com.google.android.wearable.beta.app" android:resource="@xml/wearable_app_desc" />-->
	</application>
</manifest>