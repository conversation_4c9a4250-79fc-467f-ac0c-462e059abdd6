﻿<?xml version="1.0" encoding="UTF-8" ?>
<pages:PopupPage
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    CloseWhenBackgroundIsClicked="True"
    xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView" xmlns:t="clr-namespace:DrMuscle.Layout"
    BackgroundColor="#99000000"
    xmlns:app="clr-namespace:DrMuscle.Constants"
    x:Class="DrMuscle.Views.CustomPopup">
    <StackLayout
        VerticalOptions="CenterAndExpand"
        HorizontalOptions="FillAndExpand"
        Margin="{OnPlatform Android='20,0',iOS='35,0'}"
        Padding="0"
        >
        <pancakeView:PancakeView
            IsVisible="false"
            x:Name="IOSFrame"
            IsClippedToBounds="true"
            OffsetAngle="90"
            CornerRadius="14"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            Margin="0"
            Padding="0,20,0,0"
            BackgroundColor="#E8EDF0">


            <StackLayout
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                Spacing="20"
                >
                <Label
                    Padding="0"
                    Margin="20,0"
                    x:Name="titleLabelIOS"
                    Text=""
                    VerticalOptions="StartAndExpand"
                    FontSize="19"
                    FontAttributes="Bold"
                    VerticalTextAlignment="Center"
                    TextColor="Black"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="FillAndExpand"
                     />
                <Label
                    Padding="0"
                    Margin="20,0"
                    VerticalOptions="StartAndExpand"
                    x:Name="bodyLabelIOS"
                    Text=""
                    FontSize="19"
                    VerticalTextAlignment="Center"
                    TextColor="Black"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="FillAndExpand"
                    />
                <StackLayout
                    BackgroundColor="#C8CED3"
                    Padding="0,1,0,0"
                    VerticalOptions="FillAndExpand"
                    HorizontalOptions="FillAndExpand"
                    Orientation="Horizontal"
                    Spacing="1">
                    <t:DrMuscleButton
                        Text=""
                        CornerRadius="0"
                        x:Name="cancelBtnIOS"
                        Padding="0"
                        FontSize="19"
                        FontAttributes="Bold"
                        HorizontalOptions="FillAndExpand"
                        Clicked="Cancel_Btn_ClickedIOS"
                        VerticalOptions="CenterAndExpand"
                        BackgroundColor="#E8EDF0"
        
                TextColor="#2185F4" />
                    <t:DrMuscleButton
                Text="Ok"
                Padding="0"
                        CornerRadius="0"
                x:Name="okBtnIOS"
                FontSize="19"
                HorizontalOptions="FillAndExpand"
                Clicked="OK_Btn_ClickedIOS"
                VerticalOptions="CenterAndExpand"
                BackgroundColor="#E8EDF0"
                TextColor="#2185F4" />
                </StackLayout>
            </StackLayout>
        </pancakeView:PancakeView>
        <pancakeView:PancakeView
            IsVisible="false"
            x:Name="AndroidFrame"
            IsClippedToBounds="true"
            OffsetAngle="90"
            CornerRadius="0"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            Margin="0"
            Padding="20,20,20,0"
            Style="{StaticResource PancakeViewStyleBlue}">


            <StackLayout
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                Spacing="20"
                >   
                <Label
                    Padding="0"
                    Margin="0"
                    x:Name="titleLabel"
                    Text=""
                    VerticalOptions="StartAndExpand"
                    FontSize="Medium"
                    FontAttributes="Bold"
                    VerticalTextAlignment="Center"
                    TextColor="White"
                    HorizontalTextAlignment="Start"
                    HorizontalOptions="FillAndExpand"
                     />
                <Label
                    Padding="0"
                    Margin="0"
                    VerticalOptions="StartAndExpand"
                    x:Name="bodyLabel"
                    Text=""
                    FontSize="Medium"
                    VerticalTextAlignment="Center"
                    TextColor="White"
                    HorizontalTextAlignment="Start"
                    HorizontalOptions="FillAndExpand"
                    />
                <StackLayout
                    VerticalOptions="FillAndExpand"
                    HorizontalOptions="EndAndExpand"
                    Orientation="Horizontal"
                    Spacing="10">
                    <t:DrMuscleButton
                        Text=""
                        WidthRequest="60"
                        x:Name="cancelBtn"
                        Padding="0"
                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        Clicked="Cancel_Btn_Clicked"
                        VerticalOptions="CenterAndExpand"
                        BackgroundColor="Transparent"
                
                        TextColor="White" />
                    <t:DrMuscleButton
                        Text="Ok"
                        WidthRequest="60"
                        Padding="0"
                        x:Name="okBtn"
                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        Clicked="OK_Btn_Clicked"
                        VerticalOptions="CenterAndExpand"
                        BackgroundColor="Transparent"
                        TextColor="White" />
                </StackLayout>
            </StackLayout>
        </pancakeView:PancakeView>
    </StackLayout>

</pages:PopupPage>