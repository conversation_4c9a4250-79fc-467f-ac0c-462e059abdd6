﻿<?xml version="1.0" encoding="UTF-8" ?>
<t:DrMusclePage xmlns="http://xamarin.com/schemas/2014/forms"
                x:Class="DrMuscle.Screens.Workouts.KenkoChooseYourWorkoutExercisePage"
                xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                xmlns:app="clr-namespace:DrMuscle.Constants"
                xmlns:local="clr-namespace:DrMuscle"
                xmlns:t="clr-namespace:DrMuscle.Layout"
                xmlns:constnats="clr-namespace:DrMuscle.Constants"
                xmlns:localize="clr-namespace:DrMuscle.Resx"
                xmlns:locali="clr-namespace:DrMuscle.Helpers"
                xmlns:cells="clr-namespace:DrMuscle.Cells"
                xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
                xmlns:effects="clr-namespace:DrMuscle.Effects"
                xmlns:converter="clr-namespace:DrMuscle.Converters"
                xmlns:heaer="clr-namespace:DrMuscle.Screens.Workouts"
                NavigationPage.HasNavigationBar="False"
                xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms" xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
                xmlns:controls="clr-namespace:DrMuscle.Controls"
                >
    <t:DrMusclePage.Resources>
        <ResourceDictionary>


            <DataTemplate x:Key="KenkoRegularTemplate" x:Name="RegularTemplate">
                <ViewCell Height="115" BindingContextChanged="OnBindingContextChanged">
                    <Grid IsClippedToBounds="True">

                        <pancakeView:PancakeView
                            Grid.Row="0" 
            Padding="0"
            IsClippedToBounds="true"
            OffsetAngle="90"
            CornerRadius="4,4,0,0"
                            Style="{StaticResource PancakeViewStyleBlue}"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand"
                        Margin="4,10,4,0">

                            <pancakeView:PancakeView.Triggers>
                                <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding IsFrameBackground}" Value="false">
                                    <Setter Property="IsVisible" Value="true" />
                                </DataTrigger>
                                <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding IsFrameBackground}" Value="true">
                                    <Setter Property="IsVisible" Value="false" />
                                </DataTrigger>
                            </pancakeView:PancakeView.Triggers>
                        </pancakeView:PancakeView>

                        <ffimageloading:CachedImage Grid.Row="0" Source="{Binding BodyPartId, Converter={StaticResource IdToTransBodyConverter}}" Aspect="Fill" Margin="4,10,4,0"/>
                        <Frame Margin="4,10,4,0" Grid.Row="0" HasShadow="False" CornerRadius="4" BackgroundColor="Transparent" HeightRequest="115" Padding="20,16">
                            <Frame.Triggers>
                                <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="true">
                                    <Setter Property="Margin" Value="8,10,8,0" />
                                    <Setter Property="BackgroundColor" Value="White" />
                                    <Setter Property="Padding" Value="15,5,5,10" />
                                </DataTrigger>

                                <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="false">
                                    <Setter Property="Margin" Value="4,10,4,0" />
                                    <Setter Property="Padding" Value="8,10,5,10" />
                                    <Setter Property="BackgroundColor" Value="Transparent" />
                                </DataTrigger>

                            </Frame.Triggers>
                            <StackLayout>
                                <StackLayout Orientation="Horizontal" VerticalOptions="CenterAndExpand">
                                    <StackLayout.Triggers>
                                        <DataTrigger TargetType="StackLayout" Binding="{Binding IsFrameBackground}" Value="false">
                                            <Setter Property="IsVisible" Value="true" />
                                        </DataTrigger>

                                        <DataTrigger TargetType="StackLayout" Binding="{Binding IsFrameBackground}" Value="true">
                                            <Setter Property="IsVisible" Value="false" />
                                        </DataTrigger>
                                    </StackLayout.Triggers>
                                    <Image Source="done2.png" WidthRequest="18" Aspect="AspectFit" HorizontalOptions="Start" VerticalOptions="FillAndExpand" IsVisible="{Binding IsFinished}" Margin="7,0,0,0"/>
                                    <ffimageloading:CachedImage Source="{Binding BodyPartId, Converter={StaticResource IdToBodyConverter}}" HeightRequest="90" WidthRequest="65" Aspect="AspectFit">

                                    </ffimageloading:CachedImage>
                                    <StackLayout Spacing="0" VerticalOptions="Center" Margin="0,0,0,8">
                                        <controls:AutoSizeLabel HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#FFFFFF" FontSize="19" MaxLines="3"  FontAttributes="Bold" >
                                            <controls:AutoSizeLabel.FormattedText>
                                                <FormattedString>
                                                    <Span Text="{Binding CountNo}" />
                                                    <Span Text="&#8211;" />
                                                    <Span Text="{Binding Label}" />
                                                </FormattedString>
                                            </controls:AutoSizeLabel.FormattedText>
                                            <controls:AutoSizeLabel.Triggers>
                                                <DataTrigger TargetType="controls:AutoSizeLabel" Binding="{Binding IsNextExercise}" Value="false">
                                                    <Setter Property="TextColor" Value="White" />
                                                </DataTrigger>

                                                <DataTrigger TargetType="controls:AutoSizeLabel" Binding="{Binding IsNextExercise}" Value="true">
                                                    <Setter Property="TextColor" Value="#97D2F3" />
                                                </DataTrigger>
                                            </controls:AutoSizeLabel.Triggers>
                                        </controls:AutoSizeLabel>

                                        <!--<Label Text="{Binding CountNo}" HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#AAFFFFFF" FontSize="16" />-->
                                    </StackLayout>
                                    <!---->
                                    <!---->
                                    <!--                           <StackLayout Spacing="2" Orientation="Horizontal" VerticalOptions="Center" Margin="0,0,0,8">
                                        <controls:AutoSizeLabel Text="{Binding CountNo}" HorizontalOptions="StartAndExpand" VerticalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#AAFFFFFF" FontSize="16" />
                                        <controls:AutoSizeLabel Text="{Binding Label}" HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#FFFFFF" FontSize="18" MaxLines="3"  FontAttributes="Bold" />
                                    </StackLayout>-->
                                    <Image Source="swap.png" WidthRequest="10" Aspect="AspectFit" HorizontalOptions="Start" IsVisible="{Binding IsSwapTarget}" Margin="3,6" VerticalOptions="Start" />
                                    <Label HorizontalOptions="StartAndExpand" />
                                    <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="End" Spacing="0">

                                        <!--<t:DrMuscleButton Clicked="OnSwap" Text="{Binding [Swap].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextSwapButton}" TextColor="#0C2432" BackgroundColor="#ECFF92"/>
                                        <t:DrMuscleButton Clicked="OnRestore" Text="{Binding [Restore].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextRestoreButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnDeload" Text="Deload" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextDeloadButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnChallenge" Text="{Binding [Challenge].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextChallengeButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnReset" Text="{Binding [Settings].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextSettingsButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnContextVideo" Text="{Binding [Video].Value, Mode=OneWay,  Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" Image="Play.png" ContentLayout="Top,0" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextVideoButton}" TextColor="#97D2F3" BackgroundColor="Transparent" />
                                        

                                           
                                        </t:DrMuscleButton>-->
                                        <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" Image="menu_blue.png" WidthRequest="{OnPlatform Android=60, iOS=50}" TextColor="#97D2F3" ContentLayout="Top,0"  Text="" HorizontalOptions="End" VerticalOptions="Center" Margin="0,0,-3,0" Style="{StaticResource ItemContextVideoButton}"  BackgroundColor="Transparent" />


                                        <controls:ContextMenuButton
                                            x:Name="MenuButton"
                                            Margin="0,0,0,0"
                                            WidthRequest="1"
                                            HeightRequest="1"
                                            ItemsContainerHeight="240"
                                            ItemsContainerWidth="240"
                                            RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=0, Constant=0}"
                                            RelativeLayout.XConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=0, Constant=0}"
                                            >
                                            <controls:ContextMenuButton.Items>
                                                <x:Array Type="{x:Type MenuItem}">

                                                </x:Array>
                                            </controls:ContextMenuButton.Items>
                                        </controls:ContextMenuButton>




                                    </StackLayout>
                                    <StackLayout.GestureRecognizers>

                                        <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="CellHeaderTapped" />
                                    </StackLayout.GestureRecognizers>
                                </StackLayout>
                                <StackLayout x:Name="StackAddExercise" IsVisible="{Binding IsAddExercise}" Spacing="0">
                                    <Grid Margin="0,9,0,0">
                                        <t:DrMuscleButton x:Name="BtnAddExercise" Grid.Row="0" Grid.Column="0" TextColor="White" HorizontalOptions="FillAndExpand" Text="Add exercise" FontSize="20" FontAttributes="Bold" BackgroundColor="Transparent" Clicked="AddExerciseButton_Clicked" HeightRequest="50">
                                            <t:DrMuscleButton.Triggers>
                                                <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="false">
                                                    <Setter Property="TextColor" Value="#195377" />
                                                </DataTrigger>
                                            </t:DrMuscleButton.Triggers>
                                        </t:DrMuscleButton>
                                    </Grid>
                                </StackLayout>
                                <StackLayout x:Name="StackSets" IsVisible="{Binding IsFinishWorkoutExe}" Spacing="0">
                                    <Grid Margin="0,9,0,0">
                                        <Image Margin="-2,0" Source="finishSet_orange.png" Grid.Row="0" Grid.Column="0" HorizontalOptions="FillAndExpand" Aspect="AspectFill" IsVisible="{Binding IsFinished}" />
                                        <t:DrMuscleButton x:Name="BtnFinishWorkout" Grid.Row="0" Grid.Column="0" TextColor="White" HorizontalOptions="FillAndExpand" Text="{Binding CountNo}" FontSize="20" FontAttributes="Bold" BackgroundColor="Transparent" Clicked="SaveWorkoutButton_Clicked" HeightRequest="50">
                                            <t:DrMuscleButton.Triggers>
                                                <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="false">
                                                    <Setter Property="TextColor" Value="#195377" />
                                                </DataTrigger>
                                                <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="true">
                                                    <Setter Property="TextColor" Value="White" />
                                                </DataTrigger>
                                            </t:DrMuscleButton.Triggers>
                                        </t:DrMuscleButton>
                                    </Grid>
                                </StackLayout>
                            </StackLayout>
                        </Frame>
                    </Grid>
                </ViewCell>
            </DataTemplate>

            <DataTemplate x:Key="KenkoHeaderTemplate" x:Name="HeaderTemplate">
                <ViewCell  Height="90">
                    <Frame Margin="8,10,8,0" HasShadow="False" CornerRadius="4" BackgroundColor="Red" HeightRequest="90" Padding="15,10,15,10">
                        <StackLayout x:Name="StackSets" Spacing="0">
                            <Grid Margin="0,9,0,0">
                                <Image Margin="-2,0" Source="finishSet_orange.png" Grid.Row="0" Grid.Column="0" HorizontalOptions="FillAndExpand" Aspect="AspectFill" />
                                <t:DrMuscleButton x:Name="BtnFinishWorkout"  Grid.Row="0" Grid.Column="0" TextColor="White"  FontSize="20" HorizontalOptions="FillAndExpand" Text="{Binding CountNo}" FontAttributes="Bold" HeightRequest="50" BackgroundColor="Transparent" Clicked="SaveWorkoutButton_Clicked">
                                </t:DrMuscleButton>
                            </Grid>
                        </StackLayout>
                    </Frame>
                </ViewCell>
            </DataTemplate>
            <heaer:KenkoHeaderDataTemplateSelector x:Key="kenkoHeaderDataTemplateSelector" RegularDateTemplate="{StaticResource KenkoRegularTemplate}" FooterExerciseTemplate="{StaticResource KenkoHeaderTemplate}">
            </heaer:KenkoHeaderDataTemplateSelector>
            <cells:SetDataTemplateSelector x:Key="SetDataTemplateSelector">
            </cells:SetDataTemplateSelector>
            <converter:IdToBodyPartConverter x:Key="IdToBodyConverter" />
            <converter:IdToTransparentBodyPartConverter x:Key="IdToTransBodyConverter" />
        </ResourceDictionary>
    </t:DrMusclePage.Resources>




    <Grid x:Name="NavGrid" BackgroundColor="#D8D8D8" Padding="0,0,0,8">
        <Grid.RowDefinitions>
            <RowDefinition x:Name="StatusBarHeight" Height="20" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />

        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Image Source="nav.png" Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" VerticalOptions="Start" Aspect="AspectFill" Grid.RowSpan="3" />

        <StackLayout Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Spacing="8" HorizontalOptions="FillAndExpand" VerticalOptions="Start" Orientation="Horizontal">
            <StackLayout.Padding>
                <OnPlatform x:TypeArguments="Thickness" Android="15,0,20,0" iOS="8,0,8,0" />
            </StackLayout.Padding>
            <ImageButton Source="android_back.png" Aspect="AspectFit" BackgroundColor="Transparent" HorizontalOptions="Start" VerticalOptions="Center" Clicked="Back_Clicked" >
                <ImageButton.HeightRequest>
                    <OnPlatform x:TypeArguments="x:Double" Android="30" iOS="40" />
                </ImageButton.HeightRequest>
            </ImageButton>
            <Label x:Name="LblWorkoutName" HorizontalOptions="StartAndExpand" TextColor="White" VerticalOptions="Center" VerticalTextAlignment="Center" LineBreakMode="TailTruncation" Style="{StaticResource BoldLabelStyle}" FontSize="24" />

        </StackLayout>
        <StackLayout Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" VerticalOptions="Start" Orientation="Horizontal"
                                     x:Name="StackHeader"
                                     effects:TooltipEffect.Text="Go back to your previous exercise"
                                effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Bottom"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="False">

            <StackLayout.Padding>
                <OnPlatform x:TypeArguments="Thickness" Android="15,5,20,8" iOS="8,-3,8,8" />
            </StackLayout.Padding>
            <StackLayout HorizontalOptions="StartAndExpand" Spacing="0">
                <Label Text="In progress" HorizontalOptions="Start" VerticalTextAlignment="Center" VerticalOptions="Center" HorizontalTextAlignment="End" FontSize="16" TextColor="#AAFFFFFF" >
                    <Label.Margin>
                        <OnPlatform x:TypeArguments="Thickness" Android="0" iOS="10,0,0,0" />
                    </Label.Margin>
                </Label>
                <t:DrMuscleButton Clicked="NewTapped" x:Name="BtnEditWorkout" Image="ic_edit_white.png" HeightRequest="30" Text="Edit workout" TextColor="#97D2F3" BackgroundColor="Transparent" HorizontalOptions="End" VerticalOptions="Center" Padding="0,2" FontSize="16">
                    <t:DrMuscleButton.WidthRequest>
                        <OnPlatform x:TypeArguments="x:Double" iOS="150" Android="130" />
                    </t:DrMuscleButton.WidthRequest>
                    <t:DrMuscleButton.Margin>
                        <OnPlatform x:TypeArguments="Thickness" iOS="-5,0,0,0" Android="0" />
                    </t:DrMuscleButton.Margin>
                </t:DrMuscleButton>
            </StackLayout>
            <StackLayout x:Name="ImgPlate" BackgroundColor="Transparent" Padding="0"                                 effects:TooltipEffect.Text="Tap here for bar and plates"
                                effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Bottom"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="False">
                <Image Source="plate.png" BackgroundColor="Transparent" HeightRequest="40" WidthRequest="44" Aspect="AspectFit" HorizontalOptions="End" VerticalOptions="Start"
                                   
                                   >
                    <Image.GestureRecognizers>
                        <TapGestureRecognizer Tapped="PlateTapped" />
                    </Image.GestureRecognizers>
                </Image>
            </StackLayout>
            <t:DrMuscleButton x:Name="BtnTimer" Image="stopwatch.png" HeightRequest="40" TextColor="White" BackgroundColor="Transparent" HorizontalOptions="End" VerticalOptions="Start" Clicked="TimerTapped" Padding="0" Margin="0" FontSize="24">
                <t:DrMuscleButton.WidthRequest>
                    <OnPlatform x:TypeArguments="x:Double" Android="40" iOS="48" />
                </t:DrMuscleButton.WidthRequest>
            </t:DrMuscleButton>
        </StackLayout>


        <!--<t:DrMuscleListViewCache x:Name="ExerciseListView"
                                                 
                                                  Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"  Margin="0,7,0,0" HasUnevenRows="True" BackgroundColor="Transparent" SeparatorColor="Transparent" VerticalOptions="Fill" HorizontalOptions="Fill" SeparatorVisibility="None" IsGroupingEnabled="True" Header="{Binding}" ItemTemplate="{StaticResource SetDataTemplateSelector}" GroupHeaderTemplate="{StaticResource kenkoHeaderDataTemplateSelector}" RowHeight="-1" ios:ListView.GroupHeaderStyle="Grouped"
                                                 >


        </t:DrMuscleListViewCache>-->
        <ScrollView x:Name="ExerciseListScroll" Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" >
            <StackLayout x:Name="ExerciseListView" BindableLayout.ItemsSource="{Binding exerciseItems}"  Margin="0,0,0,0" Spacing="0">
                <BindableLayout.ItemTemplate>
                    <DataTemplate>
                        <StackLayout Spacing="0">
                            <Grid IsClippedToBounds="True" HeightRequest="115" BindingContextChanged="OnBindingContextChanged">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="CellHeaderTapped" />
                                </Grid.GestureRecognizers>
                                <pancakeView:PancakeView
                            Grid.Row="0" 
            Padding="0"
            IsClippedToBounds="true"
            OffsetAngle="90"
            CornerRadius="4,4,0,0"
                        Style="{StaticResource PancakeViewStyleBlue}"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand"
                        Margin="4,10,4,0">

                                    <pancakeView:PancakeView.Triggers>
                                        <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding IsFrameBackground}" Value="false">
                                            <Setter Property="IsVisible" Value="true" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding IsFrameBackground}" Value="true">
                                            <Setter Property="IsVisible" Value="false" />
                                        </DataTrigger>
                                    </pancakeView:PancakeView.Triggers>
                                </pancakeView:PancakeView>

                                <ffimageloading:CachedImage Grid.Row="0" Source="{Binding BodyPartId, Converter={StaticResource IdToTransBodyConverter}}" Aspect="Fill" Margin="4,10,4,0"/>
                                <Frame Margin="4,10,4,0" Grid.Row="0" HasShadow="False" CornerRadius="4" BackgroundColor="Transparent" HeightRequest="115" Padding="20,16">
                                    <Frame.Triggers>
                                        <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="true">
                                            <Setter Property="Margin" Value="8,10,8,0" />
                                            <Setter Property="BackgroundColor" Value="White" />
                                            <Setter Property="Padding" Value="15,5,5,10" />
                                        </DataTrigger>

                                        <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="false">
                                            <Setter Property="Margin" Value="4,10,4,0" />
                                            <Setter Property="Padding" Value="8,10,5,10" />
                                            <Setter Property="BackgroundColor" Value="Transparent" />
                                        </DataTrigger>

                                    </Frame.Triggers>
                                    <StackLayout>
                                        <StackLayout Orientation="Horizontal" VerticalOptions="CenterAndExpand">
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding IsFrameBackground}" Value="false">
                                                    <Setter Property="IsVisible" Value="true" />
                                                </DataTrigger>

                                                <DataTrigger TargetType="StackLayout" Binding="{Binding IsFrameBackground}" Value="true">
                                                    <Setter Property="IsVisible" Value="false" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <Image Source="done2.png" WidthRequest="18" Aspect="AspectFit" HorizontalOptions="Start" VerticalOptions="FillAndExpand" IsVisible="{Binding IsFinished}" Margin="7,0,0,0"/>
                                            <ffimageloading:CachedImage Source="{Binding BodyPartId, Converter={StaticResource IdToBodyConverter}}" HeightRequest="90" WidthRequest="65" Aspect="AspectFit">

                                            </ffimageloading:CachedImage>
                                            <StackLayout Spacing="0" VerticalOptions="Center" Margin="0,0,0,8">
                                                <controls:AutoSizeLabel HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#FFFFFF" FontSize="19" MaxLines="3"  FontAttributes="Bold" >
                                                    <controls:AutoSizeLabel.FormattedText>
                                                        <FormattedString>
                                                            <Span Text="{Binding CountNo}" />
                                                            <Span Text="&#8211;" />
                                                            <Span Text="{Binding Label}" />
                                                        </FormattedString>
                                                    </controls:AutoSizeLabel.FormattedText>
                                                    <controls:AutoSizeLabel.Triggers>
                                                        <DataTrigger TargetType="controls:AutoSizeLabel" Binding="{Binding IsNextExercise}" Value="false">
                                                            <Setter Property="TextColor" Value="White" />
                                                        </DataTrigger>

                                                        <DataTrigger TargetType="controls:AutoSizeLabel" Binding="{Binding IsNextExercise}" Value="true">
                                                            <Setter Property="TextColor" Value="#97D2F3" />
                                                        </DataTrigger>
                                                    </controls:AutoSizeLabel.Triggers>
                                                </controls:AutoSizeLabel>

                                                <!--<Label Text="{Binding CountNo}" HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#AAFFFFFF" FontSize="16" />-->
                                            </StackLayout>
                                            <!---->
                                            <!---->
                                            <!--                           <StackLayout Spacing="2" Orientation="Horizontal" VerticalOptions="Center" Margin="0,0,0,8">
                                        <controls:AutoSizeLabel Text="{Binding CountNo}" HorizontalOptions="StartAndExpand" VerticalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#AAFFFFFF" FontSize="16" />
                                        <controls:AutoSizeLabel Text="{Binding Label}" HorizontalOptions="Start" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="#FFFFFF" FontSize="18" MaxLines="3"  FontAttributes="Bold" />
                                    </StackLayout>-->
                                            <Image Source="swap.png" WidthRequest="10" Aspect="AspectFit" HorizontalOptions="Start" IsVisible="{Binding IsSwapTarget}" Margin="3,6" VerticalOptions="Start" />
                                            <Label HorizontalOptions="StartAndExpand" />
                                            <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="End" Spacing="0">

                                                <!--<t:DrMuscleButton Clicked="OnSwap" Text="{Binding [Swap].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextSwapButton}" TextColor="#0C2432" BackgroundColor="#ECFF92"/>
                                        <t:DrMuscleButton Clicked="OnRestore" Text="{Binding [Restore].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextRestoreButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnDeload" Text="Deload" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextDeloadButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnChallenge" Text="{Binding [Challenge].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextChallengeButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnReset" Text="{Binding [Settings].Value, Mode=OneWay, Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextSettingsButton}" TextColor="#0C2432" BackgroundColor="#ECFF92" />
                                        <t:DrMuscleButton Clicked="OnContextVideo" Text="{Binding [Video].Value, Mode=OneWay,  Source={x:Static locali:ResourceLoader.Instance}}" CommandParameter="{Binding .}" Image="Play.png" ContentLayout="Top,0" IsVisible="false" HorizontalOptions="End" VerticalOptions="Center" Style="{StaticResource ItemContextVideoButton}" TextColor="#97D2F3" BackgroundColor="Transparent" />
                                        

                                           
                                        </t:DrMuscleButton>-->
                                                <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" Image="menu_blue.png" WidthRequest="{OnPlatform Android=60, iOS=50}" TextColor="#97D2F3" ContentLayout="Top,0"  Text="" HorizontalOptions="End" VerticalOptions="Center" Margin="0,0,-3,0" Style="{StaticResource ItemContextVideoButton}"  BackgroundColor="Transparent" />


                                                <controls:ContextMenuButton
                                            x:Name="MenuButton"
                                            Margin="0,0,0,0"
                                            WidthRequest="1"
                                            HeightRequest="1"
                                            ItemsContainerHeight="240"
                                            ItemsContainerWidth="240"
                                            RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent, Property=Height, Factor=0, Constant=0}"
                                            RelativeLayout.XConstraint="{ConstraintExpression Type=RelativeToParent, Property=Width, Factor=0, Constant=0}"
                                            >
                                                    <controls:ContextMenuButton.Items>
                                                        <x:Array Type="{x:Type MenuItem}">

                                                        </x:Array>
                                                    </controls:ContextMenuButton.Items>
                                                </controls:ContextMenuButton>




                                            </StackLayout>
                                            <StackLayout.GestureRecognizers>

                                                <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="CellHeaderTapped" />
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                        <StackLayout x:Name="StackAddExercise" IsVisible="{Binding IsAddExercise}" Spacing="0">
                                            <Grid Margin="0,9,0,0">
                                                <t:DrMuscleButton x:Name="BtnAddExercise" Grid.Row="0" Grid.Column="0" TextColor="White" HorizontalOptions="FillAndExpand" Text="Add exercise" FontSize="20" FontAttributes="Bold" BackgroundColor="Transparent" Clicked="AddExerciseButton_Clicked" HeightRequest="50">
                                                    <t:DrMuscleButton.Triggers>
                                                        <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="false">
                                                            <Setter Property="TextColor" Value="#195377" />
                                                        </DataTrigger>
                                                    </t:DrMuscleButton.Triggers>
                                                </t:DrMuscleButton>
                                            </Grid>
                                        </StackLayout>
                                        <StackLayout x:Name="StackSets" IsVisible="{Binding IsFinishWorkoutExe}" Spacing="0">
                                            <Grid Margin="0,9,0,0">
                                                <Image Margin="-2,0" Source="finishSet_orange.png" Grid.Row="0" Grid.Column="0" HorizontalOptions="FillAndExpand" Aspect="AspectFill" IsVisible="{Binding IsFinished}" />
                                                <t:DrMuscleButton x:Name="BtnFinishWorkout" Grid.Row="0" Grid.Column="0" TextColor="White" HorizontalOptions="FillAndExpand" Text="{Binding CountNo}" FontSize="20" FontAttributes="Bold" BackgroundColor="Transparent" Clicked="SaveWorkoutButton_Clicked" HeightRequest="50">
                                                    <t:DrMuscleButton.Triggers>
                                                        <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="false">
                                                            <Setter Property="TextColor" Value="#195377" />
                                                        </DataTrigger>
                                                        <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding IsFinished}" Value="true">
                                                            <Setter Property="TextColor" Value="White" />
                                                        </DataTrigger>
                                                    </t:DrMuscleButton.Triggers>
                                                </t:DrMuscleButton>
                                            </Grid>
                                        </StackLayout>
                                    </StackLayout>
                                </Frame>
                            </Grid>
                            <pancakeView:PancakeView
            Padding="0"
            Margin="4,0,4,0"
            IsClippedToBounds="true"
            HorizontalOptions="FillAndExpand"
            OffsetAngle="90"
            VerticalOptions="FillAndExpand"
                    Style="{StaticResource PancakeViewStyleBlue}"
            CornerRadius="0">

                                <StackLayout x:Name="bind" BindableLayout.ItemsSource="{Binding .}" Spacing="0" BackgroundColor="Transparent">
                                    
                                    <BindableLayout.ItemTemplateSelector>
                                        <cells:SetBindingTemplateSelector />
                                    </BindableLayout.ItemTemplateSelector>

                                </StackLayout>
                            </pancakeView:PancakeView>
                        </StackLayout>
                    </DataTemplate>
                </BindableLayout.ItemTemplate>
            </StackLayout>
        </ScrollView>
    </Grid>




</t:DrMusclePage>
