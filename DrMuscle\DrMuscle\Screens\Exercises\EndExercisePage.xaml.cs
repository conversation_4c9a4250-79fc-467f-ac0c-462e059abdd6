﻿using DrMuscleWebApiSharedModel;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Globalization;
using Xamarin.Forms;
using Acr.UserDialogs;
using DrMuscle.Layout;
using DrMuscle.Helpers;
using DrMuscle.Screens.Workouts;
using DrMuscle.Resx;
using DrMuscle.Constants;
using DrMuscle.Dependencies;
using DrMuscle.Entity;
using Xamarin.Essentials;
using DrMuscle.Screens.User.OnBoarding;
using OxyPlot.Annotations;
using DrMuscle.Message;
using DrMuscle.Services;
using Rg.Plugins.Popup.Services;
using System.Threading;
using Microcharts;
using SkiaSharp;
using System.Reflection;

namespace DrMuscle.Screens.Exercises
{
    public partial class EndExercisePage : DrMusclePage
    {
        List<OneRMModel> _lastWorkoutLog = new List<OneRMModel>();
        private Dictionary<double, string> IndexToDateLabel = new Dictionary<double, string>();
        string strFacebook = "";
        bool ShouldAnimate = false;
        bool isEstimated = false;
        private decimal _userBodyWeight = 0;

        public EndExercisePage()
        {
            InitializeComponent();

            LearnMoreButton.Clicked += (sender, e) =>
            {
                Device.OpenUri(new Uri("http://drmuscleapp.com/news/deload/"));
            };
            RefreshLocalized();

            NextExerciseButton.Clicked += NextExerciseButton_Clicked;
            ShareWithFBButton.Clicked += ShareWithFBButton_Clicked;
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });

            MessagingCenter.Subscribe<ReceivedWatchMessage>(this, "ReceivedWatchMessage", (obj) =>
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    if (obj.PhoneToWatchModel.WatchMessageType == WatchMessageType.NextExercise)
                        NextExerciseButton_Clicked(NextExerciseButton, EventArgs.Empty);
                });

            });
        }

        public decimal ComputeOneRM(decimal weight, int reps)
        {
            // Mayhew
            //return (100 * weight) / (decimal)(52.2 + 41.9 * Math.Exp(-0.055 * reps));
            // Epey
            return (decimal)(AppThemeConstants.Coeficent * reps) * weight + weight;
        }

        private void RefreshLocalized()
        {
            LearnMoreButton.Text = AppResources.LearnMoreAboutDeloads;
            NextExerciseButton.Text = AppResources.NextExercise;
        }

        private async void NextExerciseButton_Clicked(object sender, EventArgs e)
        {
            //await PagesFactory.PopAsync();
            //await PagesFactory.PopAsync();
            //await PagesFactory.PopAsync();
            ShouldAnimate = false;
            if (!CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage"))
            {
                if (((App)Application.Current).UserWorkoutContexts.workouts != null) ;
                {
                    ((App)Application.Current).UserWorkoutContexts.workouts.LastWorkoutDate = DateTime.UtcNow;
                    ((App)Application.Current).UserWorkoutContexts.SaveContexts();
                }
            }
            CurrentLog.Instance.IsFromEndExercise = false;
            if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("ChooseYourDrMuscleExercisePage"))
                await PagesFactory.PushAsync<ChooseYourDrMuscleExercisePage>();
            else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("ChooseYourCustomExercisePage"))
                await PagesFactory.PushAsync<ChooseYourCustomExercisePage>();
            else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisePage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisesView") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("KenkoSingleExercisePage"))

            {
                if (Device.RuntimePlatform == Device.Android)
                {
                    await PagesFactory.PushAsyncWithoutBefore<AllExercisePage>();
                }
                else
                    await PagesFactory.PushAsync<AllExercisePage>();
            }
            else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage"))
            {
                CurrentLog.Instance.IsDemoRunningStep1 = true;

                if (!CurrentLog.Instance.IsDemoRunningStep2)
                    await PagesFactory.PopToRootAsync();
                else if (CurrentLog.Instance.IsDemoRunningStep2)
                {
                    await PagesFactory.PopToRootAsync();
                    //CurrentLog.Instance.IsDemoRunningStep2 = false;
                }
            }
            else
            {
                CurrentLog.Instance.IsFromEndExercise = true;

                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(100);
                if (CurrentLog.Instance.IsFinishedWorkoutWithExercise)
                {
                    CurrentLog.Instance.IsFinishedWorkoutWithExercise = false;
                    MessagingCenter.Send<EndExercisePage>(this, "EndExercisePage");
                    return;
                }
                PagesFactory.PopAsync();
            }
        }

        public override async void OnBeforeShow()
        {
            base.OnBeforeShow();
            DependencyService.Get<IFirebase>().SetScreenName("end_exercise_page");
            isEstimated = false;
            IconResultImage.Source = "up_arrow.png";
            lblResult1.IsVisible = true;
            lblResult2.IsVisible = true;
            lblResult21.IsVisible = true;
            lblResult3.IsVisible = true;
            lblResult4.IsVisible = true;
            lblResult6.IsVisible = true;
            lblLearnMore1.IsVisible = false;
            lblLearnMore2.IsVisible = false;
            lblResult3.Text = "";
            lblResult21.Text = "";
            lblResult4.Text = "";
            bool isTimeBased = false;// CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased;
            //plotView.Model = null;
            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
            MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NextExercise, SetModel = new WorkoutLogSerieModelRef() }, "SendWatchMessage");

            if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage"))
            {
                NextExerciseButton.Text = "Finish workout";
                ShouldAnimate = true;
                animate(NextExerciseButton);
            }
            else
                NextExerciseButton.Text = AppResources.NextExercise;

            if (CurrentLog.Instance.IsFinishedWorkoutWithExercise && !CurrentLog.Instance.IsMobilityStarted)
            {
                NextExerciseButton.Text = "Workout summary";
            }
            try
            {
                if (CurrentLog.Instance.Exercise1RM.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id) && CurrentLog.Instance.LastSerieModelList != null && CurrentLog.Instance.LastSerieModelList.Count > 0)
                {
                    _lastWorkoutLog = CurrentLog.Instance.Exercise1RM[CurrentLog.Instance.ExerciseLog.Exercice.Id];
                    if (_lastWorkoutLog != null)
                    {
                        List<decimal> listOf1Rm = new List<decimal>();
                        foreach (var item in CurrentLog.Instance.LastSerieModelList)
                        {
                            listOf1Rm.Add(ComputeOneRM(item.Weight.Kg, item.Reps));
                        }
                        _lastWorkoutLog.Remove(_lastWorkoutLog.Last());
                        _lastWorkoutLog.Insert(0, new OneRMModel()
                        {
                            ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id,
                            OneRMDate = DateTime.Now,
                            OneRM = new MultiUnityWeight(listOf1Rm.Max(), "kg"),
                            LastLogDate = DateTime.Now
                        });
                    }
                }
                else
                {

                    _lastWorkoutLog = await DrMuscleRestClient.Instance.GetOneRMForExercise(
                        new GetOneRMforExerciseModel()
                        {
                            Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                            Massunit = LocalDBManager.Instance.GetDBSetting("massunit").Value,
                            ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id
                        }
                    );

                }
                if (CurrentLog.Instance.ExerciseLog.Exercice.IsWeighted)
                {

                    if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                        _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
                    foreach (var item in _lastWorkoutLog)
                    {
                        item.OneRM = new MultiUnityWeight(ComputeOneRM(item.Weight.Kg + _userBodyWeight, item.Reps), "kg");
                    }
                }

                var chartSerie = new ChartSerie() { Name = AppResources.MAXSTRENGTHESTIMATELAST3WORKOUTS.ToLower().FirstCharToUpper(), Color = SKColor.Parse("#38418C") };
                List<ChartSerie> chartSeries = new List<ChartSerie>();

                List<ChartEntry> entries = new List<ChartEntry>();


                //var plotModel = new PlotModel
                //{
                //    Title = AppResources.MAXSTRENGTHESTIMATELAST3WORKOUTS.ToLower().FirstCharToUpper(),
                //    //Subtitle = "for the 3 last workouts",
                //    Background = OxyColors.Transparent,
                //    PlotAreaBackground = OxyColors.Transparent,
                //    TitleColor = OxyColor.Parse("#23253A"),
                //    TitleFontSize = 15,
                //    TitleFontWeight = FontWeights.Bold,
                //    PlotAreaBorderColor = OxyColor.Parse("#23253A"),

                //};

                //double minY;
                //double maxY;

                //switch (LocalDBManager.Instance.GetDBSetting("massunit").Value)
                //{
                //    default:
                //    case "kg":
                //        minY = (double)(Math.Floor(_lastWorkoutLog.Min(o => o.OneRM.Kg) / 10) * 10) - 100;
                //        maxY = (double)(Math.Ceiling(_lastWorkoutLog.Max(o => o.OneRM.Kg) / 10) * 10) + 100;
                //        break;
                //    case "lb":
                //        minY = (double)(Math.Floor(_lastWorkoutLog.Min(o => o.OneRM.Lb) / 10) * 10) - 100;
                //        maxY = (double)(Math.Ceiling(_lastWorkoutLog.Max(o => o.OneRM.Lb) / 10) * 10) + 100;
                //        break;
                //}

                //LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = minY - 5, Maximum = maxY + 5, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A") };
                //LinearAxis xAxis = new LinearAxis { Position = AxisPosition.Bottom, AxislineColor = OxyColor.Parse("#23253A"), ExtraGridlineColor = OxyColor.Parse("#23253A"), MajorGridlineColor = OxyColor.Parse("#23253A"), MinorGridlineColor = OxyColor.Parse("#23253A"), TextColor = OxyColor.Parse("#23253A"), TicklineColor = OxyColor.Parse("#23253A"), TitleColor = OxyColor.Parse("#23253A"), MinimumMajorStep = 0.3, MinorStep = 0.5, MajorStep = 0.5 };
                //xAxis.LabelFormatter = _formatter;

                //xAxis.MinimumPadding = 0.05;
                //xAxis.MaximumPadding = 0.1;
                //xAxis.IsPanEnabled = false;
                //xAxis.IsZoomEnabled = false;
                //xAxis.Minimum = 0.5;
                //xAxis.Maximum = 3.5;


               
                //yAxis.IsAxisVisible = false;
                //yAxis.IsPanEnabled = false;
                //yAxis.IsZoomEnabled = false;

                //IndexToDateLabel.Clear();
                //IndexToDateLabel.Add(xAxis.Minimum, "");
                //IndexToDateLabel.Add(xAxis.Maximum, "");

                //plotModel.Axes.Add(yAxis);
                //plotModel.Axes.Add(xAxis);


                //var s1 = new LineSeries()
                //{
                //    Color = OxyColor.Parse("#38418C"),
                //    MarkerType = MarkerType.Circle,
                //    MarkerSize = 6,
                //    MarkerStroke = OxyColor.Parse("#38418C"),
                //    MarkerFill = OxyColor.Parse("#38418C"),
                //    MarkerStrokeThickness = 1,
                //    LabelFormatString = "{1:0}",
                //    FontSize = 15,
                //    TextColor = OxyColor.Parse("#38418C"),

                //};
                //IndexToDateLabel.Clear();
                int i = 1;
                foreach (OneRMModel m in _lastWorkoutLog.OrderBy(w => w.OneRMDate))
                {
                    if (i == 2 && !m.IsAllowDelete)
                        isEstimated = true;
                    //if (!m.IsAllowDelete)
                    //    yAxis.Minimum = -120;
                    switch (LocalDBManager.Instance.GetDBSetting("massunit").Value)
                    {
                        default:
                        case "kg":

                            if (!m.IsAllowDelete)
                                entries.Add(new ChartEntry(0) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = "0" });
                            else
                            {
                                var val = (float)Math.Round(m.OneRM.Kg);
                                entries.Add(new ChartEntry(val) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = val.ToString() });
                                //var val = (float)Math.Round(inKg ? data.Average.Kg : data.Average.Lb);
                                //s1.Points.Add(new DataPoint(i, Convert.ToDouble(m.OneRM.Kg)));
                            }
                            break;
                        case "lb":
                            if (!m.IsAllowDelete)
                                entries.Add(new ChartEntry(0) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = "0" });
                            else
                            {
                                var val = (float)Math.Round(m.OneRM.Lb);
                                entries.Add(new ChartEntry(val) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = val.ToString() });
                            }
                            break;
                    }
                    //IndexToDateLabel.Add(i, m.OneRMDate.ToLocalTime().ToString("MMM dd"));

                    i++;
                }

               // plotModel.Series.Add(s1);
                //plotView.Model = plotModel;





                chartSerie.Entries = entries;
                chartSeries.Add(chartSerie);

                chartView.Chart = new LineChart
                {
                    LabelOrientation = Orientation.Vertical,
                    ValueLabelOrientation = Orientation.Vertical,
                    LabelTextSize = 18,
                    ValueLabelTextSize = 18,
                    SerieLabelTextSize = 12,
                    BackgroundColor = SKColors.Transparent,
                    LegendOption = SeriesLegendOption.None,
                    Series = chartSeries,
                };





                //Congratulations message

                DateTime minDate = _lastWorkoutLog.Min(p => p.OneRMDate);
                DateTime maxDate = _lastWorkoutLog.Max(p => p.OneRMDate);
                OneRMModel last = _lastWorkoutLog.First(p => p.OneRMDate == maxDate);
                OneRMModel beforeLast = _lastWorkoutLog.Where(p => p.OneRMDate > minDate && p.OneRMDate < maxDate).First();
                //OneRMModel beforeBeforeLast = _lastWorkoutLog.Where(p => p.OneRMDate > minDate && p.OneRMDate < maxDate).Skip(1).First();
                OneRMModel beforeBeforeLast = _lastWorkoutLog.Where(p => p.OneRMDate == minDate).First();

                decimal weight0 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                last.OneRM.Kg :
                                                last.OneRM.Lb;
                decimal weight1 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                beforeLast.OneRM.Kg :
                                                beforeLast.OneRM.Lb;
                decimal weight2 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                beforeBeforeLast.OneRM.Kg :
                                                beforeBeforeLast.OneRM.Lb;

                decimal reps0 = last.Reps;
                decimal reps1 = beforeLast.Reps;
                decimal progressNumb = 0;
                try
                {
                    lblLearnMore2.IsVisible = false;
                    lblLearnMore1.IsVisible = false;
                    lblResult2.IsVisible = true;
                    StkResult2.IsVisible = true;
                    lblResult1.Text = AppResources.YourStrengthHasGoneUp;//string.Format("{0} {1}!", AppResources.Congratulations, LocalDBManager.Instance.GetDBSetting("firstname").Value);
                    lblResult2.Text = "";

                    if (weight0 > weight1 && weight0 > weight2)
                    {
                        lblResult1.Text = "New strength record!";
                        lblResult2.Text = AppResources.YourStrengthHasGoneUpAndYouHaveSetaNewRecord;
                    }
                    else
                        lblResult2.IsVisible = false;
                    //lblResult21.Text = string.Format("{0} {1:f} {2}", AppResources.TodaysMaxEstimate, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                    //    _lastWorkoutLog.ElementAt(0).OneRM.Kg :
                    //    _lastWorkoutLog.ElementAt(0).OneRM.Lb,
                    //    LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs").ReplaceWithDot();
                    //lblResult3.Text = string.Format("{0} {1:f} {2}", AppResources.PreviousMaxEstimate, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                    //    _lastWorkoutLog.ElementAt(1).OneRM.Kg :
                    //    _lastWorkoutLog.ElementAt(1).OneRM.Lb,
                    //    LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs").ReplaceWithDot();
                    //    lblResult3.IsVisible = true;
                    //    lblResult4.Text = string.Format("{0}: {1:f} {2}",AppResources.Progress, weight0 - weight1,
                    //(LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs")).ReplaceWithDot();


                    if (isTimeBased)
                    {
                        lblResult21.Text = string.Format("{0} {1}", _lastWorkoutLog.ElementAt(0).Reps, "Secs");
                    }
                    else
                        lblResult21.Text = string.Format("{0} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1) :
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Lb, 1),
                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs").ReplaceWithDot();



                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Date}") == null)
                            LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Date}", Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1)).ReplaceWithDot());
                        else
                        {
                            var strenthCount = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Date}").Value.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
                            strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                            LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Date}", $"{strenthCount}");
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                    
                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting($"ExerciseStrenth{DateTime.Now.Date}") == null)
                        {
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenth{DateTime.Now.Date}", Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1)).ReplaceWithDot());
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenthName{DateTime.Now.Date}", CurrentLog.Instance.ExerciseLog.Exercice.Label);
                        }
                        else
                        {
                            var strenthCount = LocalDBManager.Instance.GetDBSetting($"ExerciseStrenth{DateTime.Now.Date}").Value.ReplaceWithDot();
                            //strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenth{DateTime.Now.Date}", $"{strenthCount}|{Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1)).ReplaceWithDot()}");

                            var exerciseNames = LocalDBManager.Instance.GetDBSetting($"ExerciseStrenthName{DateTime.Now.Date}").Value;
                            //strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenthName{DateTime.Now.Date}", $"{exerciseNames}|{CurrentLog.Instance.ExerciseLog.Exercice.Label}");
                        }

                    }
                    catch (Exception ex)
                    {

                    }

                    lblResult3.Text = string.Format("{0} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                        Math.Round(_lastWorkoutLog.ElementAt(1).OneRM.Kg, 1) :
                        Math.Round(_lastWorkoutLog.ElementAt(1).OneRM.Lb, 1),
                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs").ReplaceWithDot();
                    lblResult3.IsVisible = true;
                    if (isTimeBased)
                    {
                        lblResult3.Text = string.Format("{0} {1}", _lastWorkoutLog.ElementAt(1).Reps, "Sec");
                    }
                    //    lblResult4.Text = string.Format("{0} {1}",Math.Round(Math.Round(weight0,1) - Math.Round(weight1,1),1),
                    //(LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs")).ReplaceWithDot();
                    lblResult33.Text = "Last workout";
                    lblResult44.Text = "Progress";
                    
                    if (!isEstimated)
                    {
                        progressNumb = Math.Round(((Math.Round(weight0, 1) - Math.Round(weight1, 1)) * 100) / Math.Round(weight1, 1), 1);
                        lblResult4.Text = string.Format(" {0:0.#}%", progressNumb).ReplaceWithDot();
                        if (isTimeBased)
                        {
                            progressNumb = Math.Round(((reps0 - reps1) * 100) / reps1, 1);
                            lblResult4.Text = string.Format(" {0:0.#}%", progressNumb).ReplaceWithDot();
                        }

                    }
                    else
                    {
                        progressNumb = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1) :
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Lb, 1);

                        lblResult4.Text = string.Format("{0:0.#} {1}", progressNumb,
                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs").ReplaceWithDot();
                        lblResult3.Text = "0";
                        if (isTimeBased)
                        {
                            progressNumb = _lastWorkoutLog.ElementAt(0).Reps;
                            lblResult4.Text = string.Format(" {0:0}%", progressNumb).ReplaceWithDot();
                        }
                        //lblResult33.Text = "";
                        //lblResult44.Text = "";
                    }
                }
                catch (Exception ex)
                {

                }
                strFacebook = "";
                //If deload...

                if (weight0 < (weight1 * (decimal)0.98) && weight0 < (weight1 - 2))
                {
                    lblResult2.IsVisible = true;
                    StkResult2.IsVisible = true;
                    lblResult1.IsVisible = true;

                    if (isEstimated)
                    {
                        lblResult1.Text = "New strength record!";
                        lblResult2.Text = AppResources.YourStrengthHasGoneUpAndYouHaveSetaNewRecord;

                        IconResultImage.Source = "up_arrow.png";
                        //Set button text here:

                        try
                        {

                            if (LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout") == null)
                                LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", "1");
                            else
                            {
                                var recordCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout").Value);
                                recordCount += 1;
                                LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", $"{recordCount}");
                            }

                            NewRecordModel newModel = new NewRecordModel();
                            newModel.ExerciseName = CurrentLog.Instance.ExerciseLog.Exercice.Label;
                            //newModel.Prev1RM = beforeLast.OneRM;
                            newModel.New1RM = last.OneRM;
                            newModel.ExercisePercentage = lblResult4.Text;
                            newModel.ExercisePercentageNumber = progressNumb;
        ((App)Application.Current).NewRecordModelContext.NewRecordList.Add(newModel);
                            ((App)Application.Current).NewRecordModelContext.SaveContexts();
                        }
                        catch (Exception ex)
                        {

                        }
                        strFacebook = string.Format("{0} {1} {2} {3:f} {4}{5}", "I just smashed a new record!", CurrentLog.Instance.ExerciseLog.Exercice.Label, "is now", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                    _lastWorkoutLog.ElementAt(0).OneRM.Kg :
                    _lastWorkoutLog.ElementAt(0).OneRM.Lb, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", ". I train using Dr. Muscle. Get your invitation at:");
                        lblResult1.IsVisible = true;

                        lblResult6.IsVisible = false;
                        StkResult6.IsVisible = false;
                        LearnMoreButton.IsVisible = false;
                    }
                    else if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsLightSession)
                    {
                        lblResult1.Text = "Light session successful";// string.Format("{0} {1}!", AppResources.WellDone, LocalDBManager.Instance.GetDBSetting("firstname").Value);
                        lblResult2.Text = "Your light session went as planned";
                        lblResult6.Text = string.Format("{0} {1}", "Your weights will go up next time you", CurrentLog.Instance.ExerciseLog.Exercice.Label);//. [Learn more](http://drmuscleapp.com/news/deload/)
                        LearnMoreButton.IsVisible = false;
                        lblLearnMore2.IsVisible = true;
                        lblLearnMore1.IsVisible = false;
                        IconResultImage.Source = "down_arrow.png";
                    }
                    else if (LocalDBManager.Instance.GetDBSetting("RecoDeload").Value == "false")
                    {
                        lblResult1.Text = "Your strength has gone down";//string.Format("{0} {1}!", AppResources.Attention ,LocalDBManager.Instance.GetDBSetting("firstname").Value);
                        lblResult2.Text = "";
                        lblResult2.IsVisible = false;
                        StkResult2.IsVisible = false;
                        StkResult6.IsVisible = true;

                        lblResult6.Text = string.Format("{0} {1}.", AppResources.IWillLowerYourWeightsToHelpYouRecoverTheNextTimeYou, CurrentLog.Instance.ExerciseLog.Exercice.Label);
                        lblLearnMore2.IsVisible = true;
                        lblLearnMore1.IsVisible = false;
                        LearnMoreButton.IsVisible = false;
                        IconResultImage.Source = "down_arrow.png";
                    }
                    //If 2e workout au retour de deload...
                    else
                    {
                        lblResult1.Text = "Deload successful";//string.Format(AppResources.DeloadSuccessful);
                        lblResult2.Text = $"{AppResources.IHaveLowedYourWeightsToHelpYouRecoverInTheShortTermAndProgressLongTerm}";
                        lblResult6.IsVisible = false;
                        StkResult6.IsVisible = false;
                        lblLearnMore1.IsVisible = true;
                        lblLearnMore2.IsVisible = false;
                        LearnMoreButton.IsVisible = false;
                        IconResultImage.Source = "green.png";

                    }
                    if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsEasy && !isEstimated)
                    {
                        lblResult1.Text = "Recovery successful";//$"{AppResources.WellDone} {LocalDBManager.Instance.GetDBSetting("firstname").Value}";
                        lblResult2.Text = AppResources.IMadeThisExericseEasyToHelpYouRecoverTheNextTimeYouTrain; //"I made this exericse easy to help you recover. The next time you train, you'll be in a great position to smash a new record.";
                        StkResult2.IsVisible = true;
                        StkResult6.IsVisible = false;

                        lblLearnMore2.IsVisible = false;
                        lblLearnMore1.IsVisible = false;
                    }
                }
                //else if égal
                else if (weight0 == weight1)
                {
                    IconResultImage.Source = "green.png";
                    lblResult1.Text = "Lift successful";// string.Format("{0} {1}", AppResources.WellDone, LocalDBManager.Instance.GetDBSetting("firstname").Value);
                    lblResult2.Text = "Your strength has not changed, but your volume is going in the right direction. This is good.";// "Your strength has not changed, but you have done more sets. This is good.";
                    lblResult2.IsVisible = true;
                    StkResult2.IsVisible = true;
                    lblResult6.IsVisible = false;
                    StkResult6.IsVisible = false;
                    lblLearnMore2.IsVisible = false;
                    lblLearnMore1.IsVisible = false;
                    LearnMoreButton.IsVisible = false;
                }
                //else if légère diminution
                else if (weight0 >= (weight1 * (decimal)0.98) && weight0 <= weight1 || weight0 < (weight1 * (decimal)0.98) && weight0 >= (weight1 - 2))
                {
                    IconResultImage.Source = "green.png";
                    lblResult1.Text = "Lift successful";//string.Format("{0} {1}", AppResources.WellDone, LocalDBManager.Instance.GetDBSetting("firstname").Value);
                    lblResult2.Text = "Your strength has decreased slightly, but your volume is going in the right direction. Overall, this is progress.";
                    lblResult6.IsVisible = false;
                    StkResult6.IsVisible = false;
                    lblResult2.IsVisible = true;
                    StkResult2.IsVisible = true;
                    lblLearnMore2.IsVisible = false;
                    lblLearnMore1.IsVisible = false;
                    LearnMoreButton.IsVisible = false;
                }
                //Sinon (if pas deload...)
                else
                {
                    IconResultImage.Source = "up_arrow.png";
                    //Set button text here:
                    if (Config.ShowWelcomePopUp5 == false)
                    {
                        if (App.IsWelcomePopup5)
                            return;
                        App.IsWelcomePopup5 = true;
                        ConfirmConfig ShowWelcomePopUp5 = new ConfirmConfig()
                        {
                            Message = "Your progress, new records, and key stats at a glance.",
                            Title = "New record!",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = AppResources.GotIt,
                            CancelText = AppResources.RemindMe,
                        };
                        await Task.Delay(100);
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowWelcomePopUp5);
                        if (isConfirm)
                        {

                            Config.ShowWelcomePopUp5 = true;
                        }
                        else
                        {
                            Config.ShowWelcomePopUp5 = false;
                        }
                    }
                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout") == null)
                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", "1");
                        else
                        {
                            var recordCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout").Value);
                            recordCount += 1;
                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", $"{recordCount}");
                        }
                        NewRecordModel newModel = new NewRecordModel();
                        newModel.ExerciseName = CurrentLog.Instance.ExerciseLog.Exercice.Label;
                        if (!isEstimated)
                            newModel.Prev1RM = beforeLast.OneRM;
                        newModel.New1RM = last.OneRM;
                        newModel.ExercisePercentage = lblResult4.Text;
                        newModel.ExercisePercentageNumber = progressNumb;
((App)Application.Current).NewRecordModelContext.NewRecordList.Add(newModel);
                        ((App)Application.Current).NewRecordModelContext.SaveContexts();
                    }
                    catch (Exception ex)
                    {

                    }

                    strFacebook = string.Format("{0} {1} {2} {3:f} {4}{5}", "I just smashed a new record!", CurrentLog.Instance.ExerciseLog.Exercice.Label, "is now", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                    _lastWorkoutLog.ElementAt(0).OneRM.Kg :
                    _lastWorkoutLog.ElementAt(0).OneRM.Lb, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", ". I train using Dr. Muscle. Get your invitation at:");
                    lblResult1.IsVisible = true;

                    lblResult6.IsVisible = false;
                    StkResult6.IsVisible = false;
                    LearnMoreButton.IsVisible = false;
                }
                if (strFacebook == "")
                    FbShare.IsVisible = false;
                else
                    FbShare.IsVisible = true;
                if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsEasy)
                {
                    lblResult1.Text = "Recovery successful";//$"{AppResources.WellDone} {LocalDBManager.Instance.GetDBSetting("firstname").Value}";
                    lblResult2.Text = AppResources.IMadeThisExericseEasyToHelpYouRecoverTheNextTimeYouTrain;
                    lblResult2.IsVisible = true;
                }
                var setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                if (LocalDBManager.Instance.GetDBReco("RReps" + CurrentLog.Instance.ExerciseLog.Exercice.Id + setStyle + "challenge")?.Value == "max" && !Config.FirstChallenge)
                {
                    Config.FirstChallenge = true;
                    var waitHandle = new EventWaitHandle(false
                        , EventResetMode.AutoReset);
                    var modalPage = new Views.GeneralPopup("EmptyStar.png", "Congratulations!", "You completed your first challenge 💪", "Next exercise", new Thickness(0, 0, 0, 0));
                    modalPage.Disappearing += (sender2, e2) =>
                    {
                        waitHandle.Set();
                    };
                    await PopupNavigation.Instance.PushAsync(modalPage);

                    await Task.Run(() => waitHandle.WaitOne());
                }
                else if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsDeload && !Config.FirstDeload)
                {
                    Config.FirstDeload = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.GeneralPopup("EmptyStar.png", "Congratulations!", "First deload done", "Next exercise", new Thickness(0, 0, 0, 0));
                    modalPage.Disappearing += (sender2, e2) =>
                    {
                        waitHandle.Set();
                    };
                    await PopupNavigation.Instance.PushAsync(modalPage);

                    await Task.Run(() => waitHandle.WaitOne());
                }
                else if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsLightSession && !Config.FirstLightSession)
                {
                    Config.FirstLightSession = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.GeneralPopup("EmptyStar.png", "Congratulations!", "First light session done", "Next exercise", new Thickness(0, 0, 0, 0));
                    modalPage.Disappearing += (sender2, e2) =>
                    {
                        waitHandle.Set();
                    };
                    await PopupNavigation.Instance.PushAsync(modalPage);

                    await Task.Run(() => waitHandle.WaitOne());
                }
            }
            catch (Exception e)
            {
                ConnectionErrorPopup();

            }

            if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("KenkoSingleExercisePage"))
            {
                Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage() { OnlyRefresh = true }, "UpdatedWorkoutMessage");
            }
            else
            {
                // DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1352);
                var dt = DateTime.Now.AddMinutes(30);
                var timeSpan = new TimeSpan(0, dt.Hour, dt.Minute, 0);// DateTime.Now.AddMinutes(2) - DateTime.Now;////
                DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification("Dr. Muscle", "You forgot to save your workout!", timeSpan, 1352, NotificationInterval.Week, Convert.ToString(CurrentLog.Instance.CurrentWorkoutTemplate.Id));
            }
        }


        private string _formatter(double d)
        {
            return IndexToDateLabel.ContainsKey(d) ? IndexToDateLabel[d] : "";
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();

            //if (Config.ShowWelcomePopUp5 == false)
            //{
            //    if (App.IsWelcomePopup5)
            //        return;
            //    App.IsWelcomePopup5 = true;
            //    ConfirmConfig ShowWelcomePopUp5 = new ConfirmConfig()
            //    {
            //        Message = "Your progress, new records, and key stats at a glance.",
            //        Title = "New record!",
            //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //        OkText = AppResources.GotIt,
            //        CancelText = AppResources.RemindMe,
            //        OnAction = async (bool ok) => {
            //            if (ok)
            //            {
            //                Config.ShowWelcomePopUp5 = true;
            //            }
            //            else
            //            {
            //                Config.ShowWelcomePopUp5 = false;
            //            }
            //        }
            //    };
            //    await Task.Delay(100);
            //    UserDialogs.Instance.Confirm(ShowWelcomePopUp5);
            //}
        }

        async void ShareWithFBButton_Clicked(object sender, EventArgs e)
        {
            //var imageByte = DependencyService.Get<IScreenshotService>().Capture();
            //imgCaptured.Source = ImageSource.FromStream(() => new MemoryStream(imageByte));
            // FacebookShareLinkContent linkContent = new FacebookShareLinkContent("Awesome team of developers, making the world a better place one project or plugin at the time!",
            //                                                    new Uri("http://www.github.com/crossgeeks"));
            // var ret = await CrossFacebookClient.Current.ShareAsync(linkContent);
            var _manager = DependencyService.Get<IFacebookManager>();
            var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                FacebookUser result = await _manager.Login();

                if (result == null)
                {
                    UserDialogs.Instance.Alert(new AlertConfig()
                    {
                        Message = AppResources.AnErrorOccursWhenSigningIn,
                        Title = AppResources.UnableToLogIn,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    });
                    return;
                }
                DependencyService.Get<IFirebase>().LogEvent("fb_newrecord_share", "yes");

                _manager.ShareText(strFacebook, $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=newrecord&utm_content={firstname}");
            }
            else
            {
                Device.BeginInvokeOnMainThread(async () =>
                {
                    _manager.ShareText(strFacebook, $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=newrecord&utm_content={firstname}");
                    DependencyService.Get<IFirebase>().LogEvent("fb_newrecord_share", "yes");
                });
            }
        }

        async void animate(Button grid)
        {
            try
            {
                if (Battery.EnergySaverStatus == EnergySaverStatus.On && Device.RuntimePlatform.Equals(Device.Android))
                    return;
                var a = new Animation();
                a.Add(0, 0.5, new Animation((v) =>
                {
                    grid.Scale = v;
                }, 1.0, 0.8, Easing.CubicInOut, () => { System.Diagnostics.Debug.WriteLine("ANIMATION A"); }));
                a.Add(0.5, 1, new Animation((v) =>
                {
                    grid.Scale = v;
                }, 0.8, 1.0, Easing.CubicInOut, () => { System.Diagnostics.Debug.WriteLine("ANIMATION B"); }));
                a.Commit(grid, "animation", 16, 2000, null, (d, f) =>
                {
                    grid.Scale = 1.0;
                    System.Diagnostics.Debug.WriteLine("ANIMATION ALL");
                    if (ShouldAnimate)
                        animate(grid);
                });

            }
            catch (Exception ex)
            {

            }
        }

        void TapGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
        {
            Device.OpenUri(new Uri("http://dr-muscle.com/deload/"));
        }
    }
}
