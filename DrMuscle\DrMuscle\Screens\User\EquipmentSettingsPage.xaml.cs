﻿using DrMuscle;
using DrMuscle.Dependencies;
using DrMuscle.Helpers;
using DrMuscle.Screens.Exercises;
using DrMuscleWebApiSharedModel;
using System;
using Xamarin.Forms;
using DrMuscle.Layout;
using DrMuscle.Message;
using Acr.UserDialogs;
using System.Text.RegularExpressions;
using System.Globalization;
using DrMuscle.Localize;
using DrMuscle.Resx;
using System.Collections.ObjectModel;
using DrMuscle.Constants;
using global::Rg.Plugins.Popup.Services;
using DrMuscle.Views;
using System.Collections.Generic;
using Plugin.Connectivity;
using DrMuscle.Screens.Workouts;
using System.Linq;
using DrMuscle.Screens.User.OnBoarding;
using System.Threading.Tasks;
using DrMuscle.Services;
using System.Threading;
using DrMuscle.Screens.Subscription;
using System.Net.Http;
using DrMuscle.Utility;

namespace DrMuscle.Screens.User
{
    public partial class EquipmentSettingsPage : DrMusclePage, IActiveAware
    {
        public event EventHandler IsActiveChanged;
        public ObservableCollection<PlateModel> platesItems = new ObservableCollection<PlateModel>();
        public ObservableCollection<PlateModel> platesItems1 = new ObservableCollection<PlateModel>();
        public ObservableCollection<PlateModel> platesItems2 = new ObservableCollection<PlateModel>();


        public ObservableCollection<DumbellModel> dumbbellItems = new ObservableCollection<DumbellModel>();
        public ObservableCollection<DumbellModel> dumbbellItems1 = new ObservableCollection<DumbellModel>();
        public ObservableCollection<DumbellModel> dumbbellItems2 = new ObservableCollection<DumbellModel>();

        public ObservableCollection<PulleyModel> pulleyItems = new ObservableCollection<PulleyModel>();
        public ObservableCollection<PulleyModel> pulleyItems1 = new ObservableCollection<PulleyModel>();
        public ObservableCollection<PulleyModel> pulleyItems2 = new ObservableCollection<PulleyModel>();
        
        public ObservableCollection<BandsModel> bandsItems = new ObservableCollection<BandsModel>();
        public ObservableCollection<BandsModel> bandsItems1 = new ObservableCollection<BandsModel>();
        public ObservableCollection<BandsModel> bandsItems2 = new ObservableCollection<BandsModel>();


        MultiUnityWeight _increments;
        bool IsOnAppeatingFinished = false;
        bool IsEquipmentEdited = false;
        bool IsGymEnabled = false, IsHomeEnabled = false, IsOtherEnabled = false;
        bool _isActive;
        string learnMoreProgram = "";
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    IsActiveChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public EquipmentSettingsPage()
        {
            InitializeComponent();
            Title = "Equipment";
            PlatesListView.ItemsSource = platesItems;
            PlatesListView.ItemTapped += PlatesListView_ItemTapped;

            DumbbellsListView.ItemsSource = dumbbellItems;
            DumbbellsListView.ItemTapped += DumbbellsListView_ItemTapped;

            PulleyListView.ItemsSource = pulleyItems;
            PulleyListView.ItemTapped += PulleyListView_ItemTapped;

            PlatesListView1.ItemsSource = platesItems1;
            PlatesListView1.ItemTapped += PlatesListView_ItemTappedHome;

            DumbbellsListView1.ItemsSource = dumbbellItems1;
            DumbbellsListView1.ItemTapped += DumbbellsListView1_ItemTapped;

            PulleyListView1.ItemsSource = pulleyItems1;
            PulleyListView1.ItemTapped += PulleyListView1_ItemTapped;

            PlatesListView2.ItemsSource = platesItems2;
            PlatesListView2.ItemTapped += PlatesListView_ItemTappedOther;

            DumbbellsListView2.ItemsSource = dumbbellItems2;
            DumbbellsListView2.ItemTapped += DumbbellsListView2_ItemTapped; ;

            PulleyListView2.ItemsSource = pulleyItems2;
            PulleyListView2.ItemTapped += PulleyListView2_ItemTapped;
            
            
            BandsListView.ItemsSource = bandsItems;
            BandsListView.ItemTapped += BandsListView_ItemTapped;
            
            BandsListView1.ItemsSource = bandsItems1;
            BandsListView1.ItemTapped += BandsListView1_ItemTapped;
            
            BandsListView2.ItemsSource = bandsItems2;
            BandsListView2.ItemTapped += BandsListView2_ItemTapped;

            UnitEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
            MinEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
            MaxEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;

            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");
            
            if (LocalDBManager.Instance.GetDBSetting("Bands") == null)
                LocalDBManager.Instance.SetDBSetting("Bands", "true");

            SaveIncrementsButton.Clicked += async (sender, e) =>
            {
                try
                {
                    MultiUnityWeight increments = null;
                    MultiUnityWeight max = null;
                    MultiUnityWeight min = null;
                    if (!string.IsNullOrEmpty(UnitEntry.Text))
                    {

                        var text = UnitEntry.Text.Replace(",", ".");
                        var result = Convert.ToDecimal(text, System.Globalization.CultureInfo.InvariantCulture);
                        if (string.IsNullOrEmpty(result.ToString()) || result.ToString().Equals("0"))
                        {

                        }
                        else
                        {
                            increments = new MultiUnityWeight((decimal)result, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                            LocalDBManager.Instance.SetDBSetting("workout_increments", increments.Kg.ToString().Replace(",", "."));

                        }
                    }
                    else
                        LocalDBManager.Instance.SetDBSetting("workout_increments", null);

                    if (!string.IsNullOrEmpty(MaxEntry.Text))
                    {

                        var text = MaxEntry.Text.Replace(",", ".");
                        var result = Convert.ToDecimal(text, System.Globalization.CultureInfo.InvariantCulture);
                        if (string.IsNullOrEmpty(result.ToString()) || result.ToString().Equals("0"))
                        {

                        }
                        else
                        {
                            max = new MultiUnityWeight((decimal)result, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                            LocalDBManager.Instance.SetDBSetting("workout_max", max.Kg.ToString().Replace(",", "."));

                        }
                    }
                    else
                        LocalDBManager.Instance.SetDBSetting("workout_max", null);
                    if (!string.IsNullOrEmpty(MinEntry.Text))
                    {

                        var text = MinEntry.Text.Replace(",", ".");
                        var result = Convert.ToDecimal(text, System.Globalization.CultureInfo.InvariantCulture);
                        if (string.IsNullOrEmpty(result.ToString()))
                        {

                        }
                        else
                        {
                            min = new MultiUnityWeight((decimal)result, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                            LocalDBManager.Instance.SetDBSetting("workout_min", min.Kg.ToString().Replace(",", "."));
                        }
                    }
                    else
                        LocalDBManager.Instance.SetDBSetting("workout_min", null);
                    if (min != null && max != null)
                    {
                        if (min.Kg > max.Kg)
                        {
                            await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Title = AppResources.Error,
                                Message = AppResources.MinValueShouldNotGreaterThenMax,
                                OkText = AppResources.Ok
                            });

                            return;
                        }
                    }
                    LocalDBManager.Instance.ResetReco();
                    _increments = increments;
                    MessagingCenter.Send<GlobalSettingsChangeMessage>(new GlobalSettingsChangeMessage() { IsDisappear = true }, "GlobalSettingsChangeMessage");
                    await DrMuscleRestClient.Instance.SetUserIncrements(new UserInfosModel()
                    {
                        Increments = increments,
                        Max = max,
                        Min = min
                    });
                }
                catch (Exception ex)
                {

                }
            };


            OnlyEquipForSwitch.Toggled += (sender, e) =>
            {
                if (OnlyEquipForSwitch.IsToggled)
                {
                    OnlyEquipment.IsVisible = true;
                    //Checked for change program
                    if (IsOnAppeatingFinished)
                        askForProgramChange(true, false, false);
                    LocalDBManager.Instance.SetDBSetting("Equipment", "true");
                    if (IsOnAppeatingFinished)
                    {

                        LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");
                        LocalDBManager.Instance.SetDBSetting("HomeEquipment", "");
                        LocalDBManager.Instance.SetDBSetting("OtherEquipment", "");
                    }
                    
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("Equipment", "false");
                    OnlyEquipment.IsVisible = false;
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            OnlyEquipForSwitch1.Toggled += (sender, e) =>
            {
                if (OnlyEquipForSwitch1.IsToggled)
                {
                    OnlyEquipment1.IsVisible = true;
                    //Checked for change program
                    if (IsOnAppeatingFinished)
                        askForProgramChange(false, true, false);
                    LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "true");
                    if (IsOnAppeatingFinished)
                    {
                        LocalDBManager.Instance.SetDBSetting("HomeEquipment", "true");
                        LocalDBManager.Instance.SetDBSetting("GymEquipment", "");
                        LocalDBManager.Instance.SetDBSetting("OtherEquipment", "");
                    }
                    
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "false");
                    OnlyEquipment1.IsVisible = false;
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };
            OnlyEquipForSwitch2.Toggled += (sender, e) =>
            {
                if (OnlyEquipForSwitch2.IsToggled)
                {
                    OnlyEquipment2.IsVisible = true;
                    //Checked for change program
                    if (IsOnAppeatingFinished)
                        askForProgramChange(false, false, true);
                    LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", "true");
                    if (IsOnAppeatingFinished)
                    {
                        LocalDBManager.Instance.SetDBSetting("OtherEquipment", "true");
                        LocalDBManager.Instance.SetDBSetting("HomeEquipment", "");
                        LocalDBManager.Instance.SetDBSetting("GymEquipment", "");
                    }
                    
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", "false");
                    OnlyEquipment2.IsVisible = false;
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            PlatesSwitch.Toggled += async (sender, e) =>
            {
                if (PlatesSwitch.IsToggled)
                {
                    PlatesListView.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("Plate", "true");
                }
                else
                {
                    if (!DumbbellsSwitch.IsToggled && CurrentLog.Instance.IsReconfigration == false && LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                        {
                            Title = "Bodyweight program",
                            Message = "No plates no dumbbells? No problem!",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Bodyweight",
                            CancelText = AppResources.Cancel,
                        };
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                        if (isConfirm)
                        {
                            ChangingWorkout();
                        }
                        else
                        {
                            PlatesSwitch.IsToggled = true;
                            LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                            return;
                        }
                    }
                    PlatesListView.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("Plate", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            PlatesSwitch1.Toggled += async (sender, e) =>
            {
                if (PlatesSwitch1.IsToggled)
                {
                    PlatesListView1.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("HomePlate", "true");
                }
                else
                {
                    if (!DumbbellsSwitch1.IsToggled && CurrentLog.Instance.IsReconfigration == false && LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                        {
                            Title = "Bodyweight program",
                            Message = "No plates no dumbbells? No problem!",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Bodyweight",
                            CancelText = AppResources.Cancel,
                        };
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                        if (isConfirm)
                        {
                            ChangingWorkout();
                        }
                        else
                        {
                            PlatesSwitch1.IsToggled = true;
                            LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");
                            return;
                        }
                    }
                    PlatesListView1.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("HomePlate", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            PlatesSwitch2.Toggled += async (sender, e) =>
            {
                if (PlatesSwitch2.IsToggled)
                {
                    PlatesListView2.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("OtherPlate", "true");
                }
                else
                {
                    if (!DumbbellsSwitch2.IsToggled && CurrentLog.Instance.IsReconfigration == false && LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                        {
                            Title = "Bodyweight program",
                            Message = "No plates no dumbbells? No problem!",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Bodyweight",
                            CancelText = AppResources.Cancel,
                        };
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                        if (isConfirm)
                        {
                            //Set bodyweight program
                            ChangingWorkout();
                        }
                        else
                        {
                            PlatesSwitch2.IsToggled = true;
                            LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");
                            return;
                        }
                    }
                    PlatesListView2.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("OtherPlate", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };
            PullySwitch.Toggled += (sender, e) =>
            {
                if (PullySwitch.IsToggled)
                {
                    PulleyListView.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("Pully", "true");
                }
                else
                {
                    PulleyListView.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("Pully", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            PullySwitch1.Toggled += (sender, e) =>
            {
                if (PullySwitch1.IsToggled)
                {
                    PulleyListView1.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("HomePully", "true");
                }
                else
                {
                    PulleyListView1.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("HomePully", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            PullySwitch2.Toggled += (sender, e) =>
            {
                if (PullySwitch2.IsToggled)
                {
                    PulleyListView2.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("OtherPully", "true");
                }
                else
                {
                    PulleyListView2.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("OtherPully", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };


            ChinUpSwitch.Toggled += (sender, e) =>
            {
                if (ChinUpSwitch.IsToggled)
                {
                    LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("ChinUp", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            ChinUpSwitch1.Toggled += (sender, e) =>
            {
                if (ChinUpSwitch1.IsToggled)
                {
                    LocalDBManager.Instance.SetDBSetting("HomeChinUp", "true");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("HomeChinUp", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            ChinUpSwitch2.Toggled += (sender, e) =>
            {
                if (ChinUpSwitch2.IsToggled)
                {
                    LocalDBManager.Instance.SetDBSetting("OtherChinUp", "true");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("OtherChinUp", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };


            
            DumbbellsSwitch.Toggled += async (sender, e) =>
            {
                if (DumbbellsSwitch.IsToggled)
                {
                    DumbbellsListView.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                }
                else
                {
                    if (!PlatesSwitch.IsToggled && CurrentLog.Instance.IsReconfigration == false && LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
                    {
                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                        {
                            Title = "Bodyweight program",
                            Message = "No plates no dumbbells? No problem! ",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Bodyweight",
                            CancelText = AppResources.Cancel,
                        };
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                        if (isConfirm)
                        {
                            ChangingWorkout();
                        }
                        else
                        {
                            DumbbellsSwitch.IsToggled = true;
                            LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                            return;
                        }
                    }
                    DumbbellsListView.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("Dumbbell", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            DumbbellsSwitch1.Toggled += async (sender, e) =>
            {
                if (DumbbellsSwitch1.IsToggled)
                {
                    DumbbellsListView1.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");
                }
                else
                {
                    if (!PlatesSwitch1.IsToggled && CurrentLog.Instance.IsReconfigration == false && LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
                    {
                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                        {
                            Title = "Bodyweight program",
                            Message = "No plates no dumbbells? No problem! ",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Bodyweight",
                            CancelText = AppResources.Cancel,
                        };
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                        if (isConfirm)
                        {
                            ChangingWorkout();
                        }
                        else
                        {
                            DumbbellsSwitch1.IsToggled = true;
                            LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");
                            return;
                        }
                    }
                    DumbbellsListView1.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            DumbbellsSwitch2.Toggled += async (sender, e) =>
            {
                if (DumbbellsSwitch2.IsToggled)
                {
                    DumbbellsListView2.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");
                }
                else
                {
                    if (!PlatesSwitch2.IsToggled && CurrentLog.Instance.IsReconfigration == false && LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
                    {
                        ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                        {
                            Title = "Bodyweight program",
                            Message = "No plates no dumbbells? No problem! ",
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Bodyweight",
                            CancelText = AppResources.Cancel,
                        };
                        var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                        if (isConfirm)
                        {
                            ChangingWorkout();
                        }
                        else
                        {
                            DumbbellsSwitch2.IsToggled = true;
                            LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");
                            return;
                        }
                    }
                    DumbbellsListView2.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };


            BandsSwitch.Toggled += async (sender, e) =>
            {
                if (BandsSwitch.IsToggled)
                {
                    BandsListView.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("Bands", "true");
                }
                else
                {
                    BandsListView.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("Bands", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            BandsSwitch1.Toggled += async (sender, e) =>
            {
                if (BandsSwitch1.IsToggled)
                {
                    BandsListView1.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("HomeBands", "true");
                }
                else
                {
                    
                    BandsListView1.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("HomeBands", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };

            BandsSwitch2.Toggled += async (sender, e) =>
            {
                if (BandsSwitch2.IsToggled)
                {
                    BandsListView2.IsVisible = true;
                    LocalDBManager.Instance.SetDBSetting("OtherBands", "true");
                }
                else
                {
                    
                    BandsListView2.IsVisible = false;
                    LocalDBManager.Instance.SetDBSetting("OtherBands", "false");
                }
                if (IsOnAppeatingFinished)
                    SetUserEquipmentSettings();
            };
            
            RefreshLocalized();

            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
                OnAppearing();
            });

        }

        private async void askForProgramChange(bool isGym, bool isHome, bool isOther)
        {
            var isGymOn = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true";

            var isHomeOn = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true";
            
            var isOtherOn = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true";
            string titleProgram = "Change program?";
            string descriptionProgram = "Equipment profile changed. Change program?";
            var label = "";
            var level = 0;
            var workoutId = 0;
            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            var shouldShowAlert = false;
            if (workouts != null && workouts.GetUserProgramInfoResponseModel != null &&
                workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null && !string.IsNullOrEmpty(workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label))
            {
                 label = workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label;
                 level = workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Level == null
                     ? 0
                     : (int)workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Level;
            }

            if (isGym && isHomeOn)
                shouldShowAlert = true;
            else if(isHome && isGymOn)
                shouldShowAlert = true;
            else if(isOther && (isGymOn || isHomeOn))
                shouldShowAlert = true;

            TempProgramModel tempProgramModel = null; 
            if (isGym && isHomeOn && LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value != "true" && (label.Contains("Full-Body") || label.Contains("Split") || label.Contains("Push/Pull/Leg")))
            {
                // Change profile to Gym
                descriptionProgram = "Current program is home. Change to gym?";
                tempProgramModel = AppThemeConstants.GetAllLevelProgram(level,isGym, label.Contains("Full-Body"),label.Contains("Push/Pull/Leg") ? "PPL" : label);
                workoutId = tempProgramModel.workoutid;
            }
            
            if (isHome && isGymOn && LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value != "true" && (label.Contains("Full-Body") || label.Contains("Split") || label.Contains("Push/Pull/Leg")))
            {
                // Change profile to Home
                descriptionProgram = "Current program is gym. Change to home?";
                tempProgramModel = AppThemeConstants.GetAllLevelProgram(level,isGym, label.Contains("Full-Body"),label.Contains("Push/Pull/Leg") ? "PPL" : label);
                workoutId = tempProgramModel.workoutid;
            }

            if (isOther && LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value != "true")
            {
                //Other
                descriptionProgram = "Equipment profile changed. Change program?";
            }
            if (shouldShowAlert) {
            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
            {
                Title = titleProgram,
                Message = descriptionProgram,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Change program",
                CancelText = AppResources.Cancel,
            };
            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
            if (isConfirm)
            {
                if (tempProgramModel != null)
                    changeProgramForProfile(tempProgramModel);
                else
                {
                    await PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
                }
            }
            }
        }

        
        private async void changeProgramForProfile(TempProgramModel tempProgramModel)
        {
            try
            {
                bool isSystem = false;
                BooleanModel successWorkoutLog = null;
                successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkoutV3(new SaveWorkoutModel()
                        { WorkoutId = tempProgramModel.workoutid, WorkoutTemplateId = tempProgramModel.programid });
            }
            catch (Exception ex)
            {

            }
        }
        
        private async void CheckNotificationPermission()
        {
            if (!DependencyService.Get<INotificationsInterface>().registeredForNotifications())
            {
                ConfirmConfig ShowOffPopUp = new ConfirmConfig()
                {
                    Title = "Enable notifcations",
                    Message = "Notifcations off. Enable to get reminders.",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Enable",
                    CancelText = AppResources.Cancel,
                };
                var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
                if (isConfirm)
                {
                    DependencyService.Get<IAppSettingsHelper>().OpenAppSettings();
                }
            }
        }

    
        private void RefreshLocalized()
        {
            Increments.Text = AppResources.Increments;
            LblUnits.Text = "GENERAL";//AppResources.UNITS;
            SaveIncrementsButton.Text = "Save increments";
            UnitEntry.Placeholder = AppResources.TapToSet;
            LblMaxIncrements.Text = AppResources.MaxWeight;
            LblMinIncrements.Text = AppResources.MinWeight;
        }


        private async void ChangingWorkout()
        {
            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Title = AppResources.ConnectionError,
                        Message = AppResources.PleaseCheckInternetConnection,
                        OkText = AppResources.Ok
                    });
                    return;
                }
                try
                {
                    if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("QuickMode", LocalDBManager.Instance.GetDBSetting("OlderQuickMode").Value);
                            LocalDBManager.Instance.SetDBSetting("OlderQuickMode", null);
                        }
                    }
                }
                catch (Exception ex)
                {

                }
                try
                {
                    if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp == 999)
                    {
                        foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
                        {
                            LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exerciceModel.Id}", "false");
                        }
                        await PagesFactory.PopToRootAsync();
                        return;
                    }
                }
                catch (Exception ex)
                {

                }
                bool isSystem = false;
                BooleanModel successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkoutV2(new SaveWorkoutModel() { WorkoutId = 12645 });
                try
                {
                    if (successWorkoutLog.Result)
                    {
                        Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage(), "UpdatedWorkoutMessage");
                    }
                    LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
                    LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());
                    isSystem = CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise;
                }
                catch (Exception ex)
                {

                }

                var nextworkoutName = CurrentLog.Instance.CurrentWorkoutTemplate.Label;
                CurrentLog.Instance.CurrentWorkoutTemplate = null;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
                string fname = LocalDBManager.Instance.GetDBSetting("firstname").Value;

                try
                {
                    AlertConfig p = new AlertConfig()
                    {
                        Title = $"{AppResources.GotIt} {fname}!",
                        Message = $"Your next workout will be Bodyweight 1.",
                        OkText = AppResources.Ok,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    };
                    p.OnAction = async () =>
                    {
                        await PagesFactory.PopToRootAsync();
                    };
                    UserDialogs.Instance.Alert(p);

                }

                catch (Exception ex)
                {
                    await PagesFactory.PopToRootAsync();
                }

            }
            catch (Exception ex)
            {

            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            LoadData();
        }

        async void LoadData()
        {
            if (App.IsSidemenuOpen)
                return;

            IsOnAppeatingFinished = false;
            
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(1000);
            MessagingCenter.Send<GlobalSettingsChangeMessage>(new GlobalSettingsChangeMessage() { IsDisappear = true }, "GlobalSettingsChangeMessage");

            if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null)
            {
                if (LocalDBManager.Instance.GetDBSetting("workout_increments").Value != null)
                {
                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    var unit = new MultiUnityWeight(value, "kg");
                    UnitEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{unit.Kg}" : $"{unit.Lb}";
                }
            }
            else
                UnitEntry.Text = "";

            if (LocalDBManager.Instance.GetDBSetting("workout_max") != null)
            {
                if (LocalDBManager.Instance.GetDBSetting("workout_max").Value != null)
                {
                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_max").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    var unit = new MultiUnityWeight(value, "kg");
                    MaxEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{unit.Kg}" : $"{unit.Lb}";
                }
            }
            else
                MaxEntry.Text = "";
            if (LocalDBManager.Instance.GetDBSetting("workout_min") != null)
            {
                if (LocalDBManager.Instance.GetDBSetting("workout_min").Value != null)
                {
                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_min").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    var unit = new MultiUnityWeight(value, "kg");
                    MinEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{unit.Kg}" : $"{unit.Lb}";
                }
            }
            else
                MinEntry.Text = "";


            if (LocalDBManager.Instance.GetDBSetting("reprangeType") == null)
                LocalDBManager.Instance.SetDBSetting("reprangeType", "4");

            if (LocalDBManager.Instance.GetDBSetting("GymEquipment") == null || LocalDBManager.Instance.GetDBSetting("HomeEquipment") == null || LocalDBManager.Instance.GetDBSetting("OtherEquipment") == null)
                LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");

            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");

            if (LocalDBManager.Instance.GetDBSetting("Bands") == null)
                LocalDBManager.Instance.SetDBSetting("Bands", "true");


            //Home
            if (LocalDBManager.Instance.GetDBSetting("HomeMainEquipment") == null)
                LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("HomeChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("HomeChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("HomeDumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("HomePlate") == null)
                LocalDBManager.Instance.SetDBSetting("HomePlate", "true");

            if (LocalDBManager.Instance.GetDBSetting("HomePully") == null)
                LocalDBManager.Instance.SetDBSetting("HomePully", "true");

            if (LocalDBManager.Instance.GetDBSetting("HomeBands") == null)
                LocalDBManager.Instance.SetDBSetting("HomeBands", "true");

            //Other
            if (LocalDBManager.Instance.GetDBSetting("OtherMainEquipment") == null)
                LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("OtherChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("OtherChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("OtherDumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("OtherPlate") == null)
                LocalDBManager.Instance.SetDBSetting("OtherPlate", "true");

            if (LocalDBManager.Instance.GetDBSetting("OtherPully") == null)
                LocalDBManager.Instance.SetDBSetting("OtherPully", "true");

            if (LocalDBManager.Instance.GetDBSetting("OtherBands") == null)
            LocalDBManager.Instance.SetDBSetting("OtherBands", "true");

            GymEquipmentStack.IsVisible = false;
            HomeEquipmentStack.IsVisible = false;
            OtherEquipmentStack.IsVisible = false;


            GymEquipmentUnselected();
            HomeEquipmentUnSelected();
            OtherEquipmentUnselected();

            OnlyEquipForSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true";

            ChinUpSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true";
            DumbbellsSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("Dumbbell").Value == "true";
            PlatesSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true";
            PullySwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true"; 
            BandsSwitch.IsToggled = LocalDBManager.Instance.GetDBSetting("Bands")?.Value == "true";
            

            OnlyEquipForSwitch1.IsToggled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true";

            ChinUpSwitch1.IsToggled = LocalDBManager.Instance.GetDBSetting("HomeChinUp").Value == "true";
            DumbbellsSwitch1.IsToggled = LocalDBManager.Instance.GetDBSetting("HomeDumbbell").Value == "true";
            PlatesSwitch1.IsToggled = LocalDBManager.Instance.GetDBSetting("HomePlate").Value == "true";
            PullySwitch1.IsToggled = LocalDBManager.Instance.GetDBSetting("HomePully").Value == "true";
            BandsSwitch1.IsToggled = LocalDBManager.Instance.GetDBSetting("HomeBands")?.Value == "true";

            OnlyEquipForSwitch2.IsToggled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true";

            ChinUpSwitch2.IsToggled = LocalDBManager.Instance.GetDBSetting("OtherChinUp").Value == "true";
            DumbbellsSwitch2.IsToggled = LocalDBManager.Instance.GetDBSetting("OtherDumbbell").Value == "true";
            PlatesSwitch2.IsToggled = LocalDBManager.Instance.GetDBSetting("OtherPlate").Value == "true";
            PullySwitch2.IsToggled = LocalDBManager.Instance.GetDBSetting("OtherPully").Value == "true";
            BandsSwitch2.IsToggled = LocalDBManager.Instance.GetDBSetting("OtherBands")?.Value == "true";
            IsGymEnabled = IsHomeEnabled = IsOtherEnabled = false;
            
            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true")
            {
                GymEquipment();
                IsGymEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true";
            }
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true")
            {
                HomeEquipment();
                IsHomeEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true";
            }
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true")
            {
                OtherEquipment();
                IsOtherEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true";
            }

            //emailReminderSwitch
            if (LocalDBManager.Instance.GetDBSetting("IsEmailReminder") == null)
                LocalDBManager.Instance.SetDBSetting("IsEmailReminder", "true");




            if (LocalDBManager.Instance.GetDBSetting("PlatesKg") == null || LocalDBManager.Instance.GetDBSetting("PlatesLb") == null)
            {
                var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", kgString);

                var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", lbString);
            }
            if (LocalDBManager.Instance.GetDBSetting("HomePlatesKg") == null || LocalDBManager.Instance.GetDBSetting("HomePlatesLb") == null)
            {
                var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", kgString);

                var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", lbString);
            }

            if (LocalDBManager.Instance.GetDBSetting("BandsKg") == null || LocalDBManager.Instance.GetDBSetting("BandsLb") == null)
            {
                var kgString = "Black_40_2_True|Blue_30_2_True|Green_20_2_True|Red_10_2_True|Yellow_4_2_True";
                LocalDBManager.Instance.SetDBSetting("BandsKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomeBandsKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherBandsKg", kgString);
            
                var lbString = "Black_90_2_True|Blue_65_2_True|Green_45_2_True|Red_25_2_True|Yellow_10_2_True";
                LocalDBManager.Instance.SetDBSetting("BandsLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomeBandsLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherBandsLb", lbString);
            }
            
            if (LocalDBManager.Instance.GetDBSetting("MobilityRep") == null)
                LocalDBManager.Instance.SetDBSetting("MobilityRep", "10");

            SetMobilityLevel();

            if (LocalDBManager.Instance.GetDBSetting("DumbbellKg") == null || LocalDBManager.Instance.GetDBSetting("DumbbellLb") == null)
            {
                var kgString = "50_2_True|47.5_2_True|45_2_True|42.5_2_True|40_2_True|37.5_2_True|35_2_True|32.5_2_True|30_2_True|27.5_2_True|25_2_True|22.5_2_True|20_2_True|17.5_2_True|15_2_True|12.5_2_True|10_2_True|7.5_2_True|5_2_True|2.5_2_True|1_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", kgString);

                var lbString = "90_2_True|85_2_True|80_2_True|75_2_True|70_2_True|65_2_True|60_2_True|55_2_True|50_2_True|45_2_True|40_2_True|35_2_True|30_2_True|25_2_True|20_2_True|15_2_True|12_2_True|10_2_True|8_2_True|5_2_True|3_2_True|2_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", lbString);
            }

            if (LocalDBManager.Instance.GetDBSetting("PulleyKg") == null || LocalDBManager.Instance.GetDBSetting("PulleyLb") == null)
            {
                var kgString = "5_20_True|1.5_2_True";
                var lbString = "10_20_True|5_2_True|2.5_2_True";

                LocalDBManager.Instance.SetDBSetting("PulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", kgString);

                LocalDBManager.Instance.SetDBSetting("PulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", lbString);
            }

            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                GeneratePlatesArray("PlatesKg");
                GeneratePlatesArray("HomePlatesKg");
                GeneratePlatesArray("OtherPlatesKg");

                GenerateDumbbellArray("DumbbellKg");
                GenerateDumbbellArray("HomeDumbbellKg");
                GenerateDumbbellArray("OtherDumbbellKg");

                GeneratePulleyArray("PulleyKg");
                GeneratePulleyArray("HomePulleyKg");
                GeneratePulleyArray("OtherPulleyKg");
                
                GenerateBandsArray("BandsKg");
                GenerateBandsArray("HomeBandsKg");
                GenerateBandsArray("OtherBandsKg");
                
            }
            else
            {
                GeneratePlatesArray("PlatesLb");
                GeneratePlatesArray("HomePlatesLb");
                GeneratePlatesArray("OtherPlatesLb");

                GenerateDumbbellArray("DumbbellLb");
                GenerateDumbbellArray("HomeDumbbellLb");
                GenerateDumbbellArray("OtherDumbbellLb");

                GeneratePulleyArray("PulleyLb");
                GeneratePulleyArray("HomePulleyLb");
                GeneratePulleyArray("OtherPulleyLb");
                
                GenerateBandsArray("BandsLb");
                GenerateBandsArray("HomeBandsLb");
                GenerateBandsArray("OtherBandsLb");
            }




            try
            {
                if (!CrossConnectivity.Current.IsConnected)
                    return;
                var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;

                var features = "";
                var learnMore = "";
                if (workouts != null && workouts.GetUserProgramInfoResponseModel != null)
                {
                    var upi = workouts.GetUserProgramInfoResponseModel;
                    var profileCount = (LocalDBManager.Instance.GetDBSetting("Equipment")?.Value == "true" ? 1 : 0) + (LocalDBManager.Instance.GetDBSetting("HomeMainEquipment")?.Value == "true" ? 1 : 0) + (LocalDBManager.Instance.GetDBSetting("OtherMainEquipment")?.Value == "true" ? 1 : 0);
                    var profileName = "";
                    if (profileCount > 1)
                    {

                        if (IsGymEnabled)
                            profileName = "Gym";
                        if (IsHomeEnabled)
                            profileName = "Home";
                        if (IsOtherEnabled)
                            profileName = "Custom";
                    }
                    if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null)
                    {
                
                        var remainWorkout = Math.Abs(upi.RecommendedProgram.RequiredWorkoutToLevelUp - upi.RecommendedProgram.RemainingToLevelUp ?? 0);


                        LocalDBManager.Instance.SetDBSetting("remain", upi.RecommendedProgram.RemainingToLevelUp.ToString());
                    }
                    else
                    {

                        if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
                            LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
                        {
                            try
                            {
                                long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
                                long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);
                                upi = new GetUserProgramInfoResponseModel()
                                {
                                    NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value },
                                    RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
                                };
                              
                                var remainWorkout = Math.Abs(upi.RecommendedProgram.RequiredWorkoutToLevelUp - upi.RecommendedProgram.RemainingToLevelUp ?? 0);


                            }
                            catch (Exception ex)
                            {

                            }

                        }

                    }

                    if (upi.RecommendedProgram != null && upi.NextWorkoutTemplate != null && upi.NextWorkoutTemplate.IsSystemExercise)
                    {

                        try
                        {
                            var workoutLogAverage = ((App)Application.Current).UserWorkoutContexts.workouts;

                            if (!string.IsNullOrEmpty(upi.RecommendedProgram.Label))
                                features = $"You're on the {upi.RecommendedProgram.Label} program. ";

                            if (upi.NextWorkoutTemplate.IsSystemExercise)
                            {
                                try
                                {

                                    if (workoutLogAverage != null && workoutLogAverage.HistoryExerciseModel != null)
                                    {
                                        features += $"That's based on your past experience";
                                        if (workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted > 0)
                                        {
                                            var w = workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted <= 1 ? "workout" : "workouts";
                                            features += $" and {workoutLogAverage.HistoryExerciseModel.TotalWorkoutCompleted} {w} recorded.";
                                        }

                                    }

                                }
                                catch (Exception)
                                {

                                }
                            }
                            if (profileCount > 1)
                                features = $"{profileName} equipment profile loaded. On that profile, you're on the {upi.RecommendedProgram.Label} program.";
                            learnMoreProgram = learnMore;

                        }
                        catch (Exception)
                        {

                        }
  
                    }

                }


            }
            catch (Exception ex)
            {

            }

            IsOnAppeatingFinished = true;


        }
        async void ChangeWorkoutClicked(object sender, EventArgs e)
        {
            if (IsFromWorkoutPage())
            {
                await Navigation.PopToRootAsync(false);
                await PagesFactory.PushAsync<SettingsPage>();
            }
            await PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
        }

        private bool IsFromWorkoutPage()
        {
            var isMePage = false;
            foreach (var item in Navigation.NavigationStack)
            {
                if (item is ChooseDrMuscleOrCustomPage || item is KenkoChooseYourWorkoutExercisePage)
                {
                    isMePage = true;
                    break;
                }
            }
            return isMePage;
        }

        private async void ContinueOnboarding()
        {
            await UserDialogs.Instance.AlertAsync(new AlertConfig()
            {
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                Title = $"Settings",
                Message = "Customize your reps, sets, body part priority, cardio, and more.",
                OkText = AppResources.GotIt
            });

           

        }

        protected override async void OnDisappearing()
        {
            base.OnDisappearing();
            MessagingCenter.Send<GlobalSettingsChangeMessage>(new GlobalSettingsChangeMessage() { IsDisappear = false }, "GlobalSettingsChangeMessage");
            if (IsEquipmentEdited)
            {
                await SetUserEquipmentSettingsWithLoader();
                IsEquipmentEdited = false;
                Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage(), "UpdatedWorkoutMessage");
            }

            

        }

        

        public async void BtnRestPauseClicked(object sender, EventArgs args)
        {
            UpdateSetSetyle(false);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
            //RestPause();


        }
        
        public async void BtnNormalClicked(object sender, EventArgs args)
        {

            UpdateSetSetyle(true);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");

            //Normal();

        }

     
        void BtnRPyramid_Clicked(System.Object sender, System.EventArgs e)
        {

            UpdatePyramidSetStyle(true);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
            //RPyramid();
        }

       
        void BtnPyramid_Clicked(System.Object sender, System.EventArgs e)
        {

            UpdateSetSetyle(null);

            LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
            LocalDBManager.Instance.SetDBSetting("IsPyramid", "true");
            LocalDBManager.Instance.SetDBSetting("IsRPyramid", "false");
            //Pyramid();
        }


        /// <summary>
        /// Workout Mode options
        /// </summary>

        private void Turbo()
        {
            
        }


        private async void SetupWorkoutMode(bool? isToggled)
        {
            await DrMuscleRestClient.Instance.SetUserQuickMode(new UserInfosModel()
            {
                IsQuickMode = isToggled
            });
            LocalDBManager.Instance.ResetReco();
        }

        private async void SetupUserRecommended(bool? isenabled)
        {
            await DrMuscleRestClient.Instance.SetUserRecommendedReminder(new UserInfosModel()
            {
                IsRecommendedReminder = isenabled
            });

        }

        public async void BtnTurboClicked(object sender, EventArgs args)
        {

            SetupWorkoutMode(true);
            //LocalDBManager.Instance.SetDBSetting("QuickMode", "true");
            Turbo();


        }
        private void Quick()
        {

            
        }
        public async void BtnQuickClicked(object sender, EventArgs args)
        {

            SetupWorkoutMode(null);
            
            LocalDBManager.Instance.SetDBSetting("QuickMode", "null");

            Quick();

        }
        private void NormalMode()
        {
          

        }
        void BtnNormalMode_Clicked(System.Object sender, System.EventArgs e)
        {

            SetupWorkoutMode(false);

            LocalDBManager.Instance.SetDBSetting("QuickMode", "false");

            NormalMode();
        }

        void BtnRecommended_Clicked(System.Object sender, System.EventArgs e)
        {

            SetupUserRecommended(true);

            LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "true");
            RecommendedReminderMode();
            IAlarmAndNotificationService alarmAndNotificationService = new AlarmAndNotificationService();
            alarmAndNotificationService.CancelNotification(101);
            alarmAndNotificationService.CancelNotification(102);
            alarmAndNotificationService.CancelNotification(103);
            alarmAndNotificationService.CancelNotification(104);
            alarmAndNotificationService.CancelNotification(105);
            alarmAndNotificationService.CancelNotification(106);
            alarmAndNotificationService.CancelNotification(107);
            alarmAndNotificationService.CancelNotification(108);
        }

        private void RecommendedReminderMode()
        {

            try
            {
                var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;

                if (workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.IsSystemExercise)
                {


                    //If Default and Age
                    int age = 40;
                    bool isApproxAge = true;
                    if (LocalDBManager.Instance.GetDBSetting("Age") != null && LocalDBManager.Instance.GetDBSetting("Age").Value != null)
                    {
                        age = int.Parse(LocalDBManager.Instance.GetDBSetting("Age").Value);
                    }

                    if (workouts.Sets != null)
                    {

                    }
                    else
                    {
                        workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
                    }

                    var programName = workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label;
                    var ageText = "";
                    var xDays = 0;
                    if (programName.ToLower().Contains("push/pull/legs") && age < 51)
                    {
                        xDays = 6;
                    }
                    else if (programName.ToLower().Contains("split"))
                    {
                        if (age < 30)
                            xDays = 4;
                        else if (age >= 30 && age <= 50)
                            xDays = 4;
                        else
                            xDays = 3;
                    }
                    else if (programName.ToLower().Contains("bodyweight") ||
    programName.ToLower().Contains("mobility") || programName.ToLower().Contains("full-body") || programName.ToLower().Contains("bands") || programName.ToLower().Contains("powerlifting"))
                    {
                        if (age < 30)
                            xDays = 4;
                        else if (age >= 30 && age <= 50)
                            xDays = 3;
                        else
                            xDays = 2;
                    }

                    if (xDays != 0)
                    {
                        //
                        
                    }
                }
                else
                {
                }

            }
            catch (Exception ex)
            {

            }

           

        }

        public async void BtnCustomReminderClicked(object sender, EventArgs args)
        {

            SetupUserRecommended(null);


            var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
            var modalPage = new ReminderPopup();
            modalPage.Disappearing += (sender2, e2) =>
            {
                waitHandle.Set();
            };
            await PopupNavigation.Instance.PushAsync(modalPage);

            await Task.Run(() => waitHandle.WaitOne());

            CustomReminderMode();

        }

        private void CustomReminderMode()
        {
            if (App.workoutPerDay == 0)
                return;
            if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderDays").Value != null)
            {
                var strDays = LocalDBManager.Instance.GetDBSetting("ReminderDays").Value;
                if (!strDays.Contains("1"))
                {
                    NoReminderMode();
                    return;
                }
            }

           
            if (LocalDBManager.Instance.GetDBSetting("ReminderDays") != null && LocalDBManager.Instance.GetDBSetting("ReminderDays").Value != null)
            {
                var strDays = LocalDBManager.Instance.GetDBSetting("ReminderDays").Value;
                if (!strDays.Contains("1"))
                {
                    NoReminderMode();
                    LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "null");
                    return;
                }
                LocalDBManager.Instance.SetDBSetting("RecommendedReminder", "false");
                TimeSpan timePickerSpan;
                try
                {
                    timePickerSpan = TimeSpan.Parse(LocalDBManager.Instance.GetDBSetting("ReminderTime").Value);
                }
                catch (Exception ex)
                {
                    return;
                }
                if (strDays.ToCharArray().Length == 7)
                {
                    var IsSunday = strDays[0] == '1';
                    var IsMonday = strDays[1] == '1';
                    var IsTuesday = strDays[2] == '1';
                    var IsWednesday = strDays[3] == '1';
                    var IsThursday = strDays[4] == '1';
                    var IsFriday = strDays[5] == '1';
                    var IsSaturday = strDays[6] == '1';


                    IAlarmAndNotificationService alarmAndNotificationService = new AlarmAndNotificationService();
                    alarmAndNotificationService.CancelNotification(101);
                    alarmAndNotificationService.CancelNotification(102);
                    alarmAndNotificationService.CancelNotification(103);
                    alarmAndNotificationService.CancelNotification(104);
                    alarmAndNotificationService.CancelNotification(105);
                    alarmAndNotificationService.CancelNotification(106);
                    alarmAndNotificationService.CancelNotification(107);
                    alarmAndNotificationService.CancelNotification(108);

                    string days = "";
                    if (IsMonday)
                    {
                        days = "Monday";
                    }
                    if (IsTuesday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Tuesday";
                        }
                        else
                        {
                            days += ", Tuesday";
                        }
                    }
                    if (IsWednesday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Wednesday";
                        }
                        else
                        {
                            days += ", Wednesday";
                        }
                    }
                    if (IsThursday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Thursday";
                        }
                        else
                        {
                            days += ", Thursday";
                        }
                    }
                    if (IsFriday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Friday";
                        }
                        else
                        {
                            days += ", Friday";
                        }
                    }
                    if (IsSaturday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Saturday";
                        }
                        else
                        {
                            days += ", Saturday";
                        }
                    }

                    if (IsSunday)
                    {
                        if (string.IsNullOrEmpty(days))
                        {
                            days = "Sunday";
                        }
                        else
                        {
                            days += ", Sunday";
                        }
                    }

                    if (!string.IsNullOrEmpty(days))
                    {
                        var array = days.Split(',');
                        if (array.Count() > 0)
                        {
                            days = days.Replace($",{array.Last()}", $", and{array.Last()}");
                        }
                        days = $"{days}.";
                    }

                    alarmAndNotificationService.CancelNotification(101);
                    alarmAndNotificationService.CancelNotification(102);
                    alarmAndNotificationService.CancelNotification(103);
                    alarmAndNotificationService.CancelNotification(104);
                    alarmAndNotificationService.CancelNotification(105);
                    alarmAndNotificationService.CancelNotification(106);
                    alarmAndNotificationService.CancelNotification(107);
                    alarmAndNotificationService.CancelNotification(108);

                    var day = 0;
                    if (IsSunday)
                    {
                        if (DayOfWeek.Sunday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Sunday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Sunday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 101, NotificationInterval.Week);
                    }
                    if (IsMonday)
                    {
                        if (DayOfWeek.Monday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Monday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Monday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 102, NotificationInterval.Week);
                    }
                    if (IsTuesday)
                    {
                        if (DayOfWeek.Tuesday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Tuesday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Tuesday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 103, NotificationInterval.Week);
                    }
                    if (IsWednesday)
                    {
                        if (DayOfWeek.Wednesday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Wednesday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Wednesday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 104, NotificationInterval.Week);
                    }
                    if (IsThursday)
                    {
                        if (DayOfWeek.Thursday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Thursday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Thursday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 105, NotificationInterval.Week);
                    }
                    if (IsFriday)
                    {
                        if (DayOfWeek.Friday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Friday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Friday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 106, NotificationInterval.Week);
                    }
                    if (IsSaturday)
                    {
                        if (DayOfWeek.Saturday - DateTime.Now.DayOfWeek < 0)
                        {
                            day = 7 + (DayOfWeek.Saturday - DateTime.Now.DayOfWeek);
                        }
                        else
                        {
                            day = DayOfWeek.Saturday - DateTime.Now.DayOfWeek;
                        }
                        var timeSpan = new TimeSpan(day, timePickerSpan.Hours, timePickerSpan.Minutes, timePickerSpan.Seconds);
                        alarmAndNotificationService.ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", timeSpan, 107, NotificationInterval.Week);
                    }
                }
            }

        }
        private void NoReminderMode()
        {
           

        }

        /// <summary>
        /// Bodypart priority
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BicepsBodypart()
        {
            
        }
        private void BicepsBodypartUnselected()
        {
            
        }

        private async void SetupBodyPartPriority(string bodypart)
        {
            await DrMuscleRestClient.Instance.SetUserBodypartPriority(new UserInfosModel()
            {
                BodyPartPrioriy = bodypart
            });

        }

        public async void BtnBicepsClicked(object sender, EventArgs args)
        {

            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
            {
                if (LocalDBManager.Instance.GetDBSetting("gender") != null && LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Biceps");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                BicepsBodypart();
            }
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Biceps")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        BicepsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Biceps");
                        BicepsBodypart();
                    }
                }
                else
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        BicepsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                        BicepsBodypart();
                    }
                }
            }
            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);
        }
        private void ChestBodypart()
        {
           
        }

        private void ChestBodypartUnSelected()
        {
            //BtnBiceps.BackgroundColor = Color.Transparent;
            //BtnChest.TextColor = Color.FromHex("#0C2432");
            //BtnBiceps.TextColor = Color.FromHex("#0C2432");
            //ChestGradient.BackgroundGradientStartColor = Color.Transparent;
            //ChestGradient.BackgroundGradientEndColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientStartColor = Color.Transparent;
            //BicepsGradient.BackgroundGradientEndColor = Color.Transparent;

            //BtnAbs.TextColor = Color.FromHex("#0C2432");
            //AbsGradient.BackgroundGradientStartColor = Color.Transparent;
            //AbsGradient.BackgroundGradientEndColor = Color.Transparent;
            //BtnAbs.BackgroundColor = Color.Transparent;
        }
        public async void BtnChestClicked(object sender, EventArgs args)
        {


            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
            {
                if (LocalDBManager.Instance.GetDBSetting("gender") != null && LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Chest");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Legs");
                ChestBodypart();
            }
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Chest")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        ChestBodypartUnSelected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Chest");
                        ChestBodypart();
                    }
                }
                else
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Legs")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        ChestBodypartUnSelected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Legs");
                        ChestBodypart();
                    }
                }
            }

            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);



        }
        private void AbsBodypart()
        {

     

        }
        private void AbsBodypartUnselected()
        {

        

        }

        void BtnAbs_Clicked(System.Object sender, System.EventArgs e)
        {
            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value))
            {
                if (LocalDBManager.Instance.GetDBSetting("gender") != null && LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Glutes");
                AbsBodypart();
            }
            else
            {
                if (LocalDBManager.Instance.GetDBSetting("gender").Value.Trim() == "Man")
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Abs")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        AbsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Abs");
                        AbsBodypart();
                    }
                }
                else
                {
                    if (LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value == "Glutes")
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        AbsBodypartUnselected();
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "Glutes");
                        AbsBodypart();
                    }
                }
            }
            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);

        }
        void PlatesListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            try
            {

                if (e.Item == null)
                    return;

                if (((PlateModel)e.Item).Id == -1)
                {
                    //Add new plates here
                    NewPlates("");
                }
                PlatesListView.SelectedItem = null;

            }
            catch (Exception ex)
            {

            }
        }

        void PlatesListView_ItemTappedHome(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((PlateModel)e.Item).Id == -1)
            {
                //Add new plates here
                NewPlates("Home");
            }
            PlatesListView.SelectedItem = null;
        }

        void PlatesListView_ItemTappedOther(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((PlateModel)e.Item).Id == -1)
            {
                //Add new plates here
                NewPlates("Other");
            }
            PlatesListView.SelectedItem = null;
        }

        void DumbbellsListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((DumbellModel)e.Item).Id == -1)
            {
                //Add new dumbbellss here
                NewDumbbells("");
            }
            DumbbellsListView.SelectedItem = null;
        }

        void PulleyListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((PulleyModel)e.Item).Id == -1)
            {
                NewPulley("");
            }
            PulleyListView.SelectedItem = null;
        }

        void PulleyListView1_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((PulleyModel)e.Item).Id == -1)
            {
                NewPulley("Home");
            }
            PulleyListView1.SelectedItem = null;
        }

        void PulleyListView2_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((PulleyModel)e.Item).Id == -1)
            {
                NewPulley("Other");
            }
            PulleyListView2.SelectedItem = null;
        }
        void BandsListView_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((BandsModel)e.Item).Id == -1)
            {
                NewBandsColor("");
            }
            BandsListView.SelectedItem = null;
        }
        void BandsListView1_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((BandsModel)e.Item).Id == -1)
            {
                NewBandsColor("Home");
            }
            BandsListView1.SelectedItem = null;
        }
        void BandsListView2_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((BandsModel)e.Item).Id == -1)
            {
                NewBandsColor("Other");
            }
            BandsListView2.SelectedItem = null;
        }

        void DumbbellsListView1_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((DumbellModel)e.Item).Id == -1)
            {
                //Add new dumbbellss here
                NewDumbbells("Home");
            }
            DumbbellsListView.SelectedItem = null;
        }

        void DumbbellsListView2_ItemTapped(object sender, ItemTappedEventArgs e)
        {
            if (e.Item == null)
                return;

            if (((DumbellModel)e.Item).Id == -1)
            {
                //Add new dumbbellss here
                NewDumbbells("Other");
            }
            DumbbellsListView.SelectedItem = null;
        }


        //flexibility buttons
        public async void BtnBeginnerClicked(object sender, EventArgs args)
        {
            LocalDBManager.Instance.SetDBSetting("MobilityLevel", "Beginner");
            Beginner();
            SetUserMobilityLevel("Beginner");
        }

        private void SetMobilityLevel()
        {
            if (LocalDBManager.Instance.GetDBSetting("MobilityLevel")?.Value == "Beginner")
                Beginner();
            else if (LocalDBManager.Instance.GetDBSetting("MobilityLevel")?.Value == "Intermediate")
                Intermediate();
            else if (LocalDBManager.Instance.GetDBSetting("MobilityLevel")?.Value == "Advanced")
                Advanced();
            else
                UnSelectMobilityLevel();
        }

        void UnSelectMobilityLevel()
        {
           
        }

        void Beginner()
        {
            
        }

        private async void SetUserMobilityLevel(string level)
        {
            var userModel = new UserInfosModel()
            {
                MobilityLevel = string.IsNullOrEmpty(level) ? LocalDBManager.Instance.GetDBSetting("MobilityLevel").Value : level,
            };

            
            await DrMuscleRestClient.Instance.SetUserMobilityLevel(userModel);

        }

        public async void BtnIntermediateClicked(object sender, EventArgs args)
        {
            LocalDBManager.Instance.SetDBSetting("MobilityLevel", "Intermediate");
            Intermediate();
            SetUserMobilityLevel("Intermediate");
        }

        void Intermediate()
        {
            

        }
        void BtnadvancedClicked(System.Object sender, System.EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("MobilityLevel", "Advanced");
            Advanced();
            SetUserMobilityLevel("Advanced");
        }
        void Advanced()
        {
           
        }

        private async void GeneratePulleyArray(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;


            if (weightType.ToLower().Contains("home"))
                pulleyItems1.Clear();
            else if (weightType.ToLower().Contains("other"))
                pulleyItems2.Clear();
            else
                pulleyItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PulleyModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    if (weightType.ToLower().Contains("home"))
                        pulleyItems1.Add(model);
                    else if (weightType.ToLower().Contains("other"))
                        pulleyItems2.Add(model);
                    else
                        pulleyItems.Add(model);
                }
            }
            if (weightType.ToLower().Contains("home"))
                pulleyItems1.Add(new PulleyModel() { Key = "Tap to enter new pulley increments", Id = -1 });
            else if (weightType.ToLower().Contains("other"))
                pulleyItems2.Add(new PulleyModel() { Key = "Tap to enter new pulley increments", Id = -1 });
            else
                pulleyItems.Add(new PulleyModel() { Key = "Tap to enter new pulley increments", Id = -1 });



            PulleyListView.HeightRequest = pulleyItems.Count * 45 + 1;

            PulleyListView1.HeightRequest = pulleyItems1.Count * 45 + 1;

            PulleyListView2.HeightRequest = pulleyItems2.Count * 45 + 1; ;
        }

        private async void GenerateBandsArray(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;


            if (weightType.ToLower().Contains("home"))
                bandsItems1.Clear();
            else if (weightType.ToLower().Contains("other"))
                bandsItems2.Clear();
            else
                bandsItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new BandsModel();
                if (pair.Length == 4)
                {
                    model.BandColor = pair[0];
                    model.Key = pair[1];
                    model.Value = Int32.Parse(pair[2]);
                    model.IsSystemPlates = pair[3] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    if (weightType.ToLower().Contains("home"))
                        bandsItems1.Add(model);
                    else if (weightType.ToLower().Contains("other"))
                        bandsItems2.Add(model);
                    else
                        bandsItems.Add(model);
                }
            }
            if (weightType.ToLower().Contains("home"))
                bandsItems1.Add(new BandsModel() { Key = "Tap to enter new bands", Id = -1 });
            else if (weightType.ToLower().Contains("other"))
                bandsItems2.Add(new BandsModel() { Key = "Tap to enter new bands", Id = -1 });
            else
                bandsItems.Add(new BandsModel() { Key = "Tap to enter new bands", Id = -1 });



            BandsListView.HeightRequest = bandsItems.Count * 45 + 1;

            BandsListView1.HeightRequest = bandsItems1.Count * 45 + 1;

            BandsListView2.HeightRequest = bandsItems2.Count * 45 + 1; ;
        }
        private async void GenerateDumbbellArray(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;


            if (weightType.ToLower().Contains("home"))
                dumbbellItems1.Clear();
            else if (weightType.ToLower().Contains("other"))
                dumbbellItems2.Clear();
            else
                dumbbellItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new DumbellModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    if (weightType.ToLower().Contains("home"))
                        dumbbellItems1.Add(model);
                    else if (weightType.ToLower().Contains("other"))
                        dumbbellItems2.Add(model);
                    else
                        dumbbellItems.Add(model);
                }
            }
            if (weightType.ToLower().Contains("home"))
                dumbbellItems1.Add(new DumbellModel() { Key = "Tap to enter new dumbbells", Id = -1 });
            else if (weightType.ToLower().Contains("other"))
                dumbbellItems2.Add(new DumbellModel() { Key = "Tap to enter new dumbbells", Id = -1 });
            else
                dumbbellItems.Add(new DumbellModel() { Key = "Tap to enter new dumbbells", Id = -1 });



            DumbbellsListView.HeightRequest = dumbbellItems.Count * 45 + 1;

            DumbbellsListView1.HeightRequest = dumbbellItems1.Count * 45 + 1;

            DumbbellsListView2.HeightRequest = dumbbellItems2.Count * 45 + 1; ;
        }

        private async void GeneratePlatesArray(string weightType)
        {

            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            if (weightType.ToLower().Contains("home"))
                platesItems1.Clear();
            else if (weightType.ToLower().Contains("other"))
                platesItems2.Clear();
            else
                platesItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PlateModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    if (weightType.ToLower().Contains("home"))
                        platesItems1.Add(model);
                    else if (weightType.ToLower().Contains("other"))
                        platesItems2.Add(model);
                    else
                        platesItems.Add(model);
                }
            }
            if (weightType.ToLower().Contains("home"))
                platesItems1.Add(new PlateModel() { Key = AppResources.TapToEnterNewPlates, Id = -1 });
            else if (weightType.ToLower().Contains("other"))
                platesItems2.Add(new PlateModel() { Key = AppResources.TapToEnterNewPlates, Id = -1 });
            else
                platesItems.Add(new PlateModel() { Key = AppResources.TapToEnterNewPlates, Id = -1 });


            PlatesListView.HeightRequest = platesItems.Count * 45;

            PlatesListView1.HeightRequest = platesItems1.Count * 45;

            PlatesListView2.HeightRequest = platesItems2.Count * 45;
        }

        private async void GenerateDumbbellArrayHome(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            dumbbellItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new DumbellModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    dumbbellItems.Add(model);

                }
            }
            platesItems.Add(new PlateModel()
            { Key = "Tap to enter new dumbbells", Id = -1 });
            
            DumbbellsListView1.HeightRequest = dumbbellItems.Count * 45;
        }

        private async void GeneratePlatesArrayHome(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            platesItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PlateModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    platesItems.Add(model);
                }
            }
            platesItems.Add(new PlateModel()
            { Key = AppResources.TapToEnterNewPlates, Id = -1 });
            
            PlatesListView1.HeightRequest = platesItems.Count * 45;
            DumbbellsListView1.HeightRequest = dumbbellItems.Count * 45;
        }

        private async void GenerateDumbbellArrayOther(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;

            dumbbellItems.Clear();
            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new DumbellModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    dumbbellItems.Add(model);
                }
            }
            platesItems.Add(new PlateModel()
            { Key = "Tap to enter new dumbbells", Id = -1 });
            
            DumbbellsListView2.HeightRequest = dumbbellItems.Count * 45;
        }
        private async void GeneratePlatesArrayOther(string weightType)
        {
            var keyVal = LocalDBManager.Instance.GetDBSetting(weightType).Value;
            platesItems.Clear();

            string[] items = keyVal.Split('|');
            foreach (var item in items)
            {
                string[] pair = item.Split('_');
                var model = new PlateModel();
                if (pair.Length == 3)
                {
                    model.Key = pair[0];
                    model.Value = Int32.Parse(pair[1]);
                    model.IsSystemPlates = pair[2] == "True" ? true : false;
                    model.WeightType = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                    platesItems.Add(model);
                }
            }
            platesItems.Add(new PlateModel()
            { Key = AppResources.TapToEnterNewPlates, Id = -1 });
            
            PlatesListView2.HeightRequest = platesItems.Count * 45;
        }

        private async Task SetUserEquipmentSettings()
        {
            var model = new EquipmentModel()
            {
                IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
                IsDumbbellEnabled = LocalDBManager.Instance.GetDBSetting("Dumbbell").Value == "true",
                IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true",
                IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
                IsBands = LocalDBManager.Instance.GetDBSetting("Bands").Value == "true",
                IsHomeEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true",
                IsHomeChinupBar = LocalDBManager.Instance.GetDBSetting("HomeChinUp").Value == "true",
                IsHomeDumbbell = LocalDBManager.Instance.GetDBSetting("HomeDumbbell").Value == "true",
                IsHomePlate = LocalDBManager.Instance.GetDBSetting("HomePlate").Value == "true",
                IsHomePully = LocalDBManager.Instance.GetDBSetting("HomePully").Value == "true",
                IsHomeBands = LocalDBManager.Instance.GetDBSetting("HomeBands")?.Value == "true",
                IsOtherEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true",
                IsOtherChinupBar = LocalDBManager.Instance.GetDBSetting("OtherChinUp").Value == "true",
                IsOtherDumbbell = LocalDBManager.Instance.GetDBSetting("OtherDumbbell").Value == "true",
                IsOtherPlate = LocalDBManager.Instance.GetDBSetting("OtherPlate").Value == "true",
                IsOtherPully = LocalDBManager.Instance.GetDBSetting("OtherPully").Value == "true",
                IsOtherBands = LocalDBManager.Instance.GetDBSetting("OtherBands")?.Value == "true",
            };

            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true" && model.IsEquipmentEnabled)
                model.Active = "gym";
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true" && model.IsHomeEquipmentEnabled)
                model.Active = "home";
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true" && model.IsOtherEquipmentEnabled)
                model.Active = "other";
            if (string.IsNullOrEmpty(model.Active))
            {
                var hmm = "";
                if (model.IsOtherEquipmentEnabled)
                    hmm = "other";
                if (model.IsHomeEquipmentEnabled)
                    hmm = "home";
                if (model.IsEquipmentEnabled)
                    hmm = "gym";
                model.Active = hmm;
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", hmm == "other" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", hmm == "home" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("GymEquipment", hmm == "gym" ? "true" : "");
            }

            if (IsOnAppeatingFinished)
            {
                IsEquipmentEdited = true;
                await DrMuscleRestClient.Instance.SetUserEquipmentSettings(model);
            }
        }

        private async Task SetUserEquipmentSettingsWithLoader()
        {
            var model = new EquipmentModel()
            {
                IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
                IsDumbbellEnabled = LocalDBManager.Instance.GetDBSetting("Dumbbell").Value == "true",
                IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true",
                IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
                IsBands = LocalDBManager.Instance.GetDBSetting("Bands")?.Value == "true",
                IsHomeEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true",
                IsHomeChinupBar = LocalDBManager.Instance.GetDBSetting("HomeChinUp").Value == "true",
                IsHomeDumbbell = LocalDBManager.Instance.GetDBSetting("HomeDumbbell").Value == "true",
                IsHomePlate = LocalDBManager.Instance.GetDBSetting("HomePlate").Value == "true",
                IsHomePully = LocalDBManager.Instance.GetDBSetting("HomePully").Value == "true",
                IsHomeBands = LocalDBManager.Instance.GetDBSetting("HomeBands")?.Value == "true",
                IsOtherEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true",
                IsOtherChinupBar = LocalDBManager.Instance.GetDBSetting("OtherChinUp").Value == "true",
                IsOtherDumbbell = LocalDBManager.Instance.GetDBSetting("OtherDumbbell").Value == "true",
                IsOtherPlate = LocalDBManager.Instance.GetDBSetting("OtherPlate").Value == "true",
                IsOtherPully = LocalDBManager.Instance.GetDBSetting("OtherPully").Value == "true",
                IsOtherBands = LocalDBManager.Instance.GetDBSetting("OtherBands")?.Value == "true",
            };

            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true" && model.IsEquipmentEnabled)
                model.Active = "gym";
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true" && model.IsHomeEquipmentEnabled)
                model.Active = "home";
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true" && model.IsOtherEquipmentEnabled)
                model.Active = "other";
            if (string.IsNullOrEmpty(model.Active))
            {
                var hmm = "";
                if (model.IsOtherEquipmentEnabled)
                    hmm = "other";
                if (model.IsHomeEquipmentEnabled)
                    hmm = "home";
                if (model.IsEquipmentEnabled)
                    hmm = "gym";
                model.Active = hmm;
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", hmm == "other" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", hmm == "home" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("GymEquipment", hmm == "gym" ? "true" : "");
            }

            if (IsOnAppeatingFinished)
            {
                IsEquipmentEdited = true;
                await DrMuscleRestClient.Instance.SetUserEquipmentSettings(model);
            }
        }

       
        private async void UpdateMassUnit(string munit)
        {
            await DrMuscleRestClient.Instance.SetUserMassUnit(new UserInfosModel()
            {
                MassUnit = munit
            });
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                {
                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    var weight1 = new MultiUnityWeight(value, "kg");
                }
                if (IsOnAppeatingFinished)
                {
                    if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                    {
                        GeneratePlatesArray("PlatesKg");
                        GeneratePlatesArray("HomePlatesKg");
                        GeneratePlatesArray("OtherPlatesKg");
                    }
                    else
                    {
                        GeneratePlatesArray("PlatesLb");
                        GeneratePlatesArray("HomePlatesLb");
                        GeneratePlatesArray("OtherPlatesLb");
                    }

                    if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
                    {
                        GenerateDumbbellArray("DumbbellKg");
                        GenerateDumbbellArray("HomeDumbbellKg");
                        GenerateDumbbellArray("OtherDumbbellKg");

                        GeneratePulleyArray("PulleyKg");
                        GeneratePulleyArray("HomePulleyKg");
                        GeneratePulleyArray("OtherPulleyKg");
                    }
                    else
                    {
                        GenerateDumbbellArray("DumbbellLb");
                        GenerateDumbbellArray("HomeDumbbellLb");
                        GenerateDumbbellArray("OtherDumbbellLb");

                        GeneratePulleyArray("PulleyLb");
                        GeneratePulleyArray("HomePulleyLb");
                        GeneratePulleyArray("OtherPulleyLb");
                    }

                    MultiUnityWeight increments = null;
                    MultiUnityWeight max = null;
                    MultiUnityWeight min = null;
                    if (LocalDBManager.Instance.GetDBSetting("SliderValue") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("SliderValue").Value != null)
                        {
                          
                        }
                    }
                    if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("workout_increments").Value != null)
                        {
                            var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                            increments = new MultiUnityWeight(value, "kg");
                            if (munit == "lb")
                            {
                                foreach (var item in platesItems.Reverse())
                                {
                                    if (item.Id == -1)
                                        continue;
                                    if (Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture) >= (int)increments.Lb)
                                    {
                                        increments = new MultiUnityWeight(Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture), "lb");
                                        _increments = increments;
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                foreach (var item in platesItems.Reverse())
                                {
                                    if (item.Id == -1)
                                        continue;
                                    if (Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture) >= increments.Kg)
                                    {
                                        increments = new MultiUnityWeight(Convert.ToDecimal(item.Key, System.Globalization.CultureInfo.InvariantCulture), "kg");
                                        _increments = increments;
                                        break;
                                    }
                                }
                            }

                        }
                    }
                    

                   
                    await DrMuscleRestClient.Instance.SetUserIncrements(new UserInfosModel()
                    {
                        Increments = increments,
                        Max = max,
                        Min = min
                    });
                }
            }
            catch (Exception ex)
            {

            }
            Xamarin.Forms.MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage() { OnlyRefresh = true }, "UpdatedWorkoutMessage");
        }


        private async void UpdateSetSetyle(bool? IsNormal)
        {
            await DrMuscleRestClient.Instance.SetUserSetStyle(new UserInfosModel()
            {
                IsNormalSet = IsNormal
            });
        }
        private async void UpdatePyramidSetStyle(bool isPyramid)
        {
            await DrMuscleRestClient.Instance.SetUserPyramidSetStyle(new UserInfosModel()
            {
                IsPyramid = isPyramid
            });
        }

        private void setLocale()
        {
            var localize = DependencyService.Get<ILocalize>();
            if (localize != null)
            {
                localize.SetLocale(new CultureInfo(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value));
                ResourceLoader.Instance.SetCultureInfo(new CultureInfo(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value));
                MessagingCenter.Send(new LanguageChangeMessage(), "LocalizeUpdated");
            }
        }



        public void LoadCustomReps()
        {
            
        }

        void PlateItems_Unfocused(object sender, Xamarin.Forms.FocusEventArgs e)
        {
            //Do code for save new values for next time
            var mi = ((Entry)sender);
            PlateModel m = (PlateModel)mi.BindingContext;

        }

        void UnitEntry_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {

                var entry = (Entry)sender;
                const string textRegex = @"^\d+(?:[\.,]\d{0,2})?$";
                var text = e.NewTextValue.Replace(",", ".");
                bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
                if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
                {
                    entry.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
                }

            }
            catch (Exception ex)
            {

            }
        }

        //TODO: Plates
        void OnCancelClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            s.Children[0].IsVisible = false;
            s.Children[1].IsVisible = false;
            s.Children[2].IsVisible = false;
            s.Children[3].IsVisible = true;
        }

        void OnContextMenuClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                if (m.IsSystemPlates)
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = false;
                    s.Children[3].IsVisible = false;
                }
                else
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = true;
                    s.Children[3].IsVisible = false;
                }
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                if (m.IsSystemPlates)
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = false;
                    s.Children[3].IsVisible = false;
                }
                else
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = true;
                    s.Children[3].IsVisible = false;
                }
            }
            else if (mi.CommandParameter is PulleyModel)
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                if (m.IsSystemPlates)
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = false;
                    s.Children[3].IsVisible = false;
                }
                else
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = true;
                    s.Children[3].IsVisible = false;
                }
            }
            else if (mi.CommandParameter is BandsModel)
            {
                BandsModel m = (BandsModel)mi.CommandParameter;
                if (m.IsSystemPlates)
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = false;
                    s.Children[3].IsVisible = false;
                }
                else
                {
                    s.Children[0].IsVisible = true;
                    s.Children[1].IsVisible = true;
                    s.Children[2].IsVisible = true;
                    s.Children[3].IsVisible = false;
                }
            }
        }
        public void OnEdit(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditCounts(m, "");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{AppResources.EditPlateWeight}",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditCounts(m, "");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditDumbbellCounts(m, "");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit dumbbell weight",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditDumbbellCounts(m, "");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is PulleyModel)
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditPulleyCounts(m, "");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit pulley weight",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditPulleyCounts(m, "");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is BandsModel)
            {
                BandsModel m = (BandsModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditBandsCounts(m, "");
                    return;
                }
                //Edit workout log
                PromptConfig editColorPopup = new PromptConfig()
                {
                    IsCancellable = true,
                    Title = $"New bands color",
                    MaxLength = 18,
                    Placeholder = "Enter color",
                    Text = m.BandColor,
                    OkText = AppResources.Add,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (!weightResponse.Ok)
                            return;
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) )
                        {
                            return;
                        }
                        if (weightResponse.Value.Contains("_"))
                        {
                            UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Title = AppResources.Error,
                                Message = "Special character not allow in color name.",
                                OkText = AppResources.Ok
                            });

                            return;
                        }

                        m.BandColor = weightResponse.Value.ToString().Trim().FirstCharToUpper();
                        AskForEditBandsWeight(m, "");
                    }
                    
                
                };
                UserDialogs.Instance.Prompt(editColorPopup);
                
                
                
            }
        }

        public void AskForEditBandsWeight(BandsModel m, string profile)
        {
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"Edit bands weight",
                MaxLength = 6,
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Text = m.Key.ToString().ReplaceWithDot(),
                OkText = AppResources.Edit,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    AskForEditBandsCounts(m, profile);

                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        public void OnEditHome(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditCounts(m, "Home");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{AppResources.EditPlateWeight}",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditCounts(m, "Home");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditDumbbellCounts(m, "Home");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit dumbbell weight",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditDumbbellCounts(m, "Home");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is PulleyModel)
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditPulleyCounts(m, "Home");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"Edit pulley weight",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditPulleyCounts(m, "Home");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is BandsModel)
            {
                BandsModel m = (BandsModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditBandsCounts(m, "Home");
                    return;
                }
                //Edit workout log
                PromptConfig editColorPopup = new PromptConfig()
                {
                    IsCancellable = true,
                    Title = $"New bands color",
                    MaxLength = 18,
                    Placeholder = "Enter color",
                    Text = m.BandColor,
                    OkText = AppResources.Add,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (!weightResponse.Ok)
                            return;
                        if (string.IsNullOrWhiteSpace(weightResponse.Value))
                        {
                            return;
                        }
                        if (weightResponse.Value.Contains("_"))
                        {
                            UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Title = AppResources.Error,
                                Message = "Special character not allow in color name.",
                                OkText = AppResources.Ok
                            });

                            return;
                        }

                        m.BandColor = weightResponse.Value.ToString().Trim().FirstCharToUpper();
                        AskForEditBandsWeight(m, "Home");
                    }


                };
                UserDialogs.Instance.Prompt(editColorPopup);



            }
        }

        public void OnEditOther(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            bool isPlates = false;
            if (mi.CommandParameter is PlateModel)
                isPlates = true;
            if (isPlates)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditCounts(m, "Other");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{AppResources.EditPlateWeight}",
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditCounts(m, "Other");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                //Dumbbell
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditDumbbellCounts(m, "Other");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{"Edit dumbbell weight"}",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditDumbbellCounts(m, "Other");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is PulleyModel)
            {
                //Dumbbell
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditPulleyCounts(m, "Other");
                    return;
                }
                //Edit workout log
                var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
                PromptConfig firsttimeExercisePopup = new PromptConfig()
                {
                    InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                    IsCancellable = true,
                    Title = $"{"Edit pulley weight"}",
                    MaxLength = 6,
                    Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                    Text = m.Key.ToString().ReplaceWithDot(),
                    OkText = AppResources.Edit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                        {
                            return;
                        }
                        var weightText = weightResponse.Value.Replace(",", ".");
                        decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        m.Key = weight1.ToString().ReplaceWithDot();
                        AskForEditPulleyCounts(m, "Other");

                    }
                };

                firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
                UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            else if (mi.CommandParameter is BandsModel)
            {
                BandsModel m = (BandsModel)mi.CommandParameter;
                OnCancelClicked(sender, e);

                if (m.IsSystemPlates)
                {
                    AskForEditBandsCounts(m, "Other");
                    return;
                }
                //Edit workout log
                PromptConfig editColorPopup = new PromptConfig()
                {
                    IsCancellable = true,
                    Title = $"New bands color",
                    MaxLength = 18,
                    Placeholder = "Enter color",
                    Text = m.BandColor,
                    OkText = AppResources.Add,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OnAction = (weightResponse) =>
                    {
                        if (!weightResponse.Ok)
                            return;
                        if (string.IsNullOrWhiteSpace(weightResponse.Value))
                        {
                            return;
                        }
                        if (weightResponse.Value.Contains("_"))
                        {
                            UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Title = AppResources.Error,
                                Message = "Special character not allow in color name.",
                                OkText = AppResources.Ok
                            });

                            return;
                        }

                        m.BandColor = weightResponse.Value.ToString().Trim().FirstCharToUpper();
                        AskForEditBandsWeight(m, "Other");
                    }


                };
                UserDialogs.Instance.Prompt(editColorPopup);



            }
        }
        void FirsttimeExercisePopup_OnTextChanged(PromptTextChangedArgs obj)
        {

            const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
            var text = obj.Value.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
            {
                double result;
                obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
                double.TryParse(obj.Value, out result);
                obj.Value = result.ToString();
            }
        }
        void ExerciseRepsPopup_OnTextChanged(PromptTextChangedArgs obj)
        {
            const string textRegex = @"^\d+(?:)?$";
            bool IsValid = Regex.IsMatch(obj.Value, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
            {
                double result;
                obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
                double.TryParse(obj.Value, out result);
                obj.Value = result.ToString();
            }
        }
        public void OnDelete(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = AppResources.DeletePlates,
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            platesItems.Remove(m);
                            PlatesListView.HeightRequest = platesItems.Count * 45;
                            SaveEquipments("");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete dumbbells",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            dumbbellItems.Remove(m);
                            DumbbellsListView.HeightRequest = dumbbellItems.Count * 45;
                            SaveDumbellEquipments("");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is PulleyModel)
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete pulleys",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            pulleyItems.Remove(m);
                            PulleyListView.HeightRequest = pulleyItems.Count * 45;
                            SavePulleyEquipments("");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is BandsModel)
            {
                BandsModel m = (BandsModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete Band",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            bandsItems.Remove(m);
                            BandsListView.HeightRequest = bandsItems.Count * 45;
                            SaveBandsEquipments("");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
        }

        public void OnDeleteHome(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = AppResources.DeletePlates,
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            platesItems1.Remove(m);
                            PlatesListView1.HeightRequest = platesItems.Count * 45;
                            SaveEquipments("Home");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete dumbbells",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            dumbbellItems1.Remove(m);
                            DumbbellsListView1.HeightRequest = dumbbellItems1.Count * 45;
                            SaveDumbellEquipments("Home");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is PulleyModel)
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete pulley",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            pulleyItems1.Remove(m);
                            PulleyListView1.HeightRequest = pulleyItems1.Count * 45;
                            SavePulleyEquipments("Home");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is BandsModel)
            {
                BandsModel m = (BandsModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete Band",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            bandsItems1.Remove(m);
                            BandsListView1.HeightRequest = bandsItems1.Count * 45;
                            SaveBandsEquipments("Home");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
        }

        public void OnDeleteOther(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is PlateModel)
            {
                PlateModel m = (PlateModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = AppResources.DeletePlates,
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            platesItems2.Remove(m);
                            PlatesListView2.HeightRequest = platesItems.Count * 45;
                            SaveEquipments("Other");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is DumbellModel)
            {
                DumbellModel m = (DumbellModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete dumbbells",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            dumbbellItems2.Remove(m);
                            DumbbellsListView2.HeightRequest = dumbbellItems2.Count * 45;
                            SaveDumbellEquipments("Other");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is PulleyModel)
            {
                PulleyModel m = (PulleyModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete pulleys",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            pulleyItems2.Remove(m);
                            PulleyListView2.HeightRequest = pulleyItems2.Count * 45;
                            SavePulleyEquipments("Other");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
            else if (mi.CommandParameter is BandsModel)
            {
                BandsModel m = (BandsModel)mi.CommandParameter;
                ConfirmConfig p = new ConfirmConfig()
                {
                    Title = "Delete Band",
                    Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
                    OkText = AppResources.Delete,
                    CancelText = AppResources.Cancel,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };
                p.OnAction = (obj) =>
                {
                    if (obj)
                    {
                        //Delete Here
                        if (!m.IsSystemPlates)
                        {
                            bandsItems2.Remove(m);
                            BandsListView2.HeightRequest = bandsItems2.Count * 45;
                            SaveBandsEquipments("Other");
                        }
                        //Save
                    }
                };
                UserDialogs.Instance.Confirm(p);
            }
        }
        void AskForEditPulleyCounts(PulleyModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 5,
                Text = m.Value.ToString(),
                OkText = "Edit equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok || string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        //Save new value
                        SavePulleyEquipments(profile);

                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        void AskForEditBandsCounts(BandsModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 5,
                Text = m.Value.ToString(),
                OkText = "Edit equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok || string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        //Save new value
                        SaveBandsEquipments(profile);

                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        void AskForEditDumbbellCounts(DumbellModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 5,
                Text = m.Value.ToString(),
                OkText = "Edit equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok || string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        //Save new value
                        SaveDumbellEquipments(profile);

                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }


        void AskForEditCounts(PlateModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 5,
                Text = m.Value.ToString(),
                OkText = "Edit equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok || string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        //Save new value
                        SaveEquipments(profile);

                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void NewDumbbells(string profile)
        {
            DumbellModel m = new DumbellModel();
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            m.WeightType = massUnit;
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"Add dumbbell weight",
                MaxLength = 6,
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Placeholder = AppResources.EnterWeights,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    m.Value = 2;
                    AddDumbbellCounts(m, profile);
                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void NewPulley(string profile)
        {
            PulleyModel m = new PulleyModel();
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            m.WeightType = massUnit;
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"Add pulley weight",
                MaxLength = 6,
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Placeholder = AppResources.EnterWeights,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    AddPulleyCounts(m, profile);
                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }
        private async void NewBandsColor(string profile)
        {
            BandsModel m = new BandsModel();
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                IsCancellable = true,
                Title = $"New bands color",
                MaxLength = 18,
                Placeholder = "Enter color",
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) )
                    {
                        return;
                    }
                    if (weightResponse.Value.Contains("_"))
                    {
                         UserDialogs.Instance.AlertAsync(new AlertConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Title = AppResources.Error,
                            Message = "Special character not allow in color name.",
                            OkText = AppResources.Ok
                        });

                        return;
                    }

                    m.BandColor = weightResponse.Value.ToString().Trim().FirstCharToUpper();
                    NewBands(m, profile);
                    }
                
            };

            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }
        private async void NewBands(BandsModel m, string profile)
        {
            try
            {

            
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            m.WeightType = massUnit;
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"New bands tension",
                MaxLength = 6,
                Placeholder = "Enter tension",
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    AddBandsCounts(m, profile);
                    }
                
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            }
            catch (Exception ex)
            {

            }
        }


        private async void NewPlates(string profile)
        {
            
            PlateModel m = new PlateModel();
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            m.WeightType = massUnit;
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"{AppResources.AddPlateWeight}",
                MaxLength = 5,
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Placeholder = AppResources.EnterWeights,
                OkText = AppResources.Add,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 0)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Key = weight1.ToString().ReplaceWithDot();
                    AddPlatesCounts(m, profile);
                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void AddPulleyCounts(PulleyModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 6,
                OkText = "Add equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int dumbbellCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = dumbbellCount;
                        m.IsSystemPlates = false;
                        if (profile == "")
                        {
                            pulleyItems.Insert(pulleyItems.Count - 1, m);
                            PulleyListView.HeightRequest = pulleyItems.Count * 45;
                        }
                        if (profile == "Home")
                        {
                            pulleyItems1.Insert(pulleyItems1.Count - 1, m);
                            PulleyListView1.HeightRequest = pulleyItems1.Count * 45;
                        }
                        if (profile == "Other")
                        {
                            pulleyItems2.Insert(pulleyItems2.Count - 1, m);
                            PulleyListView2.HeightRequest = pulleyItems2.Count * 45;
                        }
                        SavePulleyEquipments(profile);
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }


        private async void AddBandsCounts(BandsModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 6,
                OkText = "Add equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int bandsCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = bandsCount;
                        m.IsSystemPlates = false;
                        if (profile == "")
                        {
                            bandsItems.Insert(bandsItems.Count - 1, m);
                            BandsListView.HeightRequest = bandsItems.Count * 45;
                        }
                        if (profile == "Home")
                        {
                            bandsItems1.Insert(bandsItems1.Count - 1, m);
                            BandsListView1.HeightRequest = bandsItems1.Count * 45;
                        }
                        if (profile == "Other")
                        {
                            bandsItems2.Insert(bandsItems2.Count - 1, m);
                            BandsListView2.HeightRequest = bandsItems2.Count * 45;
                        }
                        SaveBandsEquipments(profile);
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }
        
        private async void AddDumbbellCounts(DumbellModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 6,
                Text = m.Value.ToString(),
                OkText = "Add equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int dumbbellCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = dumbbellCount;
                        m.IsSystemPlates = false;
                        if (profile == "")
                        {
                            dumbbellItems.Insert(dumbbellItems.Count - 1, m);
                            DumbbellsListView.HeightRequest = dumbbellItems.Count * 45;
                        }
                        if (profile == "Home")
                        {
                            dumbbellItems1.Insert(dumbbellItems1.Count - 1, m);
                            DumbbellsListView1.HeightRequest = dumbbellItems1.Count * 45;
                        }
                        if (profile == "Other")
                        {
                            dumbbellItems2.Insert(dumbbellItems2.Count - 1, m);
                            DumbbellsListView2.HeightRequest = dumbbellItems2.Count * 45;
                        }
                        SaveDumbellEquipments(profile);
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        private async void AddPlatesCounts(PlateModel m, string profile)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = "How many do you have?",
                Placeholder = "Enter count",
                MaxLength = 5,
                OkText = "Add equipment",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int plateCount = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Value = plateCount;
                        m.IsSystemPlates = false;
                        if (profile == "")
                        {
                            platesItems.Insert(platesItems.Count - 1, m);
                            PlatesListView.HeightRequest = platesItems.Count * 45;
                        }
                        if (profile == "Home")
                        {
                            platesItems1.Insert(platesItems1.Count - 1, m);
                            PlatesListView1.HeightRequest = platesItems1.Count * 45;
                        }
                        if (profile == "Other")
                        {
                            platesItems2.Insert(platesItems2.Count - 1, m);
                            PlatesListView2.HeightRequest = platesItems2.Count * 45;
                        }
                        SaveEquipments(profile);
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        void Reminder_Click(object sender, EventArgs args)
        {
            PopupNavigation.Instance.PushAsync(new ReminderPopup());
        }

        private async void SaveBandsEquipments(string profile)
        {
            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                if (profile == "")
                {
                    var kgString = "";
                    foreach (var item in bandsItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.BandColor}_{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}BandsKg", kgString);

                }
                else if (profile == "Home")
                {
                    var kgString = "";
                    foreach (var item in bandsItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.BandColor}_{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}BandsKg", kgString);
                }
                else if (profile == "Other")
                {
                    var kgString = "";
                    foreach (var item in bandsItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.BandColor}_{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}BandsKg", kgString);
                }
                

            }
            else
            {
                if (profile == "")
                {
                    var lbString = "";
                    foreach (var item in bandsItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.BandColor}_{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}BandsLb", lbString);
                }
                else if (profile == "Home")
                {
                    var lbString = "";
                    foreach (var item in bandsItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.BandColor}_{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}BandsLb", lbString);
                }
                else if (profile == "Other")
                {
                    var lbString = "";
                    foreach (var item in bandsItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.BandColor}_{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}BandsLb", lbString);
                }
            }
            try
            {
                await DrMuscleRestClient.Instance.SetUserEquipmentBandsSettings(new EquipmentModel()
                {
                    AvilableBands = LocalDBManager.Instance.GetDBSetting($"BandsKg")?.Value,
                    AvilableHomeBands = LocalDBManager.Instance.GetDBSetting($"HomeBandsKg")?.Value,
                    AvilableOtherBands = LocalDBManager.Instance.GetDBSetting($"OtherBandsKg")?.Value,
                    AvilableLbBands = LocalDBManager.Instance.GetDBSetting($"BandsLb")?.Value,
                    AvilableHomeLbBands = LocalDBManager.Instance.GetDBSetting($"HomeBandsLb")?.Value,
                    AvilableOtherLbBands = LocalDBManager.Instance.GetDBSetting($"OtherBandsLb")?.Value,
                });
            }
            catch (Exception ex)
            {

            }
        }
        private async void SavePulleyEquipments(string profile)
        {
            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                if (profile == "")
                {
                    var kgString = "";
                    foreach (var item in pulleyItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyKg", kgString);

                }
                else if (profile == "Home")
                {
                    var kgString = "";
                    foreach (var item in pulleyItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyKg", kgString);
                }
                else if (profile == "Other")
                {
                    var kgString = "";
                    foreach (var item in pulleyItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyKg", kgString);
                }
                try
                {
                    await DrMuscleRestClient.Instance.SetUserEquipmentPulleySettings(new EquipmentModel()
                    {
                        AvilablePulley = LocalDBManager.Instance.GetDBSetting($"PulleyKg")?.Value,
                        AvilableHomePulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyKg")?.Value,
                        AvilableOtherPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyKg")?.Value,
                        AvilableLbPulley = LocalDBManager.Instance.GetDBSetting($"PulleyLb")?.Value,
                        AvilableHomeLbPulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyLb")?.Value,
                        AvilableOtherLbPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyLb")?.Value,

                    });
                }
                catch (Exception ex)
                {

                }

            }
            else
            {
                if (profile == "")
                {
                    var lbString = "";
                    foreach (var item in pulleyItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyLb", lbString);
                }
                else if (profile == "Home")
                {
                    var lbString = "";
                    foreach (var item in pulleyItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyLb", lbString);
                }
                else if (profile == "Other")
                {
                    var lbString = "";
                    foreach (var item in pulleyItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PulleyLb", lbString);
                }
                try
                {
                    await DrMuscleRestClient.Instance.SetUserEquipmentPulleySettings(new EquipmentModel()
                    {
                        AvilablePulley = LocalDBManager.Instance.GetDBSetting($"PulleyKg")?.Value,
                        AvilableHomePulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyKg")?.Value,
                        AvilableOtherPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyKg")?.Value,
                        AvilableLbPulley = LocalDBManager.Instance.GetDBSetting($"PulleyLb")?.Value,
                        AvilableHomeLbPulley = LocalDBManager.Instance.GetDBSetting($"HomePulleyLb")?.Value,
                        AvilableOtherLbPulley = LocalDBManager.Instance.GetDBSetting($"OtherPulleyLb")?.Value,
                    });
                }
                catch (Exception ex)
                {

                }

            }
        }


        private async void SaveDumbellEquipments(string profile)
        {
            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                if (profile == "")
                {
                    var kgString = "";
                    foreach (var item in dumbbellItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellKg", kgString);

                }
                else if (profile == "Home")
                {
                    var kgString = "";
                    foreach (var item in dumbbellItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellKg", kgString);
                }
                else if (profile == "Other")
                {
                    var kgString = "";
                    foreach (var item in dumbbellItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellKg", kgString);
                }
                try
                {
                    await DrMuscleRestClient.Instance.SetUserEquipmentDumbbellSettings(new EquipmentModel()
                    {
                        AvilableDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellKg")?.Value,
                        AvilableHomeDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellKg")?.Value,
                        AvilableOtherDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellKg")?.Value,
                        AvilableLbDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellLb")?.Value,
                        AvilableHomeLbDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellLb")?.Value,
                        AvilableOtherLbDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellLb")?.Value,

                    });
                }
                catch (Exception ex)
                {

                }
                
            }
            else
            {
                if (profile == "")
                {
                    var lbString = "";
                    foreach (var item in dumbbellItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellLb", lbString);
                }
                else if (profile == "Home")
                {
                    var lbString = "";
                    foreach (var item in dumbbellItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellLb", lbString);
                }
                else if (profile == "Other")
                {
                    var lbString = "";
                    foreach (var item in dumbbellItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}DumbbellLb", lbString);
                }
                try
                {
                    await DrMuscleRestClient.Instance.SetUserEquipmentDumbbellSettings(new EquipmentModel()
                    {
                        AvilableDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellKg")?.Value,
                        AvilableHomeDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellKg")?.Value,
                        AvilableOtherDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellKg")?.Value,
                        AvilableLbDumbbell = LocalDBManager.Instance.GetDBSetting($"DumbbellLb")?.Value,
                        AvilableHomeLbDumbbell = LocalDBManager.Instance.GetDBSetting($"HomeDumbbellLb")?.Value,
                        AvilableOtherLbDumbbell = LocalDBManager.Instance.GetDBSetting($"OtherDumbbellLb")?.Value,
                    });
                }
                catch (Exception ex)
                {

                }
                
            }
        }

        private async void SaveEquipments(string profile)
        {
            if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            {
                if (profile == "")
                {
                    var kgString = "";
                    foreach (var item in platesItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesKg", kgString);

                }
                else if (profile == "Home")
                {
                    var kgString = "";
                    foreach (var item in platesItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesKg", kgString);
                }
                else if (profile == "Other")
                {
                    var kgString = "";
                    foreach (var item in platesItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (kgString != null)
                            kgString += "|";
                        kgString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesKg", kgString);
                }
                try
                {
                    await DrMuscleRestClient.Instance.SetUserEquipmentPlateSettings(new EquipmentModel()
                    {
                        AvilablePlate = LocalDBManager.Instance.GetDBSetting($"PlatesKg")?.Value,
                        AvilableHomePlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesKg")?.Value,
                        AvilableOtherPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesKg")?.Value,
                        AvilableLbPlate = LocalDBManager.Instance.GetDBSetting($"PlatesLb")?.Value,
                        AvilableHomeLbPlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesLb")?.Value,
                        AvilableOtherLbPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesLb")?.Value,

                    });
                }
                catch (Exception ex)
                {

                }
            }
            else
            {
                if (profile == "")
                {
                    var lbString = "";
                    foreach (var item in platesItems)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesLb", lbString);
                }
                else if (profile == "Home")
                {
                    var lbString = "";
                    foreach (var item in platesItems1)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesLb", lbString);
                }
                else if (profile == "Other")
                {
                    var lbString = "";
                    foreach (var item in platesItems2)
                    {
                        if (item.Id == -1)
                            continue;
                        if (lbString != null)
                            lbString += "|";
                        lbString += $"{item.Key}_{item.Value}_{item.IsSystemPlates}";
                    }
                    LocalDBManager.Instance.SetDBSetting($"{profile}PlatesLb", lbString);
                }
                try
                {
                    await DrMuscleRestClient.Instance.SetUserEquipmentPlateSettings(new EquipmentModel()
                    {
                        AvilablePlate = LocalDBManager.Instance.GetDBSetting($"PlatesKg")?.Value,
                        AvilableHomePlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesKg")?.Value,
                        AvilableOtherPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesKg")?.Value,
                        AvilableLbPlate = LocalDBManager.Instance.GetDBSetting($"PlatesLb")?.Value,
                        AvilableHomeLbPlate = LocalDBManager.Instance.GetDBSetting($"HomePlatesLb")?.Value,
                        AvilableOtherLbPlate = LocalDBManager.Instance.GetDBSetting($"OtherPlatesLb")?.Value,

                    });
                }
                catch (Exception ex)
                {

                }
            }
        }
        //
        void MobilityRepsEntry_TextChanged(System.Object sender, Xamarin.Forms.TextChangedEventArgs e)
        {
            try
            {

                var entry = (Entry)sender;
                const string textRegex = @"^\d+(?:)?$";
                var text = e.NewTextValue.Replace(",", ".");
                bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
                if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
                {
                    entry.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
                }

            }
            catch (Exception ex)
            {

            }
        }

        void SetEntry_TextChanged(System.Object sender, Xamarin.Forms.TextChangedEventArgs e)
        {
            try
            {

                var entry = (Entry)sender;
                const string textRegex = @"^\d+(?:[\.,]\d{0,2})?$";
                var text = e.NewTextValue.Replace(",", ".");
                bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
                if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
                {
                    entry.Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);

                }

                int set = int.Parse(entry.Text);
            }

            catch (Exception ex)
            {

            }
        }

        async void ReconfigureButton_Clicked(System.Object sender, System.EventArgs e)
        {
            //

            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
            {
                Title = "Are you sure?",
                Message = "Your program, goals, and settings may change.",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Reconfigure",
                CancelText = AppResources.Cancel,
            };
            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
            if (isConfirm)
            {
                PagesFactory.PushAsync<ReconfigureBoardingPage>(true);
            }
            else
            {
                return;
            }
        }


        void GymEquipment()
        {
            //Biceps //Chest //Abs
            Device.BeginInvokeOnMainThread(() =>
            {
                GymGradient.BackgroundColor = GetBlueGradient();
                BtnGymEquipment.TextColor = Color.White;

                BtnHomeEquipment.BackgroundColor = Color.Transparent;
                HomeGradient.BackgroundColor = GetTransparentGradient();
                BtnHomeEquipment.TextColor = Color.FromHex("#0C2432");

                BtnOther.TextColor = Color.FromHex("#0C2432");
                OtherGradient.BackgroundColor = GetTransparentGradient();
                BtnOther.BackgroundColor = Color.Transparent;

                GymEquipmentStack.IsVisible = true;
                HomeEquipmentStack.IsVisible = false;
                OtherEquipmentStack.IsVisible = false;
            });
        }
        private void GymEquipmentUnselected()
        {
            BtnGymEquipment.TextColor = Color.FromHex("#0C2432");
            GymGradient.BackgroundColor = GetTransparentGradient();

            BtnHomeEquipment.BackgroundColor = Color.Transparent;
            HomeGradient.BackgroundColor = GetTransparentGradient();
            BtnHomeEquipment.TextColor = Color.FromHex("#0C2432");

            BtnOther.TextColor = Color.FromHex("#0C2432");
            OtherGradient.BackgroundColor = GetTransparentGradient();
            BtnOther.BackgroundColor = Color.Transparent;
            GymEquipmentStack.IsVisible = false;
            HomeEquipmentStack.IsVisible = false;
            OtherEquipmentStack.IsVisible = false;
        }

        void BtnGymEquipmentClicked(System.Object sender, System.EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("HomeEquipment", "");
            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "");
            
            bool isSetted = LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true";
            LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");
            GymEquipment();
            
            if (LocalDBManager.Instance.GetDBSetting("Equipment")?.Value == "true" && !isSetted && !IsGymEnabled)
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    IsGymEnabled = true;
                    IsHomeEnabled = false;
                    IsOtherEnabled = false;
                    SetUserEquipmentSettings();
                });

            }
            else
            {
                ResetEquipmeentToggle();
            }
        }

        void HomeEquipment()
        {

            //Biceps //Chest //Abs
            Device.BeginInvokeOnMainThread(() =>
            {
                BtnGymEquipment.BackgroundColor = Color.Transparent;
                BtnGymEquipment.TextColor = Color.FromHex("#0C2432");
                GymGradient.BackgroundColor = GetTransparentGradient();

                BtnHomeEquipment.TextColor = Color.White;
                HomeGradient.BackgroundColor = GetBlueGradient();

                BtnOther.TextColor = Color.FromHex("#0C2432");
                OtherGradient.BackgroundColor = GetTransparentGradient();
                BtnOther.BackgroundColor = Color.Transparent;

                GymEquipmentStack.IsVisible = false;
                HomeEquipmentStack.IsVisible = true;
                OtherEquipmentStack.IsVisible = false;
            });
        }

        private void HomeEquipmentUnSelected()
        {
            BtnGymEquipment.BackgroundColor = Color.Transparent;
            BtnGymEquipment.TextColor = Color.FromHex("#0C2432");
            GymGradient.BackgroundColor = GetTransparentGradient();

            BtnHomeEquipment.TextColor = Color.FromHex("#0C2432");
            HomeGradient.BackgroundColor = GetTransparentGradient();

            BtnOther.TextColor = Color.FromHex("#0C2432");
            OtherGradient.BackgroundColor = GetTransparentGradient();
            BtnOther.BackgroundColor = Color.Transparent;

            GymEquipmentStack.IsVisible = false;
            HomeEquipmentStack.IsVisible = false;
            OtherEquipmentStack.IsVisible = false;
        }

        void BtnHomeEquipmentClicked(System.Object sender, System.EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("GymEquipment", "");
            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "");

            bool isSetted = LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true";
            LocalDBManager.Instance.SetDBSetting("HomeEquipment", "true");
            HomeEquipment();

            if (LocalDBManager.Instance.GetDBSetting("HomeMainEquipment")?.Value == "true" && !isSetted && !IsHomeEnabled)
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    IsGymEnabled = false;
                    IsHomeEnabled = true;
                    IsOtherEnabled = false;
                    SetUserEquipmentSettings();
                });
            }
            else
            {
                ResetEquipmeentToggle();
            }
        }

        void OtherEquipment()
        {

            //Biceps //Chest //Abs

            Device.BeginInvokeOnMainThread(() =>
            {
                BtnGymEquipment.BackgroundColor = Color.Transparent;
                BtnGymEquipment.TextColor = Color.FromHex("#0C2432");
                GymGradient.BackgroundColor = GetTransparentGradient();

                BtnHomeEquipment.TextColor = Color.FromHex("#0C2432");
                HomeGradient.BackgroundColor = GetTransparentGradient();

                BtnOther.TextColor = Color.White; ;
                OtherGradient.BackgroundColor = GetBlueGradient();

                GymEquipmentStack.IsVisible = false;
                HomeEquipmentStack.IsVisible = false;
                OtherEquipmentStack.IsVisible = true;
            });
        }
        private void OtherEquipmentUnselected()
        {

            BtnGymEquipment.BackgroundColor = Color.Transparent;
            BtnGymEquipment.TextColor = Color.FromHex("#0C2432");
            GymGradient.BackgroundColor = GetTransparentGradient();

            BtnHomeEquipment.TextColor = Color.FromHex("#0C2432");
            HomeGradient.BackgroundColor = GetTransparentGradient();

            BtnOther.TextColor = Color.FromHex("#0C2432");
            OtherGradient.BackgroundColor = GetTransparentGradient();

            GymEquipmentStack.IsVisible = false;
            HomeEquipmentStack.IsVisible = false;
            OtherEquipmentStack.IsVisible = false;
        }

        void BtnOtherEquipmentClicked(System.Object sender, System.EventArgs e)
        {

            LocalDBManager.Instance.SetDBSetting("GymEquipment", "");
            LocalDBManager.Instance.SetDBSetting("HomeEquipment", "");
            
            bool isSetted = LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true";
            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "true");
            OtherEquipment();

            //}
            if (LocalDBManager.Instance.GetDBSetting("OtherMainEquipment")?.Value == "true" && !isSetted && !IsOtherEnabled)
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    IsGymEnabled = false;
                    IsHomeEnabled = false;
                    IsOtherEnabled = true;
                    SetUserEquipmentSettings();
                });
            }
            else
            {
                ResetEquipmeentToggle();
            }
        }
        private void ResetEquipmeentToggle()
        {
            try
            {

            
            var model = new EquipmentModel()
            {
                IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                IsHomeEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("HomeMainEquipment").Value == "true",
                IsOtherEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("OtherMainEquipment").Value == "true",

            };
            if (LocalDBManager.Instance.GetDBSetting("GymEquipment")?.Value == "true" && model.IsEquipmentEnabled)
                model.Active = "gym";
            if (LocalDBManager.Instance.GetDBSetting("HomeEquipment")?.Value == "true" && model.IsHomeEquipmentEnabled)
                model.Active = "home";
            if (LocalDBManager.Instance.GetDBSetting("OtherEquipment")?.Value == "true" && model.IsOtherEquipmentEnabled)
                model.Active = "other";
            if (string.IsNullOrEmpty(model.Active))
            {
                var hmm = "";
                if (IsOtherEnabled && model.IsOtherEquipmentEnabled)
                    hmm = "other";
                if (IsHomeEnabled && model.IsHomeEquipmentEnabled)
                    hmm = "home";
                if (IsGymEnabled && model.IsEquipmentEnabled)
                    hmm = "gym";
                model.Active = hmm;

                if (string.IsNullOrEmpty(model.Active))
                {
                    if (model.IsOtherEquipmentEnabled)
                        hmm = "other";
                    if (model.IsHomeEquipmentEnabled)
                        hmm = "home";
                    if (model.IsEquipmentEnabled)
                        hmm = "gym";
                }
                model.Active = hmm;
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", hmm == "other" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", hmm == "home" ? "true" : "");
                LocalDBManager.Instance.SetDBSetting("GymEquipment", hmm == "gym" ? "true" : "");
            }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
             
            }
        }

        void ViewMoreStats_Clicked(System.Object sender, System.EventArgs e)
        {
            Device.OpenUri(new Uri("https://dashboard.dr-muscle.com/"));
        }

        void PickerBodyPart_Unfocused(System.Object sender, Xamarin.Forms.FocusEventArgs e)
        {            
            SetupBodyPartPriority(LocalDBManager.Instance.GetDBSetting("BodypartPriority").Value);
        }

        void LearnMoreWButtonClicked(System.Object sender, System.EventArgs e)
        {
            if (CheckTrialUser())
                return;

            ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];

            PagesFactory.PopToRootAsync();
        }

        private bool CheckTrialUser()
        {
            if (App.IsFreePlan)
            {
                ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                {
                    Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
                    Title = "You discovered a premium feature!",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Upgrade",
                    CancelText = "Maybe later",
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            PagesFactory.PushAsync<SubscriptionPage>();
                        }
                        else
                        {

                        }
                    }
                };
                UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
            return App.IsFreePlan;
        }

        void SetStylePicker_Unfocused(System.Object sender, Xamarin.Forms.FocusEventArgs e)
        {
            
        }

        async void ResetButton_Clicked(System.Object sender, System.EventArgs e)
        {
            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
            {
                Title = "Are you sure?",
                Message = "Reset history for all exercises? This cannot be undone.",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Reset all",
                CancelText = AppResources.Cancel,
            };
            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
            if (isConfirm)
            {
                await DrMuscleRestClient.Instance.ResetAllExercise();
            }
            else
            {
                return;
            }
        }

        void ChangeEmailButton_Clicked(System.Object sender, System.EventArgs e)
        {
            GetEmail();
        }
        private void GetEmail()
        {
            PromptConfig p = new PromptConfig()
            {
                InputType = InputType.Email,
                IsCancellable = true,
                Title = "Update account email",
                Placeholder = "Enter your email",
                OkText = "Update email",
                CancelText = "Cancel",
                Text = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = new Action<PromptResult>(GetEmailAction)
            };

            UserDialogs.Instance.Prompt(p);
        }
        private async void GetEmailAction(PromptResult response)
        {

            if (!CrossConnectivity.Current.IsConnected)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = AppResources.PleaseCheckInternetConnection,
                    Title = AppResources.ConnectionError,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });

                GetEmailAction(response);
                return;
            }
            if (response.Ok)
            {
                if (!Emails.ValidateEmail(response.Text))
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        Message = AppResources.InvalidEmailError,
                        Title = AppResources.InvalidEmailAddress,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    });
                    GetEmail();
                    return;
                }
                var email = LocalDBManager.Instance.GetDBSetting("email")?.Value.ToLower();
                if (response.Text.ToLower() == email)
                    return;
                BooleanModel existingUser = await DrMuscleRestClient.Instance.UpdateEmail(new IsEmailAlreadyExistModel() { email = response.Text });
                if (existingUser != null)
                {
                    if (!existingUser.Result)
                    {

                        if (existingUser.api?.ErrorMessage == "Failed to update")
                        {
                            AlertConfig ShowAlertPopUp1 = new AlertConfig()
                            {
                                Title = "Failed to update",
                                Message = $"You are using {email} email in your subscription, to changing this email causes it to remove your subscription",
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = "OK",


                            };
                            await UserDialogs.Instance.AlertAsync(ShowAlertPopUp1);

                            return;
                        }
                        AlertConfig ShowAlertPopUp = new AlertConfig()
                        {
                            Title = "Error",
                            Message = existingUser.api.ErrorMessage,
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = "Try again",


                        };
                        await UserDialogs.Instance.AlertAsync(ShowAlertPopUp);

                        return;
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("email", response.Text.ToLower());
                    }

                }
                else
                    GetEmailAction(response);


            }
        }

        async void hoursBeforeEntry_Unfocused(System.Object sender, Xamarin.Forms.FocusEventArgs e)
        {
            try
            {
               
            }
            catch (Exception)
            {

            }

        }

        async void UpdateReminderHours()
        {
            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            var index = 0;

            try
            {
                if (workouts.Sets != null)
                {

                }
                else
                {

                    workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
                }
                if (workouts.GetUserProgramInfoResponseModel != null)
                {

                }
            }
            catch (Exception ex)
            {

                workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            }

            var summaryRestModel = new BotModel()
            {
                Type = BotType.SummaryRest,
            };

            var RequiredHours = 18;
            int hours = 0;
            int minutes = 0;

            if (workouts != null && workouts.LastWorkoutDate != null)
            {
                bool IsInserted = false;

                TimeSpan timeSpan;
                String dayStr = "days";
                int days = 0;
                hours = 0;
                minutes = 0;

                if (workouts.LastWorkoutDate != null)
                {

                    days = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays;
                    hours = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalHours;
                    minutes = (int)(DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalMinutes;
                    if (days > 0)
                        dayStr = days == 1 ? "day" : "days";
                    else if (hours > 0 && hours < 72)
                        dayStr = hours <= 1 ? "hour" : "hours";
                    else if (minutes < 60)
                        dayStr = minutes <= 1 ? "minute" : "minutes";

                    var d = 0;
                    if (days > 0)
                        d = days;
                    else
                    {
                        d = timeSpan.Days;

                        if (days > 0)
                            dayStr = d == 1 ? "day" : "days";
                        else if (hours > 0 && hours < 72)
                            dayStr = hours <= 1 ? "hour" : "hours";
                        else if (minutes < 60)
                            dayStr = minutes <= 1 ? "minute" : "minutes";


                    }
                }



                if (workouts.LastWorkoutDate != null)
                {
                    RequiredHours = 18;
                    if (workouts != null && workouts.GetUserProgramInfoResponseModel != null && workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null && workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null && workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate.IsSystemExercise)
                    {
                        if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bodyweight") ||
workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("mobility") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("powerlifting") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("full-body") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bands"))
                        {

                            RequiredHours = 42;
                            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                            {
                                if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) < 30)
                                    RequiredHours = 18;
                            }
                            if (workouts.LastConsecutiveWorkoutDays > 1 && workouts.LastWorkoutDate != null && (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays < 2)
                                RequiredHours = 42;
                        }

                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[home] push") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[home] pull") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[home] legs") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[gym] push") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[gym] pull") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("[gym] legs"))
                        {
                            RequiredHours = 18;
                            if (workouts.LastConsecutiveWorkoutDays > 5 && workouts.LastWorkoutDate != null && (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays < 3)

                            {
                                RequiredHours = 42;
                            }
                        }
                        else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("split"))
                        {
                            RequiredHours = 18;
                            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                            {
                                if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) > 50)
                                    RequiredHours = 42;
                            }
                            if (workouts.LastConsecutiveWorkoutDays > 1 && workouts.LastWorkoutDate != null && (DateTime.Now - ((DateTime)workouts.LastWorkoutDate).ToLocalTime()).TotalDays < 2)
                                RequiredHours = 42;
                        }
                    }

                    if (days > 0 && hours >= RequiredHours)
                    {
                        summaryRestModel.SinceTime = $"{days} {dayStr}";
                        summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                        summaryRestModel.TrainRest = "Train";
                        summaryRestModel.StrengthTextColor = AppThemeConstants.GreenColor;
                        summaryRestModel.TrainRestText = "Coach says";// "Recovered";// (days > 9 ? "I may recommend lighter weights" : "You should have recovered").ToLower().FirstCharToUpper();
                        IsInserted = true;
                    }
                    else if (hours > 0)
                    {

                        if (hours < RequiredHours)
                        {
                            if (LocalDBManager.Instance.GetDBSetting("RecommendedReminder")?.Value == "true")
                            {
                                DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification("Workout time!", "Ready to crush your workout? You got this!", TimeSpan.FromHours(RequiredHours - hours) + DateTime.Now.TimeOfDay, 1111, NotificationInterval.Week);
                            }
                            else
                                DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1111);
                            summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;
                            var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                            
                            summaryRestModel.TrainRest = "Rest";
                            summaryRestModel.SinceTime = $"{hours}/{RequiredHours} {h}";
                            if (LocalDBManager.Instance.GetDBSetting($"WorkoutAdded{DateTime.Now.Date.AddDays(1)}")?.Value == "true")
                            {
                                summaryRestModel.SinceTime = "18 hours";
                            }
                            summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                            summaryRestModel.TrainRestText = "Coach says";// "Fatigued";// $"At least {RequiredHours - hours} {h} more to recover".ToLower().FirstCharToUpper();

                            IsInserted = true;
                        }
                        else
                        {
                            summaryRestModel.SinceTime = $"{hours} {dayStr}";
                            summaryRestModel.SinceTime = $"{hours}/{RequiredHours} hours";
                            if (LocalDBManager.Instance.GetDBSetting($"WorkoutAdded{DateTime.Now.Date.AddDays(1)}")?.Value == "true")
                            {
                                summaryRestModel.SinceTime = "18 hours";
                            }
                            summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                            summaryRestModel.TrainRest = "Train";
                            summaryRestModel.StrengthTextColor = AppThemeConstants.GreenColor;
                            summaryRestModel.TrainRestText = "Coach says";// "Recovered"; //"You should have recovered".ToLower().FirstCharToUpper();
                            IsInserted = true;
                        }

                    }
                    else
                    {
                        var h = RequiredHours - hours <= 1 ? "hour" : "hours";
                        summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;
                        summaryRestModel.TrainRest = "Rest";
                        summaryRestModel.SinceTime = $"{RequiredHours} {h}";
                        summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                        summaryRestModel.TrainRestText = "Coach says";
                        if (LocalDBManager.Instance.GetDBSetting($"WorkoutAdded{DateTime.Now.Date.AddDays(1)}")?.Value == "true")
                        {
                            summaryRestModel.SinceTime = "18 hours";
                        }

                        IsInserted = true;
                    }
                }

                if (!IsInserted)
                {
                    summaryRestModel.SinceTime = $"N/A";
                    summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                    summaryRestModel.TrainRest = "Train";


                }

            }
            else
            {
                RequiredHours = 18;
                if (workouts != null && workouts.GetUserProgramInfoResponseModel != null && workouts.GetUserProgramInfoResponseModel.RecommendedProgram != null && workouts.GetUserProgramInfoResponseModel.NextWorkoutTemplate != null && workouts.GetUserProgramInfoResponseModel.RecommendedProgram.IsSystemExercise)
                {
                    if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bodyweight") ||
workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("mobility") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("powerlifting") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("full-body") || workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("bands"))
                    {
                        RequiredHours = 42;
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                        {
                            if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) < 30)
                                RequiredHours = 18;
                        }
                    }
                    else if (workouts.GetUserProgramInfoResponseModel.RecommendedProgram.Label.ToLower().Contains("split"))
                    {
                        RequiredHours = 18;
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("Age")?.Value))
                        {
                            if (int.Parse(LocalDBManager.Instance.GetDBSetting("Age")?.Value) > 50)
                                RequiredHours = 42;
                        }
                    }
                }
                summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;

                summaryRestModel.StrengthTextColor = AppThemeConstants.DarkRedColor;
                summaryRestModel.TrainRest = "Rest";
                summaryRestModel.SinceTime = $"{RequiredHours} hours";
                summaryRestModel.LastWorkoutText = $"recovery".ToLower().FirstCharToUpper();
                summaryRestModel.TrainRestText = "Coach says";


            }
            if (summaryRestModel.TrainRest == "Rest")
            {
                if (LocalDBManager.Instance.GetDBSetting("IsEmailReminder") == null)
                    LocalDBManager.Instance.SetDBSetting("IsEmailReminder", "true");
                if (RequiredHours - hours > 0)
                {
                    var beforeHour = 0;
                    try
                    {
                        if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("ReminderHours")?.Value))
                            beforeHour = int.Parse(LocalDBManager.Instance.GetDBSetting("ReminderHours")?.Value);
                    }
                    catch (Exception ex)
                    {

                    }

                    var date = DateTime.Now;
                    if (workouts.LastWorkoutDate != null)
                    {
                        date = ((DateTime)workouts.LastWorkoutDate).ToLocalTime();
                    }
                    DrMuscleRestClient.Instance.SetUserEmailReminderTime(new UserModel()
                    {
                        LastActiveDate = date.AddHours(RequiredHours - hours - beforeHour)
                    });

                    if (LocalDBManager.Instance.GetDBSetting("IsEmailReminder")?.Value == "true")
                    {
                        try
                        {

                            var dt1 = DateTime.Now.AddHours(RequiredHours - hours - beforeHour);
                            var timeSpan = dt1 - DateTime.Now;

                            var workoutName = $"{workouts?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Label}";
                            var weekStreak = "";
                            if (workouts.ConsecutiveWeeks != null && workouts.ConsecutiveWeeks.Count > 0)
                            {
                                var lastTime = workouts.ConsecutiveWeeks.Last();
                                var year = Convert.ToString(lastTime.MaxWeek).Substring(0, 4);
                                var weekOfYear = Convert.ToString(lastTime.MaxWeek).Substring(4, 2);
                                CultureInfo myCI = new CultureInfo("en-US");
                                Calendar cal = myCI.Calendar;

                                if (int.Parse(year) == DateTime.Now.Year)
                                {
                                    var currentWeekOfYear = cal.GetWeekOfYear(DateTime.Now, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
                                    if (int.Parse(weekOfYear) >= currentWeekOfYear)
                                    {
                                        if (lastTime.ConsecutiveWeeks > 0)
                                        {
                                            weekStreak = $"—{lastTime.ConsecutiveWeeks}-week streak!";
                                        }
                                    }

                                    else if (int.Parse(weekOfYear) == currentWeekOfYear - 1)
                                    {
                                        if (lastTime.ConsecutiveWeeks > 0)
                                        {
                                            weekStreak = $"—{lastTime.ConsecutiveWeeks}-week streak!";
                                        }
                                    }
                                }
                            }
                            timeSpan = TimeSpan.FromHours(RequiredHours - hours - beforeHour) + DateTime.Now.TimeOfDay;
                            DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification($"Workout in {beforeHour} {(beforeHour < 2 ? "hour" : "hours")}", $"{workoutName}{weekStreak}", timeSpan, 1122, NotificationInterval.Week);

                        }
                        catch (Exception ex)
                        {

                        }
                    }
                    else
                    {
                        DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1122);
                    }
                }


            }
        }

        async void DeleteButton_Clicked(System.Object sender, System.EventArgs e)
        {

            ConfirmConfig ShowOffPopUp = new ConfirmConfig()
            {
                Title = "Are you sure?",
                Message = "Delete everything? This cannot be undone.",
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Delete",
                CancelText = AppResources.Cancel,
            };
            var isConfirm = await UserDialogs.Instance.ConfirmAsync(ShowOffPopUp);
            if (isConfirm)
            {
                var response = await DrMuscleRestClient.Instance.DeleteAccount();
                if (response != null)
                {
                    AlertConfig alert = new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Message = "Account purged.",
                        Title = "Deletion successful",
                        OkText = "Continue"
                    };
                    await UserDialogs.Instance.AlertAsync(alert);

                    if (App.IsMealPlan || App.IsV1User)
                    {
                        ConfirmConfig alert2 = new ConfirmConfig()
                        {
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            Message = "Subscription still active. Email us to cancel?",
                            Title = "In-app records deleted",
                            OkText = "Email",
                            CancelText = "Skip"
                        };
                        var res = await UserDialogs.Instance.ConfirmAsync(alert2);
                        if (res)
                        {
                            await HelperClass.SendMail("Subscription question");
                        }
                    }
                    Logout();
                }
            }
            else
            {
                return;
            }
        }

        private async void Logout()
        {
            CancelNotification();
            LocalDBManager.Instance.Reset();
            CurrentLog.Instance.Reset();
            App.IsV1User = false;
            App.IsV1UserTrial = false;
            App.IsCongratulated = false;
            App.IsSupersetPopup = false;
            App.IsFreePlan = false;

            ((App)Application.Current).UserWorkoutContexts.workouts = new GetUserWorkoutLogAverageResponse();

            ((App)Application.Current).UserWorkoutContexts.SaveContexts();
            ((App)Application.Current).WorkoutHistoryContextList.Histories = new List<HistoryModel>();
            ((App)Application.Current).WorkoutHistoryContextList.SaveContexts();
            ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
            ((App)Application.Current).WorkoutLogContext.SaveContexts();

            ((App)Application.Current).WeightsContextList.Weights = new List<UserWeight>();
            ((App)Application.Current).WeightsContextList.SaveContexts();

            try
            {
                if (((global::DrMuscle.MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage.Navigation.NavigationStack[0] is LearnPage)
                    ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).SelectedItem = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[0];
            }
            catch (Exception ex)
            {

            }
            await PagesFactory.PopToRootAsync();
            ((App)Application.Current).displayCreateNewAccount = true;

            PagesFactory.PopThenPushAsync<MainOnboardingPage>(true);

           
        }
        private void CancelNotification()
        {
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1251);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1351);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1451);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1551);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1651);
        }
    }


}


