﻿<?xml version="1.0" encoding="UTF-8" ?>
<t:DrMusclePage
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:t="clr-namespace:DrMuscle.Layout"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
                    xmlns:app="clr-namespace:DrMuscle.Constants"

    x:Class="DrMuscle.Screens.User.FAQPage">
    <ContentPage.Content>
        <ScrollView>
            
            <StackLayout
                Padding="20,20,10,20"
                Spacing="1"
                BackgroundColor="Transparent">
                
                <Label
                    Text="Are you different from other workout apps?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    Style="{StaticResource NormalLabelStyle1}"
                RawText="Yes! You get a smart, custom program. Your smart program is optimized in real time. Every time you complete a set, it updates to match your progress, and speed up future progress. So, you get in shape faster. It's like a trainer in your phone. Our founder Dr. <PERSON> has a PhD in exercise statistics. He keeps the app on the cutting edge. [<PERSON>rn more](https://dr-muscle.com)" />

                <Label
                    Margin="0,15,0,0"
                    Text="Do you automate my workouts?"
                    Style="{StaticResource BoldLabelStyle1}" />
                <Label Text="Yes! We automate all the following:"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="1. [Progressive overload](https://dr-muscle.com/building-muscle)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="2. [Daily undulating periodization](https://dr-muscle.com/build-muscle-faster/#1)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="3. [Rest times](https://dr-muscle.com/between-sets)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="4. [Deloads](https://dr-muscle.com/deload)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="5. [Reps and sets](https://dr-muscle.com/building-muscle)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="6. [Exercise selection](https://dr-muscle.com/change-routine-exercises/)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="7. Weight selection"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="8. [Rest-pause sets](https://dr-muscle.com/rest-pause)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="9. Straight sets"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="10. Back-off sets (new)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="11. [Tracking weekly sets and strength](https://dashboard.dr-muscle.com/)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="12. [Light session (when you return from a break)](https://dr-muscle.com/overtraining)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="13. Volume boost (when you skip a workout)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="14. Challenge time! (when you progress fast)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="15. Suggested exercises (when you swap one you don't like)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="16. Custom exercises and workouts"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="17. [Upload / import your program](https://my.dr-muscle.com/)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="18. Normal, bodyweight, time-based, and unilateral exercises"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="19. Plate calculator"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="20. Support available inside app via chat"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="21. [Overtraining protection](https://dr-muscle.com/easy/)"
                       Style="{StaticResource NormalLabelStyle1}" />
                <controls:HyperlinkLabel RawText="22. [Specialization programs](https://dr-muscle.com/big-arms/)"
                       Style="{StaticResource NormalLabelStyle1}" />

                <Label
                    Margin="0,15,0,0"
                    Text="Can I add cardio to my program?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    Style="{StaticResource NormalLabelStyle1}"
                RawText="Yes! Just remember it can slow down your gains. Tap here to [Learn more](http://dr-muscle.com/cardio)." />


                <Label
                    Margin="0,15,0,0"
                    Text="Can I view my workout history on the Web?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    RawText="Yes! You can analyze your full workout history at [my.dr-muscle.com](https://my.dr-muscle.com)."
                    Style="{StaticResource NormalLabelStyle1}" />

                <Label
                    Margin="0,15,0,0"
                    Text="Can I reset my exercises?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <Label
                    Text="Yes! To reset, tap &quot;…&quot; and &quot;more&quot; next to any exercise. Scroll down, and tap &quot;Reset history&quot;."
                    Style="{StaticResource NormalLabelStyle1}" />

                <Label
                    Margin="0,15,0,0"
                    Text="I only have access to plates of 2.5 lbs. Can I customize my increments?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <Label
                    Text="Yes! Just go to settings, scroll down to increments, and enter 2.5 lbs."
                    Style="{StaticResource NormalLabelStyle1}" />

                <Label
                    Margin="0,15,0,0"
                    Text="Can I create custom exercises?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <Label
                    Text="Yes! Just tap Exercises (bottom navigation) > Custom > Add my own."
                    Style="{StaticResource NormalLabelStyle1}" />

                <Label
                    Margin="0,15,0,0"
                    Text="Can I create custom workouts?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <Label
                    Text="Yes! Just tap Workouts (bottom navigation) > Custom > Add my own."
                    Style="{StaticResource NormalLabelStyle1}" />

                <Label
                    Margin="0,15,0,0"
                    Text="Can I upload my own program?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    RawText="Yes! Visit [my.dr-muscle.com](https://my.dr-muscle.com) for instructions."
                    Style="{StaticResource NormalLabelStyle1}" />

              
                <Label
                    Margin="0,15,0,0"
                    Text="I set my reps to 8-12. Why do I get 6?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <Label
                    Text="That's usually because of custom increments. If you set custom increments, the app will sometimes lower your reps to match your nearest increment."
                    Style="{StaticResource NormalLabelStyle1}" />

              
                <Label
                    Margin="0,15,0,0"
                    Text="Do you offer meal plans?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    RawText="Yes! [Email us for details](mailto:<EMAIL>?subject=I%20would%20like%20a%20meal%20plan)."
                    Style="{StaticResource NormalLabelStyle1}" >
                    <!--<Label.GestureRecognizers>
                        <TapGestureRecognizer Tapped="MealPlan_Tapped" />
                    </Label.GestureRecognizers>-->
                    </controls:HyperlinkLabel>


                <Label
                    Margin="0,15,0,0"
                    Text="Where can I view the latest updates?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    RawText="Check them out at [dr-muscle.com/timeline/](https://dr-muscle.com/timeline/)"
                    Style="{StaticResource NormalLabelStyle1}" />

                
                <Label
                    Margin="0,15,0,0"
                    Text="Can I chat with support?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <Label
                    Text="Yes! Tap here to chat 1-on-1 with support. We usually reply in one business day."
                    Style="{StaticResource NormalLabelStyle1}" >
                    <Label.FormattedText>
                        <FormattedString>
                            <Span
                                Text="Yes! " />
                            <Span
                                Text="Tap here to chat 1-on-1 with support" TextColor="{x:Static app:AppThemeConstants.BlueLightColor}" >
                                <Span.TextDecorations>
            <OnPlatform x:TypeArguments="TextDecorations" Android="Underline" iOS="Underline" />
                                </Span.TextDecorations>
                                </Span>
                            <Span
                                Text=". We usually reply in one business day." />
                        </FormattedString>
                    </Label.FormattedText>
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer Tapped="OneonOnePlan_Tapped" />
                    </Label.GestureRecognizers>
                    </Label>

                <Label
                    Margin="0,15,0,0"
                    Text="Can I email support?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    RawText="Yes! [Tap here to email support](mailto:<EMAIL>?subject=Question%20about%20Dr.%20Muscle). We usually reply in one business day."
                    Style="{StaticResource NormalLabelStyle1}" >
                    </controls:HyperlinkLabel>
<Label
                    Margin="0,15,0,0"
                    Text="Are more features coming?"
                    Style="{StaticResource BoldLabelStyle1}" />

                <controls:HyperlinkLabel
                    RawText="Yes! [View all updates](https://dr-muscle.com/timeline) or [email us your suggestions](mailto:<EMAIL>?subject=suggestion%20to%20improve). We release new features every week."
                    Style="{StaticResource NormalLabelStyle1}" >
                    </controls:HyperlinkLabel>

            </StackLayout>

        </ScrollView>
    </ContentPage.Content>
</t:DrMusclePage>
