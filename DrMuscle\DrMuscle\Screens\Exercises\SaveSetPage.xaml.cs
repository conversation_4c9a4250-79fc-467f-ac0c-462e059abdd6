using DrMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using Xamarin.Forms;
using Acr.UserDialogs;
using System.Diagnostics;
using DrMuscle.Screens.Exercises;
using DrMuscle.Layout;
using System.Globalization;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using DrMuscle.Screens.Workouts;
using DrMuscle.Helpers;
using System.Threading.Tasks;
using DrMuscle.Resx;
using DrMuscle.Constants;
using Microsoft.AppCenter.Crashes;
using Rg.Plugins.Popup.Services;
using DrMuscle.Views;

namespace DrMuscle.Screens.Exercises
{
    public partial class SaveSetPage : DrMusclePage
    {
        private ObservableCollection<WorkoutLogSerieModelEx> workoutLogSerieModel;
        private ObservableCollection<WorkoutLogSerieModelEx> Side1SetworkoutLogSerieModel;
        private decimal currentWeight = 0;
        private int currentReps = 0;
        private int? RIR = 0;
        private decimal weightStep = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? (decimal)1 : (decimal)2.5;
        TimerPopup popup;
        bool isFirstSide = false;
        bool isSecondSide = false;
        public SaveSetPage()
        {
            InitializeComponent();
            RefreshLocalized();
            WeightLess.Clicked += WeightLess_Clicked;
            WeightMore.Clicked += WeightMore_Clicked;
            RepsLess.Clicked += RepsLess_Clicked;
            RepsMore.Clicked += RepsMore_Clicked;
            WeightEntry.TextChanged += WeightEntry_TextChanged;
            RepsEntry.TextChanged += RepsEntry_TextChanged;
            FinishedButton.Clicked += Finished_Clicked;
            WeightEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
            var timerToolbarItem = new ToolbarItem(Timer.Instance.State == "STOPPED" ? "" : Timer.Instance.Remaining.ToString(), Timer.Instance.State == "STOPPED" ? "stopwatch.png" : "", SlideTimerAction, ToolbarItemOrder.Primary, 0);
            this.ToolbarItems.Add(timerToolbarItem);
            var plateToolbarItem = new ToolbarItem("", "plate.png", ShowPlateCalculater, ToolbarItemOrder.Primary, 0);
            this.ToolbarItems.Insert(0, plateToolbarItem);
            SupersetButton.Clicked += async (sender, e) =>
            {
                if (Config.Superset_warning_shown == false)
                {
                    ConfirmConfig supersetConfig = new ConfirmConfig()
                    {
                        Message = AppResources.ASuperSetIsWhenYouAlternateSetsOfDifferentExercisesYourSets,
                        Title = AppResources.WhatIsASuperset,
                        //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = AppResources.Ok,
                        CancelText = AppResources.RemindMe,
                        OnAction = async (bool ok) =>
                        {
                            if (ok)
                            {
                                Config.Superset_warning_shown = true;
                                //LocalDBManager.Instance.SetDBSetting("superset_warning_shown", "true");
                            }
                            else
                            {
                                Config.Superset_warning_shown = false;
                                //LocalDBManager.Instance.SetDBSetting("superset_warning_shown", "false");
                            }
                            Timer.Instance.Remaining = CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, true, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets);

                            if (CurrentLog.Instance.WorkoutStarted)
                                await PagesFactory.PushAsync<ChooseYourWorkoutExercisePage>();
                            else
                                await PagesFactory.PushAsync<ChooseDrMuscleOrCustomExercisePage>();
                        }
                    };
                    UserDialogs.Instance.Confirm(supersetConfig);
                    return;
                }
                Timer.Instance.Remaining = CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, true, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets);
                if (CurrentLog.Instance.WorkoutStarted)
                    await PagesFactory.PushAsync<ChooseYourWorkoutExercisePage>();
                else
                    await PagesFactory.PushAsync<ChooseDrMuscleOrCustomExercisePage>();
            };
            Timer.Instance.OnTimerDone += OnTimerDone;
             
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });
        }

        private void RefreshLocalized()
        {
            try
            {

            //DoLabel.Text = AppResources.DoThisNow;
            LblWeight.Text = AppResources.Weight + ":";
                var isTimeBased = CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased;
                
            LblReps.Text = isTimeBased ? "Secs" + ":" : AppResources.Reps + ":";
            SaveSet.Text = AppResources.Saveset;
                SupersetButton.Text = "SS"; //AppResources.Superset;
            FinishedButton.Text = AppResources.FinishExercise;

            }
            catch (Exception ex)
            {

            }

        }

        public override void OnBeforeShow()
        {
            base.OnBeforeShow();
            try
            {
				var isTimeBased = CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased;
                Device.BeginInvokeOnMainThread(() =>
                {
                    FinishedButton.Text = AppResources.FinishExercise;
                    LblReps.Text = isTimeBased ? "Secs" + ":" : AppResources.Reps + ":";
                });
				isFirstSide = false;
                if (LocalDBManager.Instance.GetDBSetting("PlatesKg") == null || LocalDBManager.Instance.GetDBSetting("PlatesLb") == null)
                {
                    var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                    LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);

                    var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                    LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
                }
                if (Config.ShowPlateCalculatorPopup < 2 && App.IsPlatePopup == false)
                    Config.ShowPlateCalculatorPopup += 1;

                if (CurrentLog.Instance.WorkoutLogSeriesByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                {
                    workoutLogSerieModel = CurrentLog.Instance.WorkoutLogSeriesByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id];
                    if (workoutLogSerieModel.Count == 3)
                        LocalDBManager.Instance.SetDBSetting("ComputeRpReps", "true");
                }
                else
                {
                    workoutLogSerieModel = new ObservableCollection<WorkoutLogSerieModelEx>();
                    CurrentLog.Instance.WorkoutLogSeriesByExercise.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, workoutLogSerieModel);
                }
                Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;

                SetListView.ItemsSource = workoutLogSerieModel;

                currentWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                              RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Weight.Kg, 1, null, null) :
                                              RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Weight.Lb, (decimal)2.5, null, null);
                currentReps = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Reps;


                WeightEntry.Text = string.Format("{0:0.00}", currentWeight).ReplaceWithDot();

                RepsEntry.Text = string.Format("{0}", currentReps).ReplaceWithDot();

                string restTime = new TimeSpan(0, 0, CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, false, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets)).ToString(@"mm\:ss");

                if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null || CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Increments != null)
                {
                    if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Increments != null)
                    {
                        var unit = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Increments;
                        weightStep = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? unit.Kg : unit.Lb;
                    }
                    else
                    {
                        var increment = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("workout_increments").Value.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
                        var unit = new MultiUnityWeight(increment, "kg");
                        weightStep = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? unit.Kg : unit.Lb;
                    }
                }
                else
                    weightStep = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? (decimal)1 : (decimal)2.5;
                // Affichage de la prochaine série à faire dans le haut de l'écran

            }
            catch (Exception ex)
            {
                var properties = new Dictionary<string, string>
                    {
                        { "SaveSet_OnBeforeShow", $"{ex.StackTrace}" }
                    };
                Crashes.TrackError(ex, properties);
            }
            ResetButtons();
            UpdateTopLabels();
            try
            {
                
            Task.Factory.StartNew(async () => {
                await Task.Delay(100);
                if (CurrentLog.Instance.ExerciseLog.Exercice.IsUnilateral && workoutLogSerieModel.Count == 0)
                {
                    if (isSecondSide)
                    {

                    }
                    else
                    {
                        isFirstSide = true;
                        Device.BeginInvokeOnMainThread(() => { 
                        AlertConfig ShowExplainRIRPopUp = new AlertConfig()
                        {
                            Title = "Do all sets for side 1 now",
                            //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = AppResources.Ok,

                        };
                        FinishedButton.Text = "Finish side 1";
                        UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
                        });
                    }
                }
                else
                    FinishedButton.Text = AppResources.FinishExercise;
            });

            }
            catch (Exception ex)
            {

            }
        }

        async void ButtonSkip_Clicked(object sender, System.EventArgs e)
        {
            NavigationPage.SetHasNavigationBar(this, true);

            await Timer.Instance.StopTimer();
            if (ToolbarItems.Count > 0)
            {
                this.ToolbarItems.RemoveAt(0);
                timerToolbarItem = new ToolbarItem("", "stopwatch.png", SlideTimerAction, ToolbarItemOrder.Primary, 0);
                this.ToolbarItems.Insert(0, timerToolbarItem);
            }
        }

        private async void UpdateTopLabels()
        {
            // Affichage de la ligne "Do" en haut de l'écran et des pickers

            WeightEntry.TextChanged -= WeightEntry_TextChanged;
            decimal wuWeight1 = 0; //LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                   //RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpWeightSet1.Kg, 1) :
                                   //RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpWeightSet1.Lb, (decimal)2.5);
            int wuReps1 = 0; //CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpReps1;

            decimal wuWeight2 = 0; //LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                   // RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpWeightSet2.Kg, 1) :
                                   // RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpWeightSet2.Lb, (decimal)2.5);
            int wuReps2 = 0; //CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpReps2;

            WeightEntry.Text = string.Format("{0:0.00}", currentWeight).ReplaceWithDot();

            RepsEntry.Text = string.Format("{0}", currentReps).ReplaceWithDot();

            string restTime = new TimeSpan(0, 0, CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, false, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets)).ToString(@"mm\:ss");
            var reco = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id];
            if (reco.WarmUpsList.Count > 0)
            {
                if (workoutLogSerieModel.Count < reco.WarmUpsList.Count)
                {
                    wuWeight1 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                    RoundDownToNearestIncrement(reco.WarmUpsList[workoutLogSerieModel.Count].WarmUpWeightSet.Kg, 1, null, null) :
                    RoundDownToNearestIncrement(reco.WarmUpsList[workoutLogSerieModel.Count].WarmUpWeightSet.Lb, (decimal)2.5, null, null);
                    wuReps1 = reco.WarmUpsList[workoutLogSerieModel.Count].WarmUpReps;
                    currentWeight = wuWeight1;
                    currentReps = wuReps1;
                    WeightEntry.Text = string.Format("{0:0.00}", currentWeight).ReplaceWithDot();
                    RepsEntry.Text = string.Format("{0}", currentReps).ReplaceWithDot();
                    RIR = null;
                    //Label1
                    DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", ((reco.WarmUpsList.Count - workoutLogSerieModel.Count) + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses), AppResources.SetsLeftLift, wuWeight1, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", wuReps1, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                    //Label2
                    decimal nextWeight = 0;
                    int nextReps = 0;
                    if (reco.WarmUpsList.Count > workoutLogSerieModel.Count + 1)
                    {
                        nextWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                RoundDownToNearestIncrement(reco.WarmUpsList[workoutLogSerieModel.Count + 1].WarmUpWeightSet.Kg, 1, null, null) :
                                RoundDownToNearestIncrement(reco.WarmUpsList[workoutLogSerieModel.Count + 1].WarmUpWeightSet.Lb, (decimal)2.5, null, null);
                        nextReps = reco.WarmUpsList[workoutLogSerieModel.Count + 1].WarmUpReps;
                    }
                    else
                    {
                        nextWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                      RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Weight.Kg, 1, null, null) :
                                      RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Weight.Lb, (decimal)2.5, null, null);
                        nextReps = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Reps;
                    }
                    if (popup != null)
                        popup?.SetTimerRepsSets(string.Format("{0:0.00} x {1} ", currentWeight, currentReps).ReplaceWithDot());
                    DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(),
                    reco.WarmUpsList.Count - 1 == workoutLogSerieModel.Count ? "00:55" : CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight ? "00:30" : "00:40",
                    AppResources.Lift,
                    nextWeight,
                    LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", nextReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" :  AppResources.times).ReplaceWithDot();
                    if (reco.WarmUpsList.Count > workoutLogSerieModel.Count + 1)
                    { }
                    else
                    {
                        string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                        string exId = $"{CurrentLog.Instance.ExerciseLog.Exercice.Id}";
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                        {
                            if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}",
                                    AppResources.Rest.FirstCharToUpper(),
                                    reco.WarmUpsList.Count - 1 == workoutLogSerieModel.Count ? "00:55" : CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight ? "00:40" : "00:55",
                                    AppResources.Lift,
                                    currentWeight,
                                    LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs",
                                    AppResources.maxLowecase, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                        }
                    }
                }

            }

            if (workoutLogSerieModel.Count == reco.WarmUpsList.Count) // First (and only) work set
            {
                currentWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                      RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Weight.Kg, 1, null, null) :
                                      RoundDownToNearestIncrement(CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Weight.Lb, (decimal)2.5, null, null);
                currentReps = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Reps;
                var nextReps = currentReps / 3;
                if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets)
                {
                    nextReps = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Reps;

                }
                WeightEntry.Text = string.Format("{0:0.00}", currentWeight).ReplaceWithDot();
                RepsEntry.Text = string.Format("{0}", currentReps).ReplaceWithDot();
                //Label1
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());
                string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                string exId = $"{CurrentLog.Instance.ExerciseLog.Exercice.Id}";
                if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                {
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                    {
                        DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", AppResources.maxLowecase, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                        if (popup != null)
                            popup?.SetTimerRepsSets(string.Format("{0:0.00} x {1} {2}", currentWeight, AppResources.maxLowecase, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot());
                    }
                }
                //Label2

                nextReps = nextReps == 0 ? 1 : nextReps;
                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(), restTime, AppResources.Lift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", nextReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                if (nextReps == 1)
                {
                    
                    DoLabel2.Text = DoLabel2.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time).ReplaceWithDot();
                }
                RIR = null;
                LocalDBManager.Instance.SetDBSetting("ComputeRpReps", "true");
            }

            if (workoutLogSerieModel.Count == reco.WarmUpsList.Count + 1) // First mini set
            {
                if (LocalDBManager.Instance.GetDBSetting("ComputeRpReps").Value == "true")
                {
                    currentReps = currentReps / 3;
                    LocalDBManager.Instance.SetDBSetting("ComputeRpReps", "false");
                }

                if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets)
                {
                    currentReps = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Reps;
                }
                currentReps = currentReps == 0 ? 1 : currentReps;
                RepsEntry.Text = string.Format("{0}", currentReps).ReplaceWithDot();
                //Label1
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses - 1), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());
                //Label2
                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(), restTime, AppResources.Lift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();

                if (currentReps == 1)
                {
                    DoLabel.Text = DoLabel.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                    DoLabel2.Text = DoLabel2.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                }

                //Début gestion des RIR (RPE)

                //Popup d'explication
                if (Config.ShowExplainRIRPopUp == false)
                {
                    if (Device.RuntimePlatform.Equals(Device.Android))
                    { 
                    ConfirmConfig ShowExplainRIRPopUp = new ConfirmConfig()
                    {
                        Message = AppResources.NowPleaseTellMeHowHardThatWas,
                        Title = string.Format("{0} {1}!", AppResources.WellDone, LocalDBManager.Instance.GetDBSetting("firstname")?.Value),
                        //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = AppResources.Ok,
                        CancelText = AppResources.RemindMe,
                        OnAction = async (bool ok) =>
                        {
                            if (ok)
                            {
                                Config.ShowExplainRIRPopUp = true;
                                //LocalDBManager.Instance.SetDBSetting("ShowExplainRIRPopUp", "false");
                                AskRIR();
                            }
                            else
                            {
                                Config.ShowExplainRIRPopUp = false;
                                //LocalDBManager.Instance.SetDBSetting("ShowExplainRIRPopUp", "true");
                                AskRIR();
                            }
                        }
                    };
                    UserDialogs.Instance.Confirm(ShowExplainRIRPopUp);
                    }
                    else
                    {
                        var IsAsk = await DisplayAlert(string.Format("{0} {1}!", AppResources.WellDone, LocalDBManager.Instance.GetDBSetting("firstname")?.Value), AppResources.NowPleaseTellMeHowHardThatWas, AppResources.Ok, AppResources.RemindMe);
                        if (IsAsk)
                        {
                            Config.ShowExplainRIRPopUp = true;
                            //LocalDBManager.Instance.SetDBSetting("ShowExplainRIRPopUp", "false");
                            AskRIR();
                        }
                        else
                        {
                            Config.ShowExplainRIRPopUp = false;
                            //LocalDBManager.Instance.SetDBSetting("ShowExplainRIRPopUp", "true");
                            AskRIR();
                        }
                    }
                }
                else
                {
                    AskRIR();
                }

                async void AskRIR()
                {
                    var isTimeBased = CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased;

                    string DoneMore = null;
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                    {
                        while (DoneMore == null)
                        {
                            int RIR;
                        DoneMore = await DisplayActionSheet(null, null, null,
                            AppResources.ThatWasVeryVeryHard,
                            isTimeBased ? "I could have done 1-2 more secs" : AppResources.ICouldHaveDone12MoreRep ,
                            isTimeBased ? "I could have done 3-4 more secs" : AppResources.ICouldHaveDone34MoreReps ,
                            isTimeBased ? "I could have done 5-6 more secs" : AppResources.IcouldHaveDone56MoreReps ,
                            isTimeBased ? "I could have done 7+ more secs" : AppResources.ICouldHaveDone7PMoreReps );
                            if (DoneMore == AppResources.ThatWasVeryVeryHard)
                            {
                                Debug.WriteLine(DoneMore);
                                RIR = 0;
                                ProcessRIR(DoneMore);
                            }
                            else if (DoneMore == AppResources.ICouldHaveDone12MoreRep || DoneMore == "I could have done 1-2 more secs")
                            {
                                Debug.WriteLine(DoneMore);
                                RIR = 1;
                                ProcessRIR(DoneMore);
                            }
                            else if (DoneMore == AppResources.ICouldHaveDone34MoreReps || DoneMore == "I could have done 3-4 more secs")
                            {
                                Debug.WriteLine(DoneMore);
                                RIR = 2;
                                ProcessRIR(DoneMore);
                            }
                            else if (DoneMore == AppResources.IcouldHaveDone56MoreReps || DoneMore == "I could have done 5-6 more secs")
                            {
                                Debug.WriteLine(DoneMore);
                                RIR = 3;
                                ProcessRIR(DoneMore);
                            }
                            else if (DoneMore == AppResources.ICouldHaveDone7PMoreReps || DoneMore == "I could have done 7+ more secs")
                            {
                                Debug.WriteLine(DoneMore);
                                RIR = 4;
                                ProcessRIR(DoneMore);
                            }
                            else if (DoneMore == null)
                            {
                                await DisplayAlert(AppResources.PleaseAnswer, AppResources.ImSorryIDidNotGetYourAnswerINeedToKnow, AppResources.TryAgain);
                            }
                        }
                    }
                    else
                    {
                        ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
                        //actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGreen);
                        actionSheetConfig.Add(AppResources.ThatWasVeryVeryHard, () =>
                        {
                            Debug.WriteLine(DoneMore);
                            RIR = 0;
                            ProcessRIR(AppResources.ThatWasVeryVeryHard);
                        });
                        actionSheetConfig.Add(isTimeBased ? "I could have done 1-2 more secs" : AppResources.ICouldHaveDone12MoreRep, () =>
                        {
                            Debug.WriteLine(DoneMore);
                            RIR = 1;
                            ProcessRIR(isTimeBased ? "I could have done 1-2 more secs" : AppResources.ICouldHaveDone12MoreRep);
                        });
                        actionSheetConfig.Add(isTimeBased ? "I could have done 3-4 more secs" : AppResources.ICouldHaveDone34MoreReps, () =>
                        {
                            Debug.WriteLine(DoneMore);
                            RIR = 2;
                            ProcessRIR(isTimeBased ? "I could have done 3-4 more secs" : AppResources.ICouldHaveDone34MoreReps);
                        });
                        actionSheetConfig.Add(isTimeBased ? "I could have done 5-6 more secs" : AppResources.IcouldHaveDone56MoreReps, () =>
                        {
                            Debug.WriteLine(DoneMore);
                            RIR = 3;
                            ProcessRIR(isTimeBased ? "I could have done 5-6 more secs" : AppResources.IcouldHaveDone56MoreReps);
                        });
                        actionSheetConfig.Add(isTimeBased ? "I could have done 7+ more secs" : AppResources.ICouldHaveDone7PMoreReps, () =>
                        {
                            Debug.WriteLine(DoneMore);
                            RIR = 4;
                            ProcessRIR(isTimeBased ? "I could have done 7+ more secs" : AppResources.ICouldHaveDone7PMoreReps);
                        });
                        UserDialogs.Instance.ActionSheet(actionSheetConfig);
                    }
                    //ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
                    //actionSheetConfig.//AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGreen);
                    //actionSheetConfig.Add(AppResources.ThatWasVeryVeryHard, () =>
                    //{
                    //    Debug.WriteLine(DoneMore);
                    //    RIR = 0;
                    //    ProcessRIR(AppResources.ThatWasVeryVeryHard);
                    //});
                    //actionSheetConfig.Add(AppResources.ICouldHaveDone12MoreRep, () =>
                    //{
                    //    Debug.WriteLine(DoneMore);
                    //    RIR = 1;
                    //    ProcessRIR(AppResources.ICouldHaveDone12MoreRep);
                    //});
                    //actionSheetConfig.Add(AppResources.ICouldHaveDone34MoreReps, () =>
                    //{
                    //    Debug.WriteLine(DoneMore);
                    //    RIR = 2;
                    //    ProcessRIR(AppResources.ICouldHaveDone34MoreReps);
                    //});
                    //actionSheetConfig.Add(AppResources.IcouldHaveDone56MoreReps, () =>
                    //{
                    //    Debug.WriteLine(DoneMore);
                    //    RIR = 3;
                    //    ProcessRIR(AppResources.IcouldHaveDone56MoreReps);
                    //});
                    //actionSheetConfig.Add(AppResources.ICouldHaveDone7PMoreReps, () =>
                    //{
                    //    Debug.WriteLine(DoneMore);
                    //    RIR = 4;
                    //    ProcessRIR(AppResources.ICouldHaveDone7PMoreReps);
                    //});
                    //UserDialogs.Instance.ActionSheet(actionSheetConfig);





                    //};

                }

                async void ProcessRIR(string DoneMore)
                {
                    if (string.IsNullOrEmpty(DoneMore))
                    {
                        AskRIR();
                        return;
                    }
                        
                    if (Config.ShowRIRPopUp == false)
                    {
                        ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
                        {
                            Title = AppResources.GotItExclamation,
                            Message = string.Format("{0} \"{1}\". {2}", AppResources.YouSaid, DoneMore, AppResources.IWillAdjustAccordingly),
                            //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            OkText = AppResources.GotIt,
                            CancelText = AppResources.RemindMe,
                            OnAction = async (bool ok) =>
                            {
                                if (ok)
                                {
                                    Config.ShowRIRPopUp = true;
                                }
                                else
                                {
                                    Config.ShowRIRPopUp = false;
                                }
                            }
                        };
                        UserDialogs.Instance.Confirm(ShowRIRPopUp);
                        return;
                    }
                }

                //Fin gestion des RIR

            }

            if (workoutLogSerieModel.Count == reco.WarmUpsList.Count + 2) // 2nd mini set
            {
                //Label1
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses - 2), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());
                //Label2
                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(), restTime, AppResources.Lift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();

                if (currentReps == 1)
                {
                    DoLabel.Text = DoLabel.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time).ReplaceWithDot();
                    DoLabel2.Text = DoLabel2.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time).ReplaceWithDot();
                }
            }

            if (workoutLogSerieModel.Count == reco.WarmUpsList.Count + 3) // 3nd mini set
            {
                //Label1
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses - 3), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                //Label2
                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());
                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(), restTime, AppResources.Lift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();

                if (currentReps == 1)
                {
                    DoLabel.Text = DoLabel.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                    DoLabel2.Text = DoLabel2.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                }
            }

            if (workoutLogSerieModel.Count == reco.WarmUpsList.Count + 4) // 4th mini set
            {
                //Label1
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses - 4), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());
                //Label2
                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(), restTime, AppResources.Lift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, AppResources.times).ReplaceWithDot();

                if (currentReps == 1)
                {
                    DoLabel.Text = DoLabel.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                    DoLabel2.Text = DoLabel2.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                }
            }

            if (workoutLogSerieModel.Count == reco.WarmUpsList.Count + 5) // 5th mini set
            {
                //Label1
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses - 5), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());
                //Label2
                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(), restTime, AppResources.Lift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();

                if (currentReps == 1)
                {
                    DoLabel.Text = DoLabel.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                    DoLabel2.Text = DoLabel2.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                }
            }

            if (workoutLogSerieModel.Count == reco.WarmUpsList.Count + 6) // 5th mini set
            {
                //Label1
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses - 6), AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();
                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());
                //Label2
                DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(), restTime, AppResources.Lift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();

                if (currentReps == 1)
                {
                    DoLabel.Text = DoLabel.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                    DoLabel2.Text = DoLabel2.Text.Replace(CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "sec" : AppResources.time);
                }
            }


            //To remove
            if (CurrentLog.Instance.ExerciseLog.Exercice.IsUnilateral && isSecondSide && Side1SetworkoutLogSerieModel != null && Side1SetworkoutLogSerieModel.Count > 0 && Side1SetworkoutLogSerieModel.Count > workoutLogSerieModel.Count )
            {
                currentWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? Side1SetworkoutLogSerieModel[workoutLogSerieModel.Count].Weight.Kg : Side1SetworkoutLogSerieModel[workoutLogSerieModel.Count].Weight.Lb;
                currentReps = Side1SetworkoutLogSerieModel[workoutLogSerieModel.Count].Reps;
                WeightEntry.Text = string.Format("{0:0.00}", currentWeight).ReplaceWithDot();
                RepsEntry.Text = string.Format("{0}", currentReps).ReplaceWithDot();
                DoLabel.Text = string.Format("{0} {1} {2:0.00} {3} {4} {5}", Side1SetworkoutLogSerieModel.Count - workoutLogSerieModel.Count, AppResources.SetsLeftLift, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();

                if (popup != null)
                    popup?.SetTimerRepsSets(string.Format("{0:0.00} {1} x {2}", currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", currentReps).ReplaceWithDot());

                if (workoutLogSerieModel.Count > Side1SetworkoutLogSerieModel.Count)
                {
                    DoLabel2.Text = string.Format("{0} {1}—{2} {3:0.00} {4} {5} {6}", AppResources.Rest.FirstCharToUpper(),
                    reco.WarmUpsList.Count - 1 == workoutLogSerieModel.Count ? "00:55" : CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight ? "00:30" : "00:40",
                    AppResources.Lift,
                    LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? Side1SetworkoutLogSerieModel[Side1SetworkoutLogSerieModel.Count].Weight.Kg : Side1SetworkoutLogSerieModel[Side1SetworkoutLogSerieModel.Count].Weight.Lb,
                    LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", Side1SetworkoutLogSerieModel[Side1SetworkoutLogSerieModel.Count].Reps, CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased ? "secs" : AppResources.times).ReplaceWithDot();

                }
            }



            if (workoutLogSerieModel.Count >= reco.WarmUpsList.Count - 1 + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses)
            {
                //Replace text in label 1
                DoLabel.Text = DoLabel.Text.Replace(AppResources.Sets, AppResources.set);
                DoLabel2.Text = string.Format(AppResources.AlmostDoneYouCanDoThis);
            }

            if (workoutLogSerieModel.Count >= reco.WarmUpsList.Count + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses)
            {
                DoLabel.Text = string.Format(AppResources.AllSetsDoneCongrats);
                DoLabel2.Text = string.Format(AppResources.TapFinishExerciseToContinue);

                ChangeButtonsEmphasis();
            }
            else
            {
                ResetButtons();
            }

            WeightEntry.TextChanged += WeightEntry_TextChanged;

            // Fin contrôle de la ligne "Do" et des pickers
        }

        private void ResetButtons()
        {
            SaveSet.BorderColor = Color.White;

            FinishedButton.TextColor = AppThemeConstants.BlueColor;
            FinishedButton.BackgroundColor = Color.Transparent;

            SupersetButton.BackgroundColor = Color.Transparent;
            SupersetButton.BorderWidth = 0;
            SupersetButton.TextColor = AppThemeConstants.BlueColor;

            SupersetButton.FontAttributes = FontAttributes.None;
        }

        private void ChangeButtonsEmphasis()
        {
            SaveSet.BorderColor = Color.Transparent;

            FinishedButton.TextColor = Color.White;
            FinishedButton.BackgroundColor = AppThemeConstants.BlueColor;
            SupersetButton.FontAttributes = FontAttributes.Bold;

            SupersetButton.BorderWidth = 2;
            SupersetButton.CornerRadius = 5;
            SupersetButton.TextColor = AppThemeConstants.BlueColor;
            SupersetButton.BorderColor = AppThemeConstants.BlueColor;
            


        }

        protected override void OnDisappearing()
        {
            if (RepsEntry.IsFocused)
                RepsEntry.Unfocus();
            if (WeightEntry.IsFocused)
                WeightEntry.Unfocus();
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();

            if (RepsEntry.IsFocused)
                RepsEntry.Unfocus();
            if (WeightEntry.IsFocused)
                WeightEntry.Unfocus();

            if (Config.ShowPlateCalculatorPopup == 2)
            {
                App.IsPlatePopup = true;
                ConfirmConfig alertConfig = new ConfirmConfig()
                {
                    Title = "Stop doing plate math",
                    Message = "Not sure how to load the bar? Tap the plate icon (top right) to see which plates to use.",
                    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = AppResources.GotIt,
                    CancelText = AppResources.RemindMe,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            Config.ShowPlateCalculatorPopup = 3;
                        }
                        else
                        {
                            Config.ShowPlateCalculatorPopup = 1;
                        }
                    }
                };
                UserDialogs.Instance.Confirm(alertConfig);
            }
            if (Config.ShowWelcomePopUp4 == false)
            {
                if (App.IsWelcomePopup4)
                    return;
                App.IsWelcomePopup4 = true;
                ConfirmConfig ShowWelcomePopUp4 = new ConfirmConfig()
                {
                    Message = AppResources.ShowWelcomePopUp4Message,
                    Title = AppResources.ShowWelcomePopUp4Title,
                    //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = AppResources.GotIt,
                    CancelText = AppResources.RemindMe,
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            Config.ShowWelcomePopUp4 = true;
                        }
                        else
                        {
                            Config.ShowWelcomePopUp4 = false;
                        }
                    }
                };
                await Task.Delay(100);
                UserDialogs.Instance.Confirm(ShowWelcomePopUp4);
            }

        }

        public void OnEdit(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            WorkoutLogSerieModelEx m = (WorkoutLogSerieModelEx)mi.CommandParameter;
            OnCancelClicked(sender, e);
            //Edit workout log
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,
                Title = $"{AppResources.Edit} {m.Exercice.Label}",
                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? Math.Round(m.Weight.Lb, 2).ToString().ReplaceWithDot() : Math.Round(m.Weight.Kg, 2).ToString().ReplaceWithDot(),
                OkText = AppResources.Edit,
                //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogFirstTimeExercise),
                OnAction = async (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    m.Weight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                    AskForEditReps(m);

                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
            //}
        }

        async void AskForEditReps(WorkoutLogSerieModelEx m)
        {
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = InputType.Number,
                IsCancellable = true,
                Title = string.Format("{0} {1}", AppResources.Edit, m.Exercice.Label),
                Message = AppResources.EnterNewReps,
                Placeholder = AppResources.TapToEnterHowMany,
                Text = m.Reps.ToString(),
                OkText = AppResources.Edit,
                //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogFirstTimeExercise),
                OnAction = async (weightResponse) =>
                {
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    try
                    {
                        int reps = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
                        m.Reps = reps;
                        Device.BeginInvokeOnMainThread(() =>
                        {
                            foreach (WorkoutLogSerieModelEx wlsme in workoutLogSerieModel)
                                wlsme.OnPropertyChanged("SetLabel");
                        });
                    }
                    catch (Exception ex)
                    {

                    }

                }
            };
            firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }

        public void OnDelete(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            WorkoutLogSerieModelEx m = (WorkoutLogSerieModelEx)mi.CommandParameter;
            workoutLogSerieModel.RemoveAt(workoutLogSerieModel.IndexOf(m));
            Device.BeginInvokeOnMainThread(() =>
            {
                foreach (WorkoutLogSerieModelEx wlsme in workoutLogSerieModel)
                    wlsme.OnPropertyChanged("SetLabel");
            });

            UpdateTopLabels();
        }

        void RepsEntry_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (RepsEntry.Text.EndsWith(",") || RepsEntry.Text.EndsWith(".") || string.IsNullOrEmpty(RepsEntry.Text))
                return;

            currentReps = Convert.ToInt32(RepsEntry.Text.Replace(",", "").Replace(".", ""));

        }

        void OnCancelClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            s.Children[0].IsVisible = false;
            s.Children[1].IsVisible = false;
            s.Children[2].IsVisible = false;
            s.Children[3].IsVisible = true;
        }

        void OnContextMenuClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            s.Children[0].IsVisible = true;
            s.Children[1].IsVisible = true;
            s.Children[2].IsVisible = true;
            s.Children[3].IsVisible = false;
        }

        private int GetIndex(WorkoutLogSerieModelEx wl)
        {
            return workoutLogSerieModel.IndexOf(wl) + 1;
        }

        async void ShowPlateCalculater()
        {
            //NavigationPage.SetHasNavigationBar(this, false);
            //if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg")
            //    CalculateKgPlate();
            //else
            //    CalculateLbsPlate();
            //PlateView.IsVisible = true;
            CurrentLog.Instance.CurrentWeight = currentWeight;
            var page = new PlateCalculatorPopup();
            await PopupNavigation.Instance.PushAsync(page);
        }


        string TimerEntry = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;

        private async void SaveSet_Clicked(object sender, EventArgs e)
        {
            
            if (currentWeight > 0 || currentReps > 0)
            {
                Debug.WriteLine("RIR = " + RIR);
                var reco = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id];
                WorkoutLogSerieModelEx serieModel = new WorkoutLogSerieModelEx()
                {
                    Exercice = new ExerciceModel() { Id = CurrentLog.Instance.ExerciseLog.Exercice.Id },
                    Reps = currentReps,
                    UserId = CurrentLog.Instance.ExerciseLog.UserId,
                    Weight = new MultiUnityWeight(currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value),
                    RIR = RIR,
                    IsWarmups = workoutLogSerieModel.Count < reco.WarmUpsList.Count ? true : false
                };

                if (workoutLogSerieModel.Count >= reco.WarmUpsList.Count)
                {
                    try
                    {
                        
                        if (LocalDBManager.Instance.GetDBSetting($"Sets{DateTime.Now.Date}") == null)
                            LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", "1");
                        else
                        {
                            var setCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"Sets{DateTime.Now.Date}").Value);
                            setCount += 1;
                            LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", $"{setCount}");
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                }
                serieModel.OnGetIndex += () => { return GetIndex(serieModel).ToString(); };
                workoutLogSerieModel.Add(serieModel);

                var last = SetListView.ItemsSource.Cast<object>().LastOrDefault();
                SetListView.ScrollTo(last, ScrollToPosition.MakeVisible, true);

                // Contrôle du timer en fonction des séries
                if (LocalDBManager.Instance.GetDBSetting("timer_autostart") == null)
                    LocalDBManager.Instance.SetDBSetting("timer_autostart", "true");
                if (LocalDBManager.Instance.GetDBSetting("timer_autostart").Value == "true")
                {
                    if (Timer.Instance.State == "RUNNING")
                    {
                        await Timer.Instance.StopTimer();
                        //await Task.Delay(1000);
                    }

                    if (reco.WarmUpsList.Count >= workoutLogSerieModel.Count)
                    {
                        var time = reco.WarmUpsList.Count == workoutLogSerieModel.Count ? "55" : CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight ? "30" : "40";
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", time);
                        timeRemain = time;
                    }
                    else
                    {
                        if (LocalDBManager.Instance.GetDBSetting("timer_autoset").Value == "true")
                        {
                            LocalDBManager.Instance.SetDBSetting("timer_remaining", CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, false, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets).ToString());
                            //LocalDBManager.Instance.SetDBSetting("timer_remaining", "25");
                            timeRemain = CurrentLog.Instance.GetRecommendationRestTime(CurrentLog.Instance.ExerciseLog.Exercice.Id, false, CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsNormalSets).ToString();
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting("timer_remaining", TimerEntry);
                            timeRemain = TimerEntry;
                        }
                    }


                    Timer.Instance.StartTimer();
                    //((DrMusclePage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).HideTimerIcon();
                    try
                    {
                        if (((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage is DrMusclePage)
                            ((DrMusclePage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).HideTimerIcon();
                        else
                        {
                            var navigation = (((MainTabbedPage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).CurrentPage.Navigation);
                            ((DrMusclePage)navigation.NavigationStack[navigation.NavigationStack.Count - 1]).HideTimerIcon();
                            //((MainTabbedPage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).CurrentPage.Navigation.NavigationStack[0]).HideTimerIcon();

                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                        LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                    if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" && !(workoutLogSerieModel.Count >= CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpsList.Count + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses))
                    {

                        popup = new TimerPopup(false);
                        TimerBased = CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased;
                        popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", popup.RemainingSeconds);
                        popup.popupTitle = "";
                        popup.SetTimerText();
                        Timer.Instance.Remaining = int.Parse(popup.RemainingSeconds);
                        PopupNavigation.Instance.PushAsync(popup);
                        //TimerView.IsVisible = true;
                        //NavigationPage.SetHasNavigationBar(this, false);
                    }
                    else if(LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true")
                    {
                        
                    }

                }
            }

            // Fin du contrôle du timer en fonction des séries

            UpdateTopLabels();
        }
        bool TimerBased = false;
        string timeRemain = "0";
        async void OnTimerDone()
        {
            try
            {
                if (TimerBased && LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true" && !(workoutLogSerieModel.Count >= CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].WarmUpsList.Count + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Series + CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].NbPauses))
                {
                    await Task.Delay(100);
                    TimerBased = false;
                    popup = new TimerPopup(false);
                    LocalDBManager.Instance.SetDBSetting("timer_remaining", RepsEntry.Text);
                    popup.popupTitle = "Work";
                    popup?.SetTimerRepsSets("");
                    popup.RemainingSeconds = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                    Timer.Instance.Remaining = int.Parse(popup.RemainingSeconds);
                    PopupNavigation.Instance.PushAsync(popup);
                    Timer.Instance.StartTimer();
                    //TimerView.IsVisible = true;
                    //NavigationPage.SetHasNavigationBar(this, false);
                }
            }
            catch (Exception ex)
            {

            }

        }

        void WeightEntry_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (WeightEntry.Text.EndsWith(",") || WeightEntry.Text.EndsWith("."))
                {

                }
                else
                {
                    if (!string.IsNullOrEmpty(WeightEntry.Text.Trim()))
                    {
                        Debug.WriteLine(string.Format("weightStep : {0}", weightStep));
                        Debug.WriteLine(string.Format("string before : {0}", WeightEntry.Text));
                        Debug.WriteLine(string.Format("currentWeight before : {0}", currentWeight));

                        string entryText = WeightEntry.Text.Replace(",", ".");
                        entryText = entryText.Replace(" ", "");
                        currentWeight = Convert.ToDecimal(entryText, CultureInfo.InvariantCulture);

                        Debug.WriteLine(string.Format("string after : {0}", WeightEntry.Text));
                        Debug.WriteLine(string.Format("currentWeight after : {0}", currentWeight));

                    }
                    else
                        currentWeight = 0;
                }
            }
            catch (Exception ex)
            {
                currentWeight = 0;
            }
        }

        private void RepsMore_Clicked(object sender, EventArgs e)
        {
            RepsEntry.TextChanged -= RepsEntry_TextChanged;
            currentReps += 1;
            RepsEntry.Text = string.Format("{0}", currentReps);
            RepsEntry.TextChanged += RepsEntry_TextChanged;
        }

        private void RepsLess_Clicked(object sender, EventArgs e)
        {
            if (currentReps <= 1)
                return;
            RepsEntry.TextChanged -= RepsEntry_TextChanged;
            currentReps -= 1;
            RepsEntry.Text = string.Format("{0}", currentReps);
            RepsEntry.TextChanged += RepsEntry_TextChanged;
        }

        private async void WeightMore_Clicked(object sender, EventArgs e)
        {
            WeightEntry.TextChanged -= WeightEntry_TextChanged;

            currentWeight += weightStep;
            if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight)
            {
                await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                {
                    BodyWeight = new MultiUnityWeight(currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                });
            }

            WeightEntry.Text = string.Format("{0:0.00}", currentWeight).ReplaceWithDot();
            WeightEntry.TextChanged += WeightEntry_TextChanged;
        }

        async void WeightEntry_Unfocused(object sender, Xamarin.Forms.FocusEventArgs e)
        {
            if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight && currentWeight != 0)
            {
                await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                {
                    BodyWeight = new MultiUnityWeight(currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                });
            }
        }

        private async void WeightLess_Clicked(object sender, EventArgs e)
        {
            if (currentWeight - weightStep <= 0)
                return;
            WeightEntry.TextChanged -= WeightEntry_TextChanged;

            
            currentWeight -= weightStep;
            if (CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsBodyweight)
            {
                await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                {
                    BodyWeight = new MultiUnityWeight(currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                });
            }

            WeightEntry.Text = string.Format("{0:0.00}", currentWeight).ReplaceWithDot();
            WeightEntry.TextChanged += WeightEntry_TextChanged;
        }

        private async void Finished_Clicked(object sender, EventArgs e)
        {

            if (FinishedButton.Text == "Finish side 1" && CurrentLog.Instance.ExerciseLog.Exercice.IsUnilateral)
            {
                if (isSecondSide)
                    isSecondSide = false;
                FinishedButton.Text = AppResources.FinishExercise;
                if (CurrentLog.Instance.ExerciseLog.Exercice.IsUnilateral && isFirstSide)
                {
                    isFirstSide = false;
                    isSecondSide = true;
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        if (CurrentLog.Instance.WorkoutLogSeriesByExercise.ContainsKey(CurrentLog.Instance.ExerciseLog.Exercice.Id))
                        {
                            Side1SetworkoutLogSerieModel = workoutLogSerieModel;
                            workoutLogSerieModel = new ObservableCollection<WorkoutLogSerieModelEx>();
                            SetListView.ItemsSource = workoutLogSerieModel;
                            CurrentLog.Instance.WorkoutLogSeriesByExercise.Remove(CurrentLog.Instance.ExerciseLog.Exercice.Id);
                            CurrentLog.Instance.WorkoutLogSeriesByExercise.Add(CurrentLog.Instance.ExerciseLog.Exercice.Id, workoutLogSerieModel);
                            UpdateTopLabels();
                            ResetButtons();
                        }
                    });
                    AlertConfig ShowExplainRIRPopUp = new AlertConfig()
                    {
                        Title = "Well done! Now do all sets for side 2",
                        //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = AppResources.Ok,

                    };

                    UserDialogs.Instance.Alert(ShowExplainRIRPopUp);
                    return;
                }
            }
            ConfirmConfig ShowRIRPopUp = new ConfirmConfig()
            {
                Title = "Finish exercise?",
                Message = "Are you sure you are finished and want to save this exercise?",
                //AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OkText = "Finish & save",
                CancelText = AppResources.Cancel,
                OnAction = async (bool ok) =>
                {
                    if (ok)
                    {
                        PushToDataServer();
                    }
                    else
                    {
                    }
                }
            };
            UserDialogs.Instance.Confirm(ShowRIRPopUp);
        }

        private async void PushToDataServer()
        {

            try
            {
                isFirstSide = false;
                isSecondSide = false;
                if (LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Date}") == null)
                    LocalDBManager.Instance.SetDBSetting($"Exercises{DateTime.Now.Date}", "1");
                else
                {
                    var exeCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Date}").Value);
                    exeCount += 1;
                    LocalDBManager.Instance.SetDBSetting($"Exercises{DateTime.Now.Date}", $"{exeCount}");
                }

            }
            catch (Exception ex)
            {

            }
            bool result = true;
            try
            {
                foreach (WorkoutLogSerieModel l in workoutLogSerieModel)
                {
                    Debug.WriteLine("RIR = " + RIR);
                    BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerie(l);
                    result = result && b.Result;
                }
                //CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Increments
                DateTime? maxDate = null;
                try
                {
                    string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
                    string exId = $"{CurrentLog.Instance.ExerciseLog.Exercice.Id}";
                    if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge") != null)
                    {
                        if (LocalDBManager.Instance.GetDBReco("RReps" + exId + setStyle + "challenge").Value == "max")
                        {
                            maxDate = DateTime.Now;
                        }
                    }
                    await DrMuscleRestClient.Instance.AddUpdateExerciseUserLightSession(new LightSessionModel()
                    {
                        ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id,
                        IsLightSession = CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].IsLightSession,
                        MaxChallengeDate = maxDate
                    });
                }
                catch (Exception ex)
                {

                }
            }
            catch (Exception ex)
            {
                var properties = new Dictionary<string, string>
                {
                    { "FinishExercise", $"{ex.StackTrace}" }
                };
                Crashes.TrackError(ex, properties);
            }
            try
            {
                if (result)
                {
                    if (Timer.Instance.State == "RUNNING")
                    {
                        await Timer.Instance.StopTimer();
                    }

                    if ((Application.Current as App)?.FinishedExercices.FirstOrDefault(x => x.Id == CurrentLog.Instance.ExerciseLog.Exercice.Id) == null)
                        (Application.Current as App)?.FinishedExercices.Add(CurrentLog.Instance.ExerciseLog.Exercice);


                    if (!CurrentLog.Instance.IsFromExercise)
                        LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.ExerciseLog.Exercice.Id}", "true");
                    LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.ExerciseLog.Exercice.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
                    LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.ExerciseLog.Exercice.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
                    DependencyService.Get<IFirebase>().LogEvent("finished_exercise", "");
                    CurrentLog.Instance.WorkoutLogSeriesByExercise.Remove(CurrentLog.Instance.ExerciseLog.Exercice.Id);
                    await PagesFactory.PushAsync<EndExercisePage>();
                }
            }
            catch (Exception ex)
            {
                var properties = new Dictionary<string, string>
                    {
                        { "FinishExerciseResultHandle", $"{ex.StackTrace}" }
                    };
                Crashes.TrackError(ex, properties);
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError
                            });
            }
        }
        public static decimal RoundToNearestIncrement(decimal numToRound, decimal step, decimal? min, decimal? max)
        {
            try
            {
                if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null && LocalDBManager.Instance.GetDBSetting("workout_increments")?.Value != null && CurrentLog.Instance.RecommendationsByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id].Increments != null)
                {
                    return numToRound;
                }
                else
                {
                    if (step == 0)
                        return numToRound;

                    if (min != null)
                    {
                        var numAdjustedForMin = (decimal)min;
                        while (numAdjustedForMin < numToRound)
                        {
                            numAdjustedForMin += step;
                        }
                        var numRounded = numAdjustedForMin;

                        if (max != null)
                        {
                            if (numRounded > max)
                                numRounded = (decimal)max;
                        }

                        return numRounded;
                    }
                    else
                    {
                        //Calc the floor value of numToRound
                        decimal floor = ((long)(numToRound / step)) * step;

                        //round up if more than 60% way of step
                        decimal round = floor;
                        decimal remainder = numToRound - floor;
                        if (remainder > (step * (decimal)0.40))
                            round += step;
                        if (max != null && round > max)
                        {
                            round = (decimal)max;
                            var steps = round % step;
                            round = round - steps;
                        }
                        return round;
                    }
                }
            }
            catch (Exception ex)
            {
                return numToRound;
            }

        }
        public static decimal RoundDownToNearestIncrement(decimal numToRound, decimal step, decimal? min, decimal? max, bool isRoundUp = false)
        {
            try
            {
                
                    if (step == 0)
                        return numToRound;

                    if (min != null)
                    {
                        var numAdjustedForMin = (decimal)min;
                        while (numAdjustedForMin < numToRound)
                        {
                            numAdjustedForMin += step;
                        }
                        var numRounded = numAdjustedForMin;

                        if (max != null)
                        {
                            if (numRounded > max)
                                numRounded = (decimal)max;
                        }

                        return numRounded;
                    }
                    else
                    {
                        if (step == 1 || step == (decimal)2.5)
                        {
                            if (numToRound == 1 ||
                                numToRound == 2 ||
                                numToRound == 3 ||
                                numToRound == 8 ||
                                numToRound == 12)
                            {
                                return numToRound;
                            }
                        }
                        //Calc the floor value of numToRound
                        decimal floor = ((long)(numToRound / step)) * step;

                        //round up if more than 60% way of step
                        decimal round = floor;
                        decimal remainder = numToRound - floor;
                        if (isRoundUp)
                        {
                            if (remainder > (step * (decimal)0.05))
                                round += step;
                        }
                        else if (remainder > (step * (decimal)0.60))
                            round += step;
                        if (max != null && round > max)
                            round = (decimal)max;
                        return round;
                    }
                
            }
            catch (Exception ex)
            {
                return numToRound;
            }
            
        }

        public static decimal RoundDownToNearestIncrementLb(decimal numToRound, decimal step, decimal? min, decimal? max)
        {
            try
            {
                
                    if (step == 0)
                        return numToRound;

                    if (min != null)
                    {
                        var numAdjustedForMin = (decimal)min;
                        while (numAdjustedForMin < numToRound)
                        {
                            numAdjustedForMin += step;
                        }
                        var numRounded = numAdjustedForMin;

                        if (max != null)
                        {
                            if (numRounded > max)
                                numRounded = (decimal)max;
                        }

                        return numRounded;
                    }
                    else
                    {
                        if (step == 1 || step == (decimal)2.5)
                        {
                            if (numToRound == 1 ||
                                numToRound == 2 ||
                                numToRound == 3 ||
                                numToRound == 8 ||
                                numToRound == 12)
                            {
                                return numToRound;
                            }
                        }
                        //Calc the floor value of numToRound
                        decimal floor = ((long)(numToRound / step)) * step;

                        //round up if more than 60% way of step
                        decimal round = floor;
                        //decimal remainder = numToRound - floor;
                        //if (remainder > (step * (decimal)0.60))
                        //    round += step;
                        if (max != null && round > max)
                            round = (decimal)max;
                        return round;

                    }
                
            }
            catch (Exception ex)
            {
                return numToRound;
            }

        }
        public static decimal RoundDownToNearest(decimal numToRound, decimal step)
        {
            try
            {

            
            if (LocalDBManager.Instance.GetDBSetting("workout_increments") != null && LocalDBManager.Instance.GetDBSetting("workout_increments")?.Value != null)
            {
                return numToRound;
            }
            else
            {
                if (step == 0)
                    return numToRound;

                if (step == 1 || step == (decimal)2.5)
                {
                    if (numToRound == 1 ||
                        numToRound == 2 ||
                        numToRound == 3 ||
                        numToRound == 8 ||
                        numToRound == 12 )
                    {
                        return numToRound;
                    }
                }
                //Calc the floor value of numToRound
                decimal floor = ((long)(numToRound / step)) * step;

                //round up if more than 60% way of step
                decimal round = floor;
                decimal remainder = numToRound - floor;
                if (remainder > (step * (decimal)0.60))
                    round += step;

                return round;
            }
            }
            catch (Exception ex)
            {
                return numToRound;
            }
        }
    }

    public class PositionConverter : IValueConverter
    {

        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            return null;
            /*
            ListBoxItem item = value as ListBoxItem;
            ListBox view = ItemsControl.ItemsControlFromItemContainer(item) as ListBox;
            int index = view.ItemContainerGenerator.IndexFromContainer(item);
            return index.ToString();*/
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new Exception("The method or operation is not implemented.");
        }
    }
}