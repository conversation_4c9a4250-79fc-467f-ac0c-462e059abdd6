﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMuscle.Dependencies;
using DrMuscle.Helpers;
using DrMuscle.Layout;
using DrMuscle.Resx;
using DrMuscle.Screens.Subscription;
using DrMuscle.Screens.User;
using DrMuscle.Screens.Workouts;
using Plugin.Connectivity;
using Rg.Plugins.Popup.Services;
using Xamarin.Forms;

namespace DrMuscle.Screens.Exercises
{
    public partial class NewExercisePage : DrMusclePage
    {
        public NewExercisePage()
        {
            InitializeComponent();
            DrMuscleWorkoutsButton.Clicked += DrMuscleWorkoutsButton_Clicked;
            ExercisesButton.Clicked += ExercisesButton_Clicked;
            MyWorkoutButton.Clicked += MyWorkoutButton_Clicked;
            RefreshLocalized();
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });
        }

        private async void DrMuscleWorkoutsButton_Clicked(object sender, EventArgs e)
        {
            if (CrossConnectivity.Current.IsConnected)
            {
                if (!App.IsV1UserTrial && !App.IsFreePlan)
                {
                    await PagesFactory.PushAsync<SubscriptionPage>();
                    return;
                }
            }
            await PagesFactory.PushAsync<ChooseGymOrHome>();
        }
        private async void ExercisesButton_Clicked(object sender, EventArgs e)
        {
            await PagesFactory.PushAsync<AllExercisePage>();
        }

        private async void MyWorkoutButton_Clicked(object sender, EventArgs e)
        {
            if (CrossConnectivity.Current.IsConnected)
            {
                if (!App.IsV1UserTrial && !App.IsFreePlan)
                {
                    await PagesFactory.PushAsync<SubscriptionPage>();
                    return;
                }
            }
            if (App.IsFreePlan)
            {
                ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                {
                    Message = "Upgrading will unlock custom workouts and programs.",
                    Title = "You discovered a premium feature!",
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = "Upgrade",
                    CancelText = "Maybe later",
                    OnAction = async (bool ok) =>
                    {
                        if (ok)
                        {
                            PagesFactory.PushAsync<SubscriptionPage>();
                        }
                        else
                        {

                        }
                    }
                };
                await Task.Delay(100);
                UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
            else
                await PagesFactory.PushAsync<ChooseYourCustomWorkoutPage>();
        }
        private void RefreshLocalized()
        {
            Title = "Exercise";
            //DrMuscleWorkoutsButton.Text = AppResources.DrMuscleWorkouts;
            //MyWorkoutButton.Text = AppResources.CustomWorkouts;
            DrMuscleWorkoutsButton.Text = "Dr. Muscle workouts";
            MyWorkoutButton.Text = "Custom workouts";
            ExercisesButton.Text = AppResources.Exercises;
        }

        public override void OnBeforeShow()
        {
            base.OnBeforeShow();
        }

       protected override bool OnBackButtonPressed()
        {
            if (PopupNavigation.Instance.PopupStack.Count > 0)
            {
                PopupNavigation.Instance.PopAllAsync();
                return true;
            }
            Device.BeginInvokeOnMainThread(async () =>
            {
                ConfirmConfig exitPopUp = new ConfirmConfig()
                {
                    Title = AppResources.Exit,
                    Message = AppResources.AreYouSureYouWantToExit,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    OkText = AppResources.Yes,
                    CancelText = AppResources.No,
                };

                var result = await UserDialogs.Instance.ConfirmAsync(exitPopUp);
                if (result)
                {
                    var kill = DependencyService.Get<IKillAppService>();
                    kill.ExitApp();
                }

            });

            return true;
        }
    }
}
