using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMuscle.Constants;
using DrMuscle.Layout;
using DrMuscle.Resx;
using DrMuscleWebApiSharedModel;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace DrMuscle.Screens.History
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class HistortWeightPage : DrMusclePage
    {
        private List<UserWeight> allWeights = new List<UserWeight>();
        private List<YearlyUserWeight> yearlyWeights = new List<YearlyUserWeight>();
        private StackLayout contextMenu = null;


        public HistortWeightPage()
        {
            InitializeComponent();
            HistoryListView.HasUnevenRows = true;
            HistoryListView.ItemTemplate = new HistoryWeightDataTemplateSelector
            {
                HistoryDateTemplate = SetHeaderTemplate,
                HistorySetTemplate = SetTemplate,
               
            };
            HistoryListView.ItemSelected += HistoryListViewOnItemSelected;
            Title = "Body weight history";
        }

        public override void OnBeforeShow()
        {
            base.OnBeforeShow();
            getUseeWeights();
        }

        private async void getUseeWeights()
        {
            allWeights = await DrMuscleRestClient.Instance.GetUserWeightsWithLoader();
            var newWeightList = new List<UserWeight>();
            foreach (var item in allWeights)
            {
                if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
                {
                    massUnitType.Text = "KG";
                    item.Weight = Math.Round(new MultiUnityWeight(item.Weight, "kg").Kg, 2);
                    item.Label = string.Format("{0:0.##}",item.Weight);
                }
                else
                {
                    massUnitType.Text = "LBS";
               
                    item.Weight = Math.Round(new MultiUnityWeight(item.Weight, "kg").Lb, 2);
                    item.Label = string.Format("{0:0.##}",item.Weight);
                }
            }

            // var list = allWeights.GroupBy(x => x.CreatedDate.Year);
            // foreach (var key in list)
            // {
            //     List<UserWeight> group = new List<UserWeight>();
            //     var yearlyGroup = new YearlyUserWeight() { YearDate =  key.Key.ToString()};
            //     foreach (var weight in key)
            //     {
            //         yearlyGroup.Add(weight);
            //     }
            //     yearlyWeights.Add(yearlyGroup);
            // }
            //
            // HistoryListView.ItemsSource = yearlyWeights; //allWeights;
            ParseUserWeights();
        }

        void ParseUserWeights()
        {
            var list = allWeights.GroupBy(x => x.CreatedDate.Year);
            List<UserWeight> group = new List<UserWeight>();
            foreach (var key in list)
            {
                group.Add(new UserWeight() { Label = key.Key.ToString(), Weight = 0});
                
                foreach (var weight in key)
                {
                    group.Add(weight);
                }
                
            }

            HistoryListView.ItemsSource = group;
        }
        void OnCancelClicked(object sender, System.EventArgs e)
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            
            if (s.Children.Count == 4)
            {
                s.Children[0].IsVisible = false;
                s.Children[1].IsVisible = false;
                s.Children[2].IsVisible = false;
                s.Children[3].IsVisible = true;
            }
            else
            {
                s.Children[0].IsVisible = false;
                s.Children[1].IsVisible = false;
                s.Children[2].IsVisible = true;
            }
        }

        void OnContextMenuClicked(object sender, System.EventArgs e)
        {
            hideButtons();
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            contextMenu = s;
           Device.BeginInvokeOnMainThread(() => {
               s.Children[0].IsVisible = true;
               s.Children[1].IsVisible = true;
               s.Children[2].IsVisible = false;
           });
               
           
        }

        private void HistoryListViewOnItemSelected(object sender, SelectedItemChangedEventArgs e)
        {
            hideButtons();
        }

        private void hideButtons()
        {
            if (contextMenu != null)
            {
                try
                {
                    
                        contextMenu.Children[0].IsVisible = false;
                        contextMenu.Children[1].IsVisible = false;
                        contextMenu.Children[2].IsVisible = true;
            
                }
                catch (Exception exception)
                {
                    
                    
                }
            }
        }
        public async void OnDeleteWeightClicked(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            UserWeight m = (UserWeight)mi.CommandParameter;
            OnCancelClicked(sender, e);
            
            if (allWeights.Count == 1)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = "You can't remove all the weights",
                    Title = "Error"
                });                
                return;
            }
            
            var r = await UserDialogs.Instance.ConfirmAsync(new ConfirmConfig()
            {
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                Title = "Are you sure?",
                CancelText = AppResources.Cancel,
                OkText = AppResources.Delete
            });
            if (r) {
                var result = await DrMuscleRestClient.Instance.DeleteUserWeightHistory(m);
            
                allWeights.Remove(m);
                HistoryListView.ItemsSource = null;
                ParseUserWeights();
                }
        }
        
        public async void OnEditClicked(object sender, EventArgs e)
        {
            var mi = ((Button)sender);
            UserWeight m = (UserWeight)mi.CommandParameter;
            OnCancelClicked(sender, e);
            var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
            PromptConfig firsttimeExercisePopup = new PromptConfig()
            {
                InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
                IsCancellable = true,

                Title = $"{AppResources.Edit} weight",

                Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
                Text = m.Label,//LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? m.Weight.ToString().ReplaceWithDot() : m.Weight.ToString().ReplaceWithDot(),
                OkText = AppResources.Edit,
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                OnAction = async (weightResponse) =>
                {
                    if (!weightResponse.Ok)
                        return;
                    if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                    var weightText = weightResponse.Value.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    var updatedWeight = new UserWeight() { };
                    updatedWeight.Weight = weight1;
                    updatedWeight.Id = m.Id;
                    updatedWeight.CreatedDate = m.CreatedDate;

                    if (LocalDBManager.Instance .GetDBSetting("massunit").Value == "lb")
                    {
                        weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        updatedWeight.Weight = new MultiUnityWeight(weight1, "lb").Kg;
                        m.Label = string.Format("{0:0.##}",weight1);
                    }
                    else
                    {
                        m.Label = string.Format("{0:0.##}",weight1);
                    }
                    m.Weight = weight1;
                    
                    HistoryListView.ItemsSource = null;
                    ParseUserWeights();
                    await DrMuscleRestClient.Instance.UpdateUserWeightHistory(updatedWeight); 
                    

                }
            };

            firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
            UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        }
    }
    
    public class YearlyUserWeight : List<UserWeight>
    {
        public string YearDate { get; set; }
    }
    
    public class HistoryWeightDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate HistoryDateTemplate { get; set; }
        public DataTemplate HistorySetTemplate { get; set; }
        

        protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
        {
            var we = ((UserWeight)item);
            if (we.Weight == 0)
            {
                return HistoryDateTemplate;
            }

            return HistorySetTemplate;
        }
    }
}