﻿<?xml version="1.0" encoding="UTF-8"?>
<t:DrMusclePage
    xmlns:t="clr-namespace:DrMuscle.Layout"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
    xmlns:local="clr-namespace:DrMuscle.Cells"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
    x:Class="DrMuscle.Screens.User.LearnPage">
    <t:DrMusclePage.Resources>
        <ResourceDictionary>
            <local:BotDataTemplateSelector
                x:Key="BotTemplateSelector">
            </local:BotDataTemplateSelector>
        </ResourceDictionary>
    </t:DrMusclePage.Resources>
    <StackLayout
        Spacing="10" Margin="0,0,-30,0" Padding="0,0,0,15"
                 BackgroundColor="#f4f4f4">
        <t:DrMuscleListView
            BackgroundColor="#f4f4f4"
            ItemTemplate="{StaticResource BotTemplateSelector}"
            HasUnevenRows="True"
            ItemAppearing="lstChats_ItemAppearing"
            ItemTapped="lstChats_ItemTapped"
            x:Name="lstChats"
            VerticalOptions="FillAndExpand"
            FlowDirection="LeftToRight"
            SeparatorColor="Transparent" />


        <!--<pancakeView:PancakeView
            x:Name="BtnShare"
            IsVisible="false"
            IsClippedToBounds="true"
            HasShadow="False"
            BackgroundGradientAngle="90"
            CornerRadius="0"
            BorderThickness="0"
            Margin="30,0"
            VerticalOptions="End"
            HorizontalOptions="FillAndExpand"
            HeightRequest="70">
            <pancakeView:PancakeView.BackgroundGradientStops>
                <pancakeView:GradientStopCollection>
                    <pancakeView:GradientStop
                        Color="#0C2432"
                        Offset="0" />
                    <pancakeView:GradientStop
                        Color="#195276"
                        Offset="1" />

                </pancakeView:GradientStopCollection>
            </pancakeView:PancakeView.BackgroundGradientStops>
            <Label
                x:Name="LblShare"
                VerticalOptions="Center"
                HorizontalOptions="Center"
                VerticalTextAlignment="Center"
                Text="Share a free month of Dr. Muscle"
                TextColor="White"
                BackgroundColor="Transparent"
                IsEnabled="False"
                FontSize="18">

            </Label>
            <pancakeView:PancakeView.GestureRecognizers>
                <TapGestureRecognizer
                    Tapped="BtnShare_Clicked"
                    CommandParameter="{Binding .}" />
            </pancakeView:PancakeView.GestureRecognizers>
        </pancakeView:PancakeView>-->

    </StackLayout>
</t:DrMusclePage>
