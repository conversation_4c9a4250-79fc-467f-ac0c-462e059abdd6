﻿using System;
using System.Collections.Generic;
using Acr.UserDialogs;
using DrMuscle.Helpers;
using DrMuscle.Layout;
//using DrMuscle.Interfaces;
using DrMuscle.Message;
using DrMuscle.Services;
using DrMuscleWebApiSharedModel;
using Rg.Plugins.Popup.Pages;
using Rg.Plugins.Popup.Services;
using Xamarin.Forms;

namespace DrMuscle.Views
{	
	public partial class TimePickerView : PopupPage
    {
        bool IsFeet = true;
        IList<int> minutesList = new List<int>();
        IList<int> secondsList = new List<int>();
        WorkoutLogSerieModelRef workout = null;
        public TimePickerView(WorkoutLogSerieModelRef w)
        {
            InitializeComponent();
            minutesList = new List<int>();

            secondsList = new List<int>();
            workout = w;

            for (int i = 0; i < 60; i++)
            {
                minutesList.Add(i);
                secondsList.Add(i);
            }
            PickerMin.ItemsSource = minutesList;
            PickerSec.ItemsSource = secondsList;
            
            if (w != null && w.Reps > 0)
            {
                workout = w;
                var timeSpan = TimeSpan.FromSeconds(w.Reps);
                PickerMin.SelectedIndex = minutesList.IndexOf(timeSpan.Minutes);
                PickerSec.SelectedIndex = secondsList.IndexOf(timeSpan.Seconds);
            }
        }

        public async void BtnFeetClicked(object sender, EventArgs args)
        {
            //BtnLbs.BackgroundColor = Color.FromHex("#5CD196");
           
            IsFeet = true;
        }


        public async void BtnDoneClicked(object sender, EventArgs args)
        {

            
            try
            {
                if (PickerMin.SelectedIndex == 0 && PickerSec.SelectedIndex == 0)
                {
                    return;
                }
                var timeSpan = new TimeSpan(0, minutesList[PickerMin.SelectedIndex], secondsList[PickerSec.SelectedIndex]);
                if (workout != null)
                {
                    workout.Reps = (int)timeSpan.TotalSeconds;
                    if (!workout.IsBackOffSet && !workout.IsWarmups) //&& !workout.IsFinished && !workout.IsEditing
                        Xamarin.Forms.MessagingCenter.Send<WeightRepsUpdatedMessage>(new WeightRepsUpdatedMessage() { model = workout }, "WeightRepsUpdatedMessage");
                }

                await PopupNavigation.Instance.PopAsync();
            }
            catch (Exception ex)
            {
                await PopupNavigation.Instance.PopAsync();
            }
            

        }


        async void BtnCancel_Clicked(System.Object sender, System.EventArgs e)
        {
            await PopupNavigation.Instance.PopAsync();
        }
    }
}

