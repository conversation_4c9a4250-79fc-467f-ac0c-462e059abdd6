﻿using Acr.UserDialogs;
using DrMuscle.Constants;
using DrMuscle.Helpers;
using DrMuscle.Layout;
using DrMuscle.Resx;
using DrMuscle.Screens.User.OnBoarding;
using DrMuscle.Views;
using DrMuscleWebApiSharedModel;
using Plugin.Connectivity;
using Plugin.GoogleClient.Shared;
using Plugin.GoogleClient;
using Sentry.Protocol;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Essentials;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using static System.Net.Mime.MediaTypeNames;
using Application = Xamarin.Forms.Application;
using Browser = Xamarin.Essentials.Browser;
using DrMuscle.Entity;
using System.Globalization;
using Rg.Plugins.Popup.Services;
using System.Threading;
using DrMuscle.Dependencies;
using Device = Xamarin.Forms.Device;
using DrMuscle.Services;
using Sentry;

namespace DrMuscle.Screens.User
{
    public partial class RegistrationPage : DrMusclePage
    {
        private bool isPasswordVisible = false;
        private bool isRequestInProgress = false;
        private IGoogleClientManager _googleClientManager;
        IFacebookManager _manager;
        private IAppleSignInService appleSignInService;
        public RegistrationPage()
        {
            InitializeComponent();
            ClearFormValues();
            SetUIAccordingToScreenSizes();
            _googleClientManager = CrossGoogleClient.Current;
            appleSignInService = DependencyService.Get<IAppleSignInService>();
            //HasSlideMenu = false;
        }

        private void SetUIAccordingToScreenSizes()
        {
            if (App.ScreenWidth > 375)
            {
                LblHeader1.FontSize = 22;
                LblHeader2.FontSize = 19;
                LblHeader3.FontSize = 19;
                LblOr.FontSize = 16;
            }
        }
        private void ClearFormValues()
        {
            EmailEntry.Text = "";
            PasswordEntry.Text = "";
            EmailValidator.IsVisible = false;
            PasswordValidator.IsVisible = false;
        }
        protected override void OnAppearing()
        {
            base.OnAppearing();
            ClearFormValues();
            CancelNotification();
        }
        private void CancelNotification()
        {
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1651);
        }
        public override void OnBeforeShow()
        {
            base.OnBeforeShow();
        }
        private async void Login_btn_clicked(object sender, EventArgs e)
        {
            ((App)Application.Current).displayCreateNewAccount = true;
            try
            {
                WelcomePage page = new WelcomePage();
                page.OnBeforeShow();
                await Navigation.PushAsync(page);
            }
            catch (Exception ex)
            {

            }
        }

        private void TermsClicked(object sender, EventArgs e)
        {
            Browser.OpenAsync("http://drmuscleapp.com/news/terms/", BrowserLaunchMode.SystemPreferred);
        }

        private void PrivacyClicked(object sender, EventArgs e)
        {
            Browser.OpenAsync("http://drmuscleapp.com/news/privacy/", BrowserLaunchMode.SystemPreferred);
        }

        private async void CreateAccountByEmail(object sender, EventArgs e)
        {
            if (!CrossConnectivity.Current.IsConnected)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = AppResources.PleaseCheckInternetConnection,
                    Title = AppResources.ConnectionError
                });
                return;
            }
            if (await DataValidation())
            {
                    App.IsNewUser = true;
                    LocalDBManager.Instance.SetDBSetting("email", EmailEntry.Text);
                    LocalDBManager.Instance.SetDBSetting("LoginType", "Email");
                    LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");
                    LocalDBManager.Instance.SetDBSetting("isAccountCreatedInBackground", "false");
                    App.IsIntroBack = true;
                    // Navigate to the next page
                    MainOnboardingPage page = new MainOnboardingPage();
                    page.OnBeforeShow();
                    Navigation.PushAsync(page);
                    
                
            }
        }

        private async Task<bool> DataValidation()
        {
            try
            {
                EmailValidator.IsVisible = false;
                PasswordValidator.IsVisible = false;
                if (string.IsNullOrEmpty(EmailEntry.Text) && string.IsNullOrEmpty(PasswordEntry.Text))
                {
                    EmailValidator.IsVisible = true;
                    EmailValidator.Text = AppResources.EnterYourEmail;
                    PasswordValidator.IsVisible = true;
                    PasswordValidator.Text = "Enter your password";
                    await Task.Delay(1000);
                    EmailValidator.IsVisible = false;
                    PasswordValidator.IsVisible = false;
                    return false;
                }
                else if (string.IsNullOrEmpty(EmailEntry.Text))
                {
                    EmailValidator.IsVisible = true;
                    EmailValidator.Text = AppResources.EnterYourEmail;
                    await Task.Delay(1000);
                    EmailValidator.IsVisible = false;
                    return false;
                }
                else if (string.IsNullOrEmpty(PasswordEntry.Text))
                {
                    PasswordValidator.IsVisible = true;
                    PasswordValidator.Text = "Enter your password";
                    await Task.Delay(1000);
                    PasswordValidator.IsVisible = false;
                    return false;
                }
                else
                {
                    bool isEmailValid = CheckEmailValidity(EmailEntry.Text);
                    bool isPasswordValid = CheckPasswordValidity(PasswordEntry.Text);
                    if (isEmailValid && isPasswordValid)
                        return true;
                    else
                        return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private bool CheckPasswordValidity(string text)
        {
            try
            {
                if (text.Length < 6)
                {
                    PasswordValidator.IsVisible = true;
                    PasswordValidator.Text = "At least 6 characters";
                    return false;
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("password", text);
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private bool CheckEmailValidity(string email)
        {
            var text = email;
            if (!string.IsNullOrEmpty(email) && text.Contains("@"))
            {
                var newEmail = email.Substring(0, email.IndexOf('@'));
                if (newEmail.Length == 1)
                    text = $"a{text}";
            }
            if (!Emails.ValidateEmail(text))
            {
                EmailValidator.IsVisible = true;
                EmailValidator.Text = AppResources.InvalidEmailError;
                return false;
            }
            if (email.Contains("#") || email.Contains("%") || email.Contains("{") || email.Contains("}") || email.Contains("(") || email.Contains("}") || email.Contains("$") || email.Contains("^") || email.Contains("&") || email.Contains("=") || email.Contains("`") || email.Contains("'") || email.Contains("\"") || email.Contains(",") || email.Contains("?") || email.Contains("/") || email.Contains("\\") || email.Contains("<") || email.Contains(">") || email.Contains(":") || email.Contains(";") || email.Contains("|") || email.Contains("[") || email.Contains("]") || email.Contains("*") || email.Contains("*") || email.Contains("!") || email.Contains("~") || email.Count(t => t == '@') > 1)
            {
                EmailValidator.IsVisible = true;
                EmailValidator.Text = AppResources.InvalidEmailError;
                return false;
            }
            try
            {
                var domain = email.Substring(email.IndexOf('@'));
                var extension = email.Substring(email.IndexOf('.') + 1).ToUpper();
                if (domain.Contains("gnail") || domain.Contains("gmaill") || domain.Contains(".cam"))
                {
                    EmailValidator.IsVisible = true;
                    EmailValidator.Text = AppResources.InvalidEmailError;
                    return false;
                }
                else
                {
                    EmailValidator.IsVisible = false;
                    EmailValidator.Text = "";
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
            return false;
        }

        private async Task<bool> CheckEmailExist(string email)
        {
            BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExistWithoutLoader(new IsEmailAlreadyExistModel() { email = email });
            if (existingUser != null)
            {
                if (existingUser.Result)
                {

                    //try
                    //{
                    //    if (firstnameDisposible != null)
                    //        firstnameDisposible.Dispose();
                    //    if (passwordDisposible != null)
                    //        passwordDisposible.Dispose();

                    //}
                    //catch (Exception ex)
                    //{

                    //}
                    //finally
                    //{
                    //    await Task.Delay(500);
                    //}
                    ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                    {
                        Title = "Email already in use",
                        Message = "Use another email or log into your existing account.",
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = "Use another email",
                        CancelText = AppResources.LogIn,

                    };
                    var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                    if (actionOk)
                    {
                        //GetEmail();
                    }
                    else
                    {
                        ((App)Xamarin.Forms.Application.Current).displayCreateNewAccount = true;
                        WelcomePage page = new WelcomePage();
                        page.OnBeforeShow();
                        await Navigation.PushAsync(page);
                    }
                    
                    return true;
                }
                else
                {
                    return false;
                }

            }
            else
            {
                return false;
            }


        }
        async void CreateAccountBeforeDemoButton_Clicked()
        {
            //try
            //{
                // Check if a request is already in progress
                
                //if (!CrossConnectivity.Current.IsConnected)
                //{
                //    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                //    {
                //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //        Message = AppResources.PleaseCheckInternetConnection,
                //        Title = AppResources.ConnectionError
                //    });
                //    return;
                //}
                

                //LocalDBManager.Instance.SetDBSetting("LoginType", "Email");

                //int? workoutId = null;
                //int? programId = null;
                //int? remainingWorkout = null;
                //var WorkoutInfo2 = "";
                //Setup Program

                //SignUp here
                //RegisterModel registerModel = new RegisterModel();

                //registerModel.Firstname = LocalDBManager.Instance.GetDBSetting("firstname").Value;

                //Revert it
                //string FirstName = "";
                //try
                //{
                //    if (EmailEntry.Text.Contains("@"))
                //    {
                //        string[] parts = EmailEntry.Text.Split('@');
                //        FirstName = parts[0];
                //    }
                //}
                //catch (Exception ex)
                //{
                //    FirstName = "";
                //}
                //LocalDBManager.Instance.SetDBSetting("firstname", FirstName);
                ////Revert it

               

                
                    // registerModel.Firstname = FirstName;
                    //registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email").Value;
                    //registerModel.MassUnit = "lb";
                    //registerModel.BodyWeight = new MultiUnityWeight(150, "lb");
                    //registerModel.Password = LocalDBManager.Instance.GetDBSetting("password").Value;
                    //registerModel.ConfirmPassword = LocalDBManager.Instance.GetDBSetting("password").Value;

                    // Continue with UI-related tasks on the main thread
                    //Device.BeginInvokeOnMainThread(() =>
                    //{
                    //    // Your existing code for setting LocalDBManager.Instance values
                    //    LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");
                    //    App.IsIntroBack = true;
                    //    // Navigate to the next page
                    //    MainOnboardingPage page = new MainOnboardingPage();
                    //    page.OnBeforeShow();
                    //    Navigation.PushAsync(page);
                    //});
                    
            //try
            //{

            //    BooleanModel registerResponse = await DrMuscleRestClient.Instance.RegisterUserBeforeDemo(registerModel);
            //    if (registerResponse.Result)
            //    {
            //        DependencyService.Get<IFirebase>().LogEvent("account_created", "");
            //    }
            //}
            //catch (Exception ex)
            //{

            //}
            //Login
            //LoginSuccessResult lr = await DrMuscleRestClient.Instance.LoginWithoutLoader(new LoginModel()
            //{
            //    Username = registerModel.EmailAddress,
            //    Password = registerModel.Password
            //});

            //if (lr != null && !string.IsNullOrEmpty(lr.access_token))
            //{
            //    DateTime current = DateTime.Now;

            //    UserInfosModel uim = await DrMuscleRestClient.Instance.GetUserInfoWithoutLoader();

            //    LocalDBManager.Instance.SetDBSetting("email", uim.Email);
            //    LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
            //    LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
            //    //LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
            //    LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
            //    LocalDBManager.Instance.SetDBSetting("password", registerModel.Password);
            //    LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
            //    LocalDBManager.Instance.SetDBSetting("token_expires_date", current.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
            //    LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
            //    LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
            //    LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
            //    LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
            //    LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
            //    LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
            //    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            //    LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
            //    LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false");                //if (uim.ReminderTime != null)

            //    if (uim.WarmupsValue != null)
            //    {
            //        LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
            //    }
            //    if (uim.Increments != null)
            //        LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
            //    if (uim.Max != null)
            //        LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
            //    if (uim.Min != null)
            //        LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
            //    if (uim.BodyWeight != null)
            //    {
            //        LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
            //    }
            //    if (uim.WeightGoal != null)
            //    {
            //        LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
            //    }
            //    LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");
            //    //await AccountCreatedPopup();
            //    //SetUpRestOnboarding();

            //    // New code
            //    App.IsIntroBack = true;
            //    // Navigation.PopModalAsync(false);
            //    //await PagesFactory.PopThenPushAsync<RegistrationPage>(true);
            //    //var page = PagesFactory.GetPage<MainOnboardingPage>();
            //    //page.OnBeforeShow();
            //    MainOnboardingPage page = new MainOnboardingPage();
            //    page.OnBeforeShow();
            //    Navigation.PushAsync(page);
            //}
            //else
            //{
            //    UserDialogs.Instance.Alert(new AlertConfig()
            //    {
            //        Message = AppResources.EmailAndPasswordDoNotMatch,
            //        Title = AppResources.UnableToLogIn,
            //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            //    });
            //}
        }
        private async void CreateAccountByGmail(object sender, EventArgs e)
        {
            if (!CrossConnectivity.Current.IsConnected)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = AppResources.PleaseCheckInternetConnection,
                    Title = AppResources.ConnectionError
                });
                //await UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    Message = AppResources.PleaseCheckInternetConnection,
                //    Title = AppResources.ConnectionError
                //});
                return;
            }
            _googleClientManager.OnLogin += OnLoginCompleted;
            try
            {
                await _googleClientManager.LoginAsync();
            }
            catch (GoogleClientSignInNetworkErrorException ex)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = ex.Message,
                    Title = AppResources.Error,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
            }
            catch (GoogleClientSignInCanceledErrorException ex)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = ex.Message,
                    Title = AppResources.Error,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
            }
            catch (GoogleClientSignInInvalidAccountErrorException ex)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = ex.Message,
                    Title = AppResources.Error,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
            }
            catch (GoogleClientSignInInternalErrorException ex)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = ex.Message,
                    Title = AppResources.Error,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
            }
            catch (GoogleClientNotInitializedErrorException ex)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = ex.Message,
                    Title = AppResources.Error,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
            }
            catch (GoogleClientBaseException ex)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    Message = ex.Message,
                    Title = AppResources.Error,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
            }

        }
        private async void OnLoginCompleted(object sender, GoogleClientResultEventArgs<GoogleUser> loginEventArgs)
        {
            _googleClientManager.OnLogin -= OnLoginCompleted;
            if (loginEventArgs.Data != null)
            {
                GoogleUser googleUser = loginEventArgs.Data;
                UserProfile user = new UserProfile();
                googleUser.Name =!string.IsNullOrEmpty(googleUser.Name)? ((googleUser.Name.Contains(' ')) ? googleUser.Name.Split(' ')[0] : googleUser.Name) : googleUser.Name ;
                user.Name = googleUser.Name;
                user.Email = googleUser.Email;
                if (user.Picture != null)
                    user.Picture = googleUser.Picture;
                //var token = CrossGoogleClient.Current.ActiveToken;
                LocalDBManager.Instance.SetDBSetting("LoginType", "Social");
                LocalDBManager.Instance.SetDBSetting("GToken", "");
                if (user.Picture != null)
                    LocalDBManager.Instance.SetDBSetting("ProfilePic", user.Picture.OriginalString);

                //IsLoggedIn = true;
                bool IsExistingUser = false;
                BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExist(new IsEmailAlreadyExistModel() { email = user.Email });
                if (existingUser != null)
                {
                    if (existingUser.Result)
                    {
                        LoginWithoutPopup(googleUser);
                        return;
                        //ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                        //{
                        //    Title = "You are already registered",
                        //    Message = "Use another account or log into your existing account.",
                        //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //    OkText = "Use another account",
                        //    CancelText = AppResources.LogIn,

                        //};
                        //var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                        //if (actionOk)
                        //{
                        //    return;
                        //}
                        //else
                        //{
                        //    IsExistingUser = true;
                        //    //((App)Application.Current).displayCreateNewAccount = true;
                        //    //await PagesFactory.PushAsync<WelcomePage>();
                        //}

                        ////return;
                    }

                }


                string mass = null;
                if(LocalDBManager.Instance.GetDBSetting("massunit") != null)
                    mass = LocalDBManager.Instance.GetDBSetting("massunit").Value;
                string body = null;
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                    body = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture), mass).Kg.ToString();
                else
                    body = "60";


                LoginSuccessResult lr = await DrMuscleRestClient.Instance.GoogleLogin("", user.Email, user.Name, body, mass);
                if (lr != null)
                {
                    UserInfosModel uim = null;
                    if (existingUser.Result)
                    {
                        uim = await DrMuscleRestClient.Instance.GetUserInfo();
                    }
                    else
                    {
                        //RegisterModel registerModel = new RegisterModel();
                        //registerModel.Firstname = user.Name;
                        //registerModel.EmailAddress = user.Email;
                        //registerModel.SelectedGender = LocalDBManager.Instance.GetDBSetting("gender").Value;
                        //registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value;
                        //if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
                        //    registerModel.IsQuickMode = false;
                        //else
                        //{
                        //    if (LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "null")
                        //        registerModel.IsQuickMode = null;
                        //    else
                        //        registerModel.IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false;
                        //}
                        //if (LocalDBManager.Instance.GetDBSetting("Age") != null)
                        //    registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);
                        //registerModel.RepsMinimum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsminimum").Value);
                        //registerModel.RepsMaximum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsmaximum").Value);
                        //registerModel.Password = "";
                        //registerModel.ConfirmPassword = "";
                        //registerModel.LearnMoreDetails = learnMore;
                        //registerModel.IsHumanSupport = IsHumanSupport;
                        //registerModel.IsCardio = IsIncludeCardio;
                        //registerModel.BodyPartPrioriy = bodypartName;

                        //registerModel.MainGoal = mainGoal;
                        //if (IsEquipment)
                        //{
                        //    registerModel.EquipmentModel = new EquipmentModel()
                        //    {
                        //        IsEquipmentEnabled = true,
                        //        IsDumbbellEnabled = isDumbbells,
                        //        IsPlateEnabled = IsPlates,
                        //        IsPullyEnabled = IsPully,
                        //        IsChinUpBarEnabled = IsChinupBar,

                        //    };
                        //}
                        //if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                        //    registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture), "kg");
                        RegisterModel registerModel = new RegisterModel();
                        registerModel.Firstname = user.Name;
                        registerModel.EmailAddress = user.Email;
                        LocalDBManager.Instance.SetDBSetting("email", user.Email);
                        LocalDBManager.Instance.SetDBSetting("firstname", user.Name);
                        registerModel.MassUnit =(LocalDBManager.Instance.GetDBSetting("massunit") != null)? LocalDBManager.Instance.GetDBSetting("massunit").Value : null;
                        registerModel.Password = "";
                        registerModel.ConfirmPassword = "";
                        if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                            registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture), "kg");
                        if (LocalDBManager.Instance.GetDBSetting("WeightGoal") != null)
                            registerModel.WeightGoal = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value, CultureInfo.InvariantCulture), "kg");

                        //await DrMuscleRestClient.Instance.RegisterUserBeforeDemo(registerModel);
                        DependencyService.Get<IFirebase>().LogEvent("account_created", "");
                        LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                        LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                        await AccountCreatedPopup();
                        //SetUpRestOnboarding();
                        LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");

                        MainOnboardingPage page = new MainOnboardingPage();
                        page.OnBeforeShow();
                        Navigation.PushAsync(page);
                    }
                    try
                    {
                        //LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                        //LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
                        LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                        LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                        LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                        LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                        LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                        LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                        LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                        LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                        LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                        LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                        LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
                        LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
                        LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
                        if (uim.Age != null)
                            LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                        if (uim.TargetIntake != null && uim.TargetIntake != 0)
                            LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
                        //if (uim.ReminderTime != null)
                        //    LocalDBManager.Instance.SetDBSetting("ReminderTime", uim.ReminderTime.ToString());
                        //if (uim.ReminderDays != null)
                        //    LocalDBManager.Instance.SetDBSetting("ReminderDays", uim.ReminderDays);

                        LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                        LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");

                        LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
                        if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                        {
                            LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                            LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                        }
                        if (uim.Increments != null)
                            LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                        if (uim.Max != null)
                            LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                        if (uim.Min != null)
                            LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                        if (uim.BodyWeight != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                        }
                        if (uim.WeightGoal != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                        }
                        if (uim.WarmupsValue != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                        }

                        if (uim.EquipmentModel != null)
                        {
                            LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting("Equipment", "false");
                            LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
                            LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                            LocalDBManager.Instance.SetDBSetting("Plate", "true");
                            LocalDBManager.Instance.SetDBSetting("Pully", "true");
                        }
                        if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                            LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                        else
                            LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

                        ((App)Application.Current).displayCreateNewAccount = true;

                        if (uim.Gender.Trim().ToLowerInvariant().Equals("man"))
                            LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Background2.png");
                        else
                            LocalDBManager.Instance.SetDBSetting("BackgroundImage", "BackgroundFemale.png");

                        if (IsExistingUser)
                        {
                            App.IsDemoProgress = false;
                            LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                            await PagesFactory.PopToRootAsync(true);
                            return;
                        }
                        await AccountCreatedPopup();
                        //SetUpRestOnboarding();
                        LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");

                        MainOnboardingPage page = new MainOnboardingPage();
                        page.OnBeforeShow();
                        Navigation.PushAsync(page);
                        // CancelNotification();
                    }
                    catch (Exception ex)
                    {

                    }
                }
                else
                {
                    UserDialogs.Instance.Alert(new AlertConfig()
                    {
                        Message = AppResources.EmailAndPasswordDoNotMatch,
                        Title = AppResources.UnableToLogIn,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    });
                }
            }
            else
            {
                UserDialogs.Instance.Alert(new AlertConfig()
                {
                    Message = loginEventArgs.Message,
                    Title = AppResources.Error,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });

            }

            _googleClientManager.OnLogin -= OnLoginCompleted;

        }
        public async Task LoginWithoutPopup(GoogleUser googleUser)
        {
            LoginSuccessResult lr = await DrMuscleRestClient.Instance.GoogleLogin("", googleUser.Email, googleUser.Name, null, null);
            if (lr != null)
            {
                UserInfosModel uim = null;

                uim = await DrMuscleRestClient.Instance.GetUserInfo();
                CancelNotification();
                try
                {
                    SentrySdk.ConfigureScope(scope =>
                    {
                        scope.User = new Sentry.User
                        {
                            Email = LocalDBManager.Instance.GetDBSetting("email")?.Value
                        };
                    });

                    LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                    LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
                    LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                    LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                    LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                    LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                    LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                    LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                    LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                    LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                    LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                    LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                    LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false");
                    LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
                    LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
                    LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");

                    LocalDBManager.Instance.SetDBSetting("DailyReset", Convert.ToString(uim.DailyExerciseCount));
                    LocalDBManager.Instance.SetDBSetting("WeeklyReset", Convert.ToString(uim.WeeklyExerciseCount));

                    LocalDBManager.Instance.SetDBSetting("IsEmailReminder", uim.IsReminderEmail ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("ReminderHours", uim.ReminderBeforeHours.ToString());
                    if (uim.ReminderTime != null)
                        LocalDBManager.Instance.SetDBSetting("ReminderTime", uim.ReminderTime.ToString());
                    if (uim.ReminderDays != null)
                        LocalDBManager.Instance.SetDBSetting("ReminderDays", uim.ReminderDays);
                    if (uim.Age != null)
                        LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                    LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                    LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                    LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Reminder5th", uim.IsReminder ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", uim.LastWorkoutWas);
                    LocalDBManager.Instance.SetDBSetting("IsMobility", uim.IsMobility == null ? null : uim.IsMobility == false ? "false" : "true");
                    LocalDBManager.Instance.SetDBSetting("MaxWorkoutDuration", uim.WorkoutDuration.ToString());
                    LocalDBManager.Instance.SetDBSetting("IsExerciseQuickMode", uim.IsExerciseQuickMode == null ? null : uim.IsExerciseQuickMode == false ? "false" : "true");
                    LocalDBManager.Instance.SetDBSetting("MobilityLevel", uim.MobilityLevel);
                    LocalDBManager.Instance.SetDBSetting("MobilityRep", uim.MobilityRep == null ? "" : Convert.ToString(uim.MobilityRep));
                    SetupEquipment(uim);
                    if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                    else
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

                    if (uim.IsPyramid)
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                        LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
                    }
                    else if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                        LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                        LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                    }
                    if (uim.Increments != null)
                        LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                    if (uim.Max != null)
                        LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                    if (uim.Min != null)
                        LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                    if (uim.BodyWeight != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                    }
                    if (uim.WeightGoal != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                    }
                    if (uim.WarmupsValue != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                    }
                    if (uim.SetCount != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("WorkSetCount", Convert.ToString(uim.SetCount));
                    }

                ((App)Application.Current).displayCreateNewAccount = true;

                    if (uim.Height != null)
                        LocalDBManager.Instance.SetDBSetting("Height", uim.Height.ToString());
                    if (uim.TargetIntake != null)
                        LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
                    LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("RecommendedReminder", uim.IsRecommendedReminder == true ? "true" : uim.IsRecommendedReminder == null ? "null" : "false");
                    await PagesFactory.PopToRootAsync(true);
                    App.RegisterDeviceToken();
                    MessagingCenter.Send(this, "BackgroundImageUpdated");
                    //await PagesFactory.PushAsync<MainAIPage>();


                }
                catch (Exception ex)
                {

                }
                try
                {
                    DateTime creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                    if ((DateTime.Now.ToUniversalTime() - creationDate).TotalDays < 14)
                    {
                        LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                        App.IsV1UserTrial = true;
                    }
                }
                catch (Exception ex)
                {

                }
            }
            else
            {
                UserDialogs.Instance.Alert(new AlertConfig()
                {
                    Message = AppResources.EmailAndPasswordDoNotMatch,
                    Title = AppResources.UnableToLogIn,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
            }
        }
        private void SetupEquipment(UserInfosModel uim)
        {
            LocalDBManager.Instance.SetDBSetting("KgBarWeight", uim.KgBarWeight == null ? "20" : Convert.ToString(uim.KgBarWeight).ReplaceWithDot());
            LocalDBManager.Instance.SetDBSetting("LbBarWeight", uim.LbBarWeight == null ? "45" : Convert.ToString(uim.LbBarWeight).ReplaceWithDot());
            if (uim.EquipmentModel != null)
            {
                LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("Bands", uim.EquipmentModel.IsBands ? "true" : "false");

                LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", uim.EquipmentModel.IsHomeEquipmentEnabled ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("HomeChinUp", uim.EquipmentModel.IsHomeChinupBar ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("HomeDumbbell", uim.EquipmentModel.IsHomeDumbbell ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("HomePlate", uim.EquipmentModel.IsHomePlate ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("HomePully", uim.EquipmentModel.IsHomePully ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("HomeBands", uim.EquipmentModel.IsHomeBands ? "true" : "false");

                LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", uim.EquipmentModel.IsOtherEquipmentEnabled ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("OtherChinUp", uim.EquipmentModel.IsOtherChinupBar ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("OtherDumbbell", uim.EquipmentModel.IsOtherDumbbell ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("OtherPlate", uim.EquipmentModel.IsOtherPlate ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("OtherPully", uim.EquipmentModel.IsOtherPully ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("OtherBands", uim.EquipmentModel.IsOtherBands ? "true" : "false");

                if (uim.EquipmentModel.Active == "gym")
                    LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");
                if (uim.EquipmentModel.Active == "home")
                    LocalDBManager.Instance.SetDBSetting("HomeEquipment", "true");
                if (uim.EquipmentModel.Active == "other")
                    LocalDBManager.Instance.SetDBSetting("OtherEquipment", "true");

                if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilableDumbbell))
                {
                    LocalDBManager.Instance.SetDBSetting("DumbbellKg", uim.EquipmentModel.AvilableDumbbell);
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", uim.EquipmentModel.AvilableHomeDumbbell);
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", uim.EquipmentModel.AvilableOtherDumbbell);

                    LocalDBManager.Instance.SetDBSetting("DumbbellLb", uim.EquipmentModel.AvilableLbDumbbell);
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", uim.EquipmentModel.AvilableHomeLbDumbbell);
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", uim.EquipmentModel.AvilableHomeLbDumbbell);
                }
                else
                {
                    var kgString = "50_2_True|47.5_2_True|45_2_True|42.5_2_True|40_2_True|37.5_2_True|35_2_True|32.5_2_True|30_2_True|27.5_2_True|25_2_True|22.5_2_True|20_2_True|17.5_2_True|15_2_True|12.5_2_True|10_2_True|7.5_2_True|5_2_True|2.5_2_True|1_2_True";
                    LocalDBManager.Instance.SetDBSetting("DumbbellKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", kgString);

                    var lbString = "90_2_True|85_2_True|80_2_True|75_2_True|70_2_True|65_2_True|60_2_True|55_2_True|50_2_True|45_2_True|40_2_True|35_2_True|30_2_True|25_2_True|20_2_True|15_2_True|12_2_True|10_2_True|8_2_True|5_2_True|3_2_True|2_2_True";
                    LocalDBManager.Instance.SetDBSetting("DumbbellLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", lbString);
                }
                if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilablePlate))
                {
                    LocalDBManager.Instance.SetDBSetting("PlatesKg", uim.EquipmentModel.AvilablePlate);
                    LocalDBManager.Instance.SetDBSetting("HomePlatesKg", uim.EquipmentModel.AvilableHomePlate);
                    LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", uim.EquipmentModel.AvilableOtherPlate);

                    LocalDBManager.Instance.SetDBSetting("PlatesLb", uim.EquipmentModel.AvilableLbPlate);
                    LocalDBManager.Instance.SetDBSetting("HomePlatesLb", uim.EquipmentModel.AvilableHomeLbPlate);
                    LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", uim.EquipmentModel.AvilableHomeLbPlate);
                }
                else
                {
                    var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                    LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("HomePlatesKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", kgString);

                    var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                    LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("HomePlatesLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", lbString);
                }

                if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilablePulley))
                {

                    LocalDBManager.Instance.SetDBSetting("PulleyKg", uim.EquipmentModel.AvilablePulley);
                    LocalDBManager.Instance.SetDBSetting("HomePulleyKg", uim.EquipmentModel.AvilableHomePulley);
                    LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", uim.EquipmentModel.AvilableOtherPulley);


                    LocalDBManager.Instance.SetDBSetting("PulleyLb", uim.EquipmentModel.AvilableLbPulley);
                    LocalDBManager.Instance.SetDBSetting("HomePulleyLb", uim.EquipmentModel.AvilableHomeLbPulley);
                    LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", uim.EquipmentModel.AvilableOtherLbPulley);
                }
                else
                {

                    var kgString = "5_20_True|1.5_2_True";
                    var lbString = "10_20_True|5_2_True|2.5_2_True";

                    LocalDBManager.Instance.SetDBSetting("PulleyKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("HomePulleyKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", kgString);


                    LocalDBManager.Instance.SetDBSetting("PulleyLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("HomePulleyLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", lbString);
                }

                if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilableBands))
                {

                    LocalDBManager.Instance.SetDBSetting("BandsKg", uim.EquipmentModel.AvilableBands);
                    LocalDBManager.Instance.SetDBSetting("HomeBandsKg", uim.EquipmentModel.AvilableHomeBands);
                    LocalDBManager.Instance.SetDBSetting("OtherBandsKg", uim.EquipmentModel.AvilableOtherBands);


                    LocalDBManager.Instance.SetDBSetting("BandsLb", uim.EquipmentModel.AvilableLbBands);
                    LocalDBManager.Instance.SetDBSetting("HomeBandsLb", uim.EquipmentModel.AvilableHomeLbBands);
                    LocalDBManager.Instance.SetDBSetting("OtherBandsLb", uim.EquipmentModel.AvilableOtherLbBands);
                }
                else
                {

                    var kgString = "Black_40_2_True|Blue_30_2_True|Green_20_2_True|Red_10_2_True|Yellow_4_2_True";
                    var lbString = "Black_90_2_True|Blue_65_2_True|Green_45_2_True|Red_25_2_True|Yellow_10_2_True";

                    LocalDBManager.Instance.SetDBSetting("BandsKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("HomeBandsKg", kgString);
                    LocalDBManager.Instance.SetDBSetting("OtherBandsKg", kgString);

                    LocalDBManager.Instance.SetDBSetting("BandsLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("HomeBandsLb", lbString);
                    LocalDBManager.Instance.SetDBSetting("OtherBandsLb", lbString);
                }
            }
            else
            {
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                LocalDBManager.Instance.SetDBSetting("Plate", "true");
                LocalDBManager.Instance.SetDBSetting("Pully", "true");
                LocalDBManager.Instance.SetDBSetting("Bands", "true");

                LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "false");
                LocalDBManager.Instance.SetDBSetting("HomeChinUp", "true");
                LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");
                LocalDBManager.Instance.SetDBSetting("HomePlate", "true");
                LocalDBManager.Instance.SetDBSetting("HomePully", "true");
                LocalDBManager.Instance.SetDBSetting("HomeBands", "true");

                LocalDBManager.Instance.SetDBSetting("OtherEquipment", "false");
                LocalDBManager.Instance.SetDBSetting("OtherChinUp", "true");
                LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");
                LocalDBManager.Instance.SetDBSetting("OtherPlate", "true");
                LocalDBManager.Instance.SetDBSetting("OtherPully", "true");
                LocalDBManager.Instance.SetDBSetting("OtherBands", "true");

            }


        }
        private async Task AccountCreatedPopup()
        {
            var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
            var modalPage = new Views.GeneralPopup("TrueState.png", "Success!", "Account created", "Customize program");
            modalPage.Disappearing += (sender2, e2) =>
            {
                waitHandle.Set();
            };
            await PopupNavigation.Instance.PushAsync(modalPage);

            await Task.Run(() => waitHandle.WaitOne());

        }
        private async void CreateAccountByFacebook(object sender, EventArgs e)
        {
            _manager = DependencyService.Get<IFacebookManager>();
            if (!CrossConnectivity.Current.IsConnected)
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = AppResources.PleaseCheckInternetConnection,
                    Title = AppResources.ConnectionError
                });
                return;
            }
            FacebookUser result = await _manager.Login();
            if (result == null)
            {
                UserDialogs.Instance.Alert(new AlertConfig()
                {
                    Message = AppResources.AnErrorOccursWhenSigningIn,
                    Title = AppResources.UnableToLogIn,
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                });
                return;
            }

            Device.BeginInvokeOnMainThread(async () =>
            {
                await WelcomePage_OnFBLoginSucceded(result.Id, result.Email, "", result.Token, !string.IsNullOrEmpty(result.FirstName) ? result.FirstName.Contains(' ') ? result.FirstName.Split(' ')[0] : result.FirstName: result.FirstName);
            });
        }
        private async Task WelcomePage_OnFBLoginSucceded(string FBId, string FBEmail, string FBGender, string FBToken, string firstname)
        {
            if (string.IsNullOrEmpty(FBEmail))
            {
                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = "Your Facebook account is not connected with email (or we do not have permission to access it). Please sign up with email.",
                    Title = AppResources.Error
                });

                return;
            }
            LocalDBManager.Instance.SetDBSetting("LoginType", "Social");
            LocalDBManager.Instance.SetDBSetting("FBId", FBId);
            LocalDBManager.Instance.SetDBSetting("FBEmail", FBEmail);
            LocalDBManager.Instance.SetDBSetting("firstname", firstname);
            LocalDBManager.Instance.SetDBSetting("FBGender", FBGender);
            LocalDBManager.Instance.SetDBSetting("FBToken", FBToken);
            var url = $"http://graph.facebook.com/{FBId}/picture?type=square";
            LocalDBManager.Instance.SetDBSetting("ProfilePic", url);



            BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExist(new IsEmailAlreadyExistModel() { email = FBEmail });
            bool IsExistingUser = false;
            if (existingUser != null)
            {
                if (existingUser.Result)
                {

                    ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                    {
                        Title = "You are already registered",
                        Message = "Use another account or log into your existing account.",
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        OkText = "Use another account",
                        CancelText = AppResources.LogIn,

                    };
                    var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                    if (actionOk)
                    {
                        return;
                    }
                    else
                    {
                        //((App)Application.Current).displayCreateNewAccount = true;
                        //await PagesFactory.PushAsync<WelcomePage>();
                        IsExistingUser = true;
                    }

                    //return;
                }

            }
            //Log in d'un compte existant avec Facebook
            string mass = "lb";
            string body = null;
            body = new MultiUnityWeight(150, "lb").Kg.ToString();
            try
            {


                LoginSuccessResult lr = await DrMuscleRestClient.Instance.FacebookLogin(FBToken, body, mass);
                if (lr != null)
                {
                    DateTime current = DateTime.Now;
                    UserInfosModel uim = null;
                    if (existingUser.Result)
                    {
                        uim = await DrMuscleRestClient.Instance.GetUserInfo();
                    }
                    else
                    {
                        RegisterModel registerModel = new RegisterModel();
                        registerModel.Firstname = firstname;
                        registerModel.EmailAddress = FBEmail;

                        registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value;

                        if (LocalDBManager.Instance.GetDBSetting("Age") != null)
                            registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);

                        registerModel.BodyWeight = new MultiUnityWeight(150, "lb");
                        registerModel.Password = "";
                        registerModel.ConfirmPassword = "";

                        //await DrMuscleRestClient.Instance.RegisterUserBeforeDemo(registerModel);
                        await AccountCreatedPopup();
                        //SetUpRestOnboarding();
                        LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");
                        DependencyService.Get<IFirebase>().LogEvent("account_created", "");
                        //New Code
                        MainOnboardingPage page1 = new MainOnboardingPage();
                        page1.OnBeforeShow();
                        Navigation.PushAsync(page1);
                        //New Code
                        return;
                    }
                    LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                    if (!string.IsNullOrEmpty(uim.Firstname))
                        LocalDBManager.Instance.SetDBSetting("firstname",!string.IsNullOrEmpty(uim.Firstname)? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname): uim.Firstname);
                    LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                    LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                    LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                    LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                    LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                    LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                    LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                    LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                    LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                    LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                    LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
                    LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
                    LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
                    if (uim.Age != null)
                        LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                    if (uim.TargetIntake != null && uim.TargetIntake != 0)
                        LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
                    //if (uim.ReminderTime != null)
                    //    LocalDBManager.Instance.SetDBSetting("ReminderTime", uim.ReminderTime.ToString());
                    //if (uim.ReminderDays != null)
                    //    LocalDBManager.Instance.SetDBSetting("ReminderDays", uim.ReminderDays);

                    LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                    LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                    LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");

                    LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
                    if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                        LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                        LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                    }
                    if (uim.Increments != null)
                        LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                    if (uim.Max != null)
                        LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                    if (uim.Min != null)
                        LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                    if (uim.BodyWeight != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                    }
                    if (uim.WeightGoal != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                    }
                    if (uim.WarmupsValue != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                    }
                    if (uim.EquipmentModel != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
                        LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("Equipment", "false");
                        LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
                        LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                        LocalDBManager.Instance.SetDBSetting("Plate", "true");
                        LocalDBManager.Instance.SetDBSetting("Pully", "true");
                    }
                    ((App)Application.Current).displayCreateNewAccount = true;

                    if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                    else
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

                    //await PagesFactory.PopToRootAsync(true);
                    //await PagesFactory.PushAsync<MainAIPage>();
                    //    App.IsWelcomeBack = true;
                    //    App.IsDemoProgress = false;
                    //LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                    //await PopupNavigation.Instance.PushAsync(new ReminderPopup());
                    if (IsExistingUser)
                    {
                        App.IsDemoProgress = false;
                        LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                        await PagesFactory.PopToRootAsync(true);
                        return;
                    }
                    await AccountCreatedPopup();
                    //SetUpRestOnboarding();
                    LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");

                    MainOnboardingPage page = new MainOnboardingPage();
                    page.OnBeforeShow();
                    Navigation.PushAsync(page);
                }
                else
                {
                    UserDialogs.Instance.Alert(new AlertConfig()
                    {
                        Message = AppResources.EmailAndPasswordDoNotMatch,
                        Title = AppResources.UnableToLogIn,
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    });
                }
            }
            catch (Exception ex)
            {

                await UserDialogs.Instance.AlertAsync(new AlertConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    Message = "We are facing problem to signup with your facebook account. Please sign up with email.",
                    Title = AppResources.Error
                });

            }
            //if (string.IsNullOrEmpty(FBEmail))
            //{
            //    await UserDialogs.Instance.AlertAsync(new AlertConfig()
            //    {
            //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //        Message = "Your Facebook account is not connected with email (or we do not have permission to access it). Please sign up with email.",
            //        Title = AppResources.Error
            //    });

            //    return;
            //}
            //LocalDBManager.Instance.SetDBSetting("LoginType", "Social");
            //LocalDBManager.Instance.SetDBSetting("FBId", FBId);
            //LocalDBManager.Instance.SetDBSetting("FBEmail", FBEmail);
            //LocalDBManager.Instance.SetDBSetting("FBGender", FBGender);
            //LocalDBManager.Instance.SetDBSetting("FBToken", FBToken);
            //var url = $"http://graph.facebook.com/{FBId}/picture?type=square";
            //LocalDBManager.Instance.SetDBSetting("ProfilePic", url);



            //BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExist(new IsEmailAlreadyExistModel() { email = FBEmail });
            //bool IsExistingUser = false;
            //if (existingUser != null)
            //{
            //    if (existingUser.Result)
            //    {

            //        ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
            //        {
            //            Title = "You are already registered",
            //            Message = "Use another account or log into your existing account.",
            //            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //            OkText = "Use another account",
            //            CancelText = AppResources.LogIn,

            //        };
            //        var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
            //        if (actionOk)
            //        {
            //            return;
            //        }
            //        else
            //        {
            //            //((App)Application.Current).displayCreateNewAccount = true;
            //            //await PagesFactory.PushAsync<WelcomePage>();
            //            IsExistingUser = true;
            //        }

            //        //return;
            //    }

            //}
            ////Log in d'un compte existant avec Facebook
            //string mass = LocalDBManager.Instance.GetDBSetting("massunit").Value;
            //string body = null;
            //if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            //    body = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture), "kg").Kg.ToString();
            //try
            //{


            //    LoginSuccessResult lr = await DrMuscleRestClient.Instance.FacebookLogin(FBToken, body, mass);
            //    if (lr != null)
            //    {
            //        DateTime current = DateTime.Now;
            //        UserInfosModel uim = null;
            //        if (existingUser.Result)
            //        {
            //            uim = await DrMuscleRestClient.Instance.GetUserInfo();
            //        }
            //        else
            //        {
            //            RegisterModel registerModel = new RegisterModel();
            //            registerModel.Firstname = firstname;
            //            registerModel.EmailAddress = FBEmail;
            //            registerModel.SelectedGender = LocalDBManager.Instance.GetDBSetting("gender").Value;
            //            registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value;
            //            if (LocalDBManager.Instance.GetDBSetting("QuickMode") == null)
            //                registerModel.IsQuickMode = false;
            //            else
            //            {
            //                if (LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "null")
            //                    registerModel.IsQuickMode = null;
            //                else
            //                    registerModel.IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true" ? true : false;
            //            }
            //            if (LocalDBManager.Instance.GetDBSetting("Age") != null)
            //                registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);
            //            registerModel.RepsMinimum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsminimum").Value);
            //            registerModel.RepsMaximum = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("repsmaximum").Value);
            //            if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            //                registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture), "kg");
            //            registerModel.Password = "";
            //            registerModel.ConfirmPassword = "";
            //            registerModel.LearnMoreDetails = learnMore;
            //            registerModel.IsHumanSupport = IsHumanSupport;
            //            registerModel.IsCardio = IsIncludeCardio;
            //            registerModel.BodyPartPrioriy = bodypartName;
            //            registerModel.SetStyle = SetStyle;
            //            if (IncrementUnit != null)
            //                registerModel.Increments = IncrementUnit.Kg;
            //            registerModel.MainGoal = mainGoal;
            //            if (IsEquipment)
            //            {
            //                var model = new EquipmentModel();

            //                if (LocalDBManager.Instance.GetDBSetting("workout_place")?.Value == "gym")
            //                {
            //                    model.IsEquipmentEnabled = true;
            //                    model.IsDumbbellEnabled = isDumbbells;
            //                    model.IsPlateEnabled = IsPlates;
            //                    model.IsPullyEnabled = IsPully;
            //                    model.IsChinUpBarEnabled = IsChinupBar;
            //                    model.Active = "gym";
            //                }
            //                else
            //                {
            //                    model.IsHomeEquipmentEnabled = true;
            //                    model.IsHomeDumbbell = isDumbbells;
            //                    model.IsHomePlate = IsPlates;
            //                    model.IsHomePully = IsPully;
            //                    model.IsHomeChinupBar = IsChinupBar;
            //                    model.Active = "home";
            //                }
            //                registerModel.EquipmentModel = model;

            //            }
            //            //RegisterModel registerModel = new RegisterModel();
            //            //registerModel.Firstname = firstname;
            //            //registerModel.EmailAddress = FBEmail;

            //            //registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value;

            //            //if (LocalDBManager.Instance.GetDBSetting("Age") != null)
            //            //    registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);
            //            //if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
            //            //    registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture), "kg");
            //            //registerModel.Password = "";
            //            //registerModel.ConfirmPassword = "";

            //            uim = await DrMuscleRestClient.Instance.RegisterWithUser(registerModel);
            //            try
            //            {
            //                DBSetting experienceSetting = LocalDBManager.Instance.GetDBSetting("experience");
            //                DBSetting workoutPlaceSetting = LocalDBManager.Instance.GetDBSetting("workout_place");

            //                var level = 0;
            //                if (LocalDBManager.Instance.GetDBSetting("MainLevel") != null)
            //                    level = int.Parse(LocalDBManager.Instance.GetDBSetting("MainLevel").Value);
            //                bool isSplit = LocalDBManager.Instance.GetDBSetting("MainProgram").Value.Contains("Split");
            //                bool isGym = workoutPlaceSetting?.Value == "gym";
            //                var mo = AppThemeConstants.GetLevelProgram(level, isGym, !isSplit);
            //                if (workoutPlaceSetting?.Value == "homeBodyweightOnly")
            //                {
            //                    if (LocalDBManager.Instance.GetDBSetting("CustomMainLevel") != null && LocalDBManager.Instance.GetDBSetting("CustomMainLevel")?.Value == "1")
            //                    {
            //                        mo.workoutName = "Bodyweight 1";
            //                        mo.workoutid = 12645;
            //                        mo.programid = 487;
            //                        mo.reqWorkout = 12;
            //                        mo.programName = "Bodyweight Level 1";
            //                    }
            //                    else if (level <= 1)
            //                    {
            //                        mo.workoutName = "Bodyweight 2";
            //                        mo.workoutid = 12646;
            //                        mo.programid = 488;
            //                        mo.reqWorkout = 12;
            //                        mo.programName = "Bodyweight Level 2";
            //                    }
            //                    else if (level == 2)
            //                    {
            //                        mo.workoutName = "Bodyweight 2";
            //                        mo.workoutid = 12646;
            //                        mo.programid = 488;
            //                        mo.reqWorkout = 12;
            //                        mo.programName = "Bodyweight Level 2";
            //                    }
            //                    else if (level == 3)
            //                    {
            //                        mo.workoutName = "Bodyweight 3";
            //                        mo.workoutid = 14017;
            //                        mo.programid = 923;
            //                        mo.reqWorkout = 15;
            //                        mo.programName = "Bodyweight Level 3";
            //                    }
            //                    else if (level >= 4)
            //                    {
            //                        mo.workoutName = "Bodyweight 4";
            //                        mo.workoutid = 14019;
            //                        mo.programid = 924;
            //                        mo.reqWorkout = 15;
            //                        mo.programName = "Bodyweight Level 4";
            //                    }

            //                }
            //                LocalDBManager.Instance.SetDBSetting("recommendedWorkoutId", mo.workoutid.ToString());
            //                LocalDBManager.Instance.SetDBSetting("recommendedWorkoutLabel", mo.workoutName);
            //                LocalDBManager.Instance.SetDBSetting("recommendedProgramId", mo.programid.ToString());
            //                LocalDBManager.Instance.SetDBSetting("recommendedRemainingWorkout", mo.reqWorkout.ToString());

            //                LocalDBManager.Instance.SetDBSetting("recommendedProgramLabel", mo.programName);


            //            }
            //            catch (Exception ex)
            //            {

            //            }
            //        }
            //        LocalDBManager.Instance.SetDBSetting("email", uim.Email);
            //        LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
            //        LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
            //        LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
            //        LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
            //        LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
            //        LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
            //        LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
            //        LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
            //        LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
            //        LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
            //        LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
            //        LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
            //        LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
            //        if (uim.Age != null)
            //            LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
            //        //if (uim.ReminderTime != null)
            //        //    LocalDBManager.Instance.SetDBSetting("ReminderTime", uim.ReminderTime.ToString());
            //        //if (uim.ReminderDays != null)
            //        //    LocalDBManager.Instance.SetDBSetting("ReminderDays", uim.ReminderDays);

            //        LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
            //        LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
            //        LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
            //        LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
            //        LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
            //        LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
            //        LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
            //        LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");

            //        LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
            //        if (uim.IsNormalSet == null || uim.IsNormalSet == true)
            //        {
            //            LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
            //            LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
            //        }
            //        else
            //        {
            //            LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
            //            LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
            //        }
            //        if (uim.Increments != null)
            //            LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
            //        if (uim.Max != null)
            //            LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
            //        if (uim.Min != null)
            //            LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
            //        if (uim.BodyWeight != null)
            //        {
            //            LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
            //        }
            //        if (uim.WarmupsValue != null)
            //        {
            //            LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
            //        }
            //        SetupEquipment(uim);
            //        ((App)Application.Current).displayCreateNewAccount = true;

            //        if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
            //            LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
            //        else
            //            LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

            //        //await PagesFactory.PopToRootAsync(true);
            //        //await PagesFactory.PushAsync<MainAIPage>();
            //        //    App.IsWelcomeBack = true;
            //        //    App.IsDemoProgress = false;
            //        //LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
            //        //await PopupNavigation.Instance.PushAsync(new ReminderPopup());
            //        if (IsExistingUser)
            //        {
            //            App.IsNUX = false;
            //            App.IsDemoProgress = false;
            //            LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
            //            await PagesFactory.PopToRootAsync(true);
            //            return;
            //        }
            //        if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
            //            LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
            //            LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
            //            LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
            //            LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
            //        {
            //            try
            //            {
            //                long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId").Value);
            //                long pId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId").Value);
            //                var upi = new GetUserProgramInfoResponseModel()
            //                {
            //                    NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value },
            //                    RecommendedProgram = new WorkoutTemplateGroupModel() { Id = pId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout").Value) },
            //                };
            //                if (upi != null)
            //                {
            //                    WorkoutTemplateModel nextWorkout = upi.NextWorkoutTemplate;
            //                    if (upi.NextWorkoutTemplate.Exercises == null || upi.NextWorkoutTemplate.Exercises.Count() == 0)
            //                    {
            //                        try
            //                        {
            //                            nextWorkout = await DrMuscleRestClient.Instance.GetUserCustomizedCurrentWorkout(workoutTemplateId);
            //                            //nextWorkout = w.Workouts.First(ww => ww.Id == upi.NextWorkoutTemplate.Id);
            //                        }
            //                        catch (Exception ex)
            //                        {
            //                            await UserDialogs.Instance.AlertAsync(new AlertConfig()
            //                            {
            //                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //                                Message = AppResources.PleaseCheckInternetConnection,
            //                                Title = AppResources.ConnectionError
            //                            });
            //                            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            //                            //{
            //                            //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //                            //    Message = AppResources.PleaseCheckInternetConnection,
            //                            //    Title = AppResources.ConnectionError
            //                            //});
            //                            return;
            //                        }

            //                    }
            //                    App.IsNUX = false;
            //                    if (nextWorkout != null)
            //                    {
            //                        CurrentLog.Instance.CurrentWorkoutTemplate = nextWorkout;
            //                        CurrentLog.Instance.WorkoutTemplateCurrentExercise = nextWorkout.Exercises.First();
            //                        CurrentLog.Instance.WorkoutStarted = true;
            //                        if (Device.RuntimePlatform.Equals(Device.Android))
            //                        {
            //                            await PagesFactory.PopToRootThenPushAsync<KenkoDemoWorkoutExercisePage>(true);
            //                            App.IsDemoProgress = false;
            //                            App.IsWelcomeBack = true;
            //                            App.IsNewUser = true;
            //                            LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
            //                            CurrentLog.Instance.Exercise1RM.Clear();
            //                            //await PopupNavigation.Instance.PushAsync(new ReminderPopup());
            //                            Device.BeginInvokeOnMainThread(async () =>
            //                            {
            //                                await PagesFactory.PopToRootAsync(true);
            //                            });
            //                            MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage(), "SignupFinishMessage");
            //                        }
            //                        else
            //                        {

            //                            App.IsDemoProgress = false;
            //                            App.IsWelcomeBack = true;
            //                            App.IsNewUser = true;
            //                            LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
            //                            CurrentLog.Instance.Exercise1RM.Clear();
            //                            //await PopupNavigation.Instance.PushAsync(new ReminderPopup());
            //                            await PagesFactory.PopToRootMoveAsync(true);
            //                            await PagesFactory.PushMoveAsync<KenkoDemoWorkoutExercisePage>();
            //                            MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage(), "SignupFinishMessage");
            //                        }

            //                    }
            //                    else
            //                    {
            //                        await PagesFactory.PopToRootAsync(true);
            //                        App.IsDemoProgress = false;
            //                        App.IsWelcomeBack = true;
            //                        App.IsNewUser = true;
            //                        LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");

            //                    }

            //                }
            //            }
            //            catch (Exception ex)
            //            {

            //            }

            //        }
            //    }
            //    else
            //    {
            //        UserDialogs.Instance.Alert(new AlertConfig()
            //        {
            //            Message = AppResources.EmailAndPasswordDoNotMatch,
            //            Title = AppResources.UnableToLogIn,
            //            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            //        });
            //    }
            //}
            //catch (Exception ex)
            //{

            //    await UserDialogs.Instance.AlertAsync(new AlertConfig()
            //    {
            //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //        Message = "We are facing problem to signup with your facebook account. Please sign up with email.",
            //        Title = AppResources.Error
            //    });

            //}
        }

        private async void CreateAccountByApple(object sender, EventArgs e)
        {
            //OnLoginCompleted(null, new GoogleClientResultEventArgs<GoogleUser>(new GoogleUser() { Email = "nazim.appleid.com", Name = "" }, GoogleActionStatus.Completed));
            //OnLoginCompleted(null, new GoogleClientResultEventArgs<GoogleUser>(new GoogleUser() { Email = "<EMAIL>", Name = "" }, GoogleActionStatus.Completed));
            //return;
            var account = await appleSignInService.SignInAsync();
            if (account != null)
            {
                if (!string.IsNullOrEmpty(account.Email))
                {
                    await SecureStorage.SetAsync("Email", account.Email);
                    if (!string.IsNullOrEmpty(account.Name))
                    {
                        account.Name =!string.IsNullOrEmpty(account.Name)? ( (account.Name.Contains(' ')) ? account.Name.Split(' ')[0] : account.Name) : account.Name;
                        await SecureStorage.SetAsync("Name", account.Name);
                    }
                    else if (!string.IsNullOrEmpty(account.GivenName))
                        await SecureStorage.SetAsync("Name", account.GivenName);
                    else if (!string.IsNullOrEmpty(account.FamilyName))
                        await SecureStorage.SetAsync("Name", account.FamilyName);
                    else
                        await SecureStorage.SetAsync("Name", "  ");
                }
                else
                {
                    string email = await SecureStorage.GetAsync("Email");
                    string name = await SecureStorage.GetAsync("Name");
                    account.Email = email;
                    account.Name = name;
                }
                if (string.IsNullOrEmpty(account.Email))
                {
                    await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    {
                        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        Message = "We haven't get email. Please login with email.",
                        Title = AppResources.Error
                    });

                    return;
                }
                OnLoginCompleted(null, new GoogleClientResultEventArgs<GoogleUser>(new GoogleUser() { Email = account.Email, Name = account.Name }, GoogleActionStatus.Completed));
            }
        }

        private void EmailTextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                var text = e.NewTextValue as string;
                if (!string.IsNullOrEmpty(text))
                {
                    EmailValidator.IsVisible = false;
                }
            }
            catch (Exception ex)
            {
                EmailValidator.IsVisible = false;
            }
        }

        private void PasswordTextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                var text = e.NewTextValue as string;
                if (!string.IsNullOrEmpty(text))
                {
                    if (text.Length < 6)
                    {
                        PasswordValidator.IsVisible = true;
                        PasswordValidator.Text = "At least 6 characters";
                    }
                    else
                    {
                        PasswordValidator.IsVisible = false;
                    }
                }
                else
                {
                    PasswordValidator.IsVisible = false;
                }
            }
            catch (Exception ex)
            {

                throw;
            }

        }
    }
}