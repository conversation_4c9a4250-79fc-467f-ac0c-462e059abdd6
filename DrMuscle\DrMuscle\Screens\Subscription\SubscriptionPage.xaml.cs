﻿using DrMuscle;
using DrMuscle.Dependencies;
using DrMuscle.Helpers;
using DrMuscle.Screens.Exercises;
using DrMuscleWebApiSharedModel;
using System;
using Xamarin.Forms;
using DrMuscle.Layout;
using DrMuscle.Message;
using DrMuscle.Resx;
using DrMuscle.Screens.User;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Plugin.Connectivity;
using Acr.UserDialogs;
using System.ComponentModel;
using System.Reflection;
using Xamarin.Essentials;
using DrMuscle.Utility;

namespace DrMuscle.Screens.Subscription
{
    public partial class SubscriptionPage : DrMusclePage
    {
        public List<ReviewsModel> userReviewList = new List<ReviewsModel>();
        public List<ReviewsModel> expertReviewList = new List<ReviewsModel>();
        public ObservableCollection<BotModel> BotList = new ObservableCollection<BotModel>();

        public SubscriptionPage()
        {
            InitializeComponent();
            //lstChats.ItemsSource = BotList;
            userReviewList = GetReviews();
            expertReviewList = GetExpertsReviews();

            //MessagingCenter.Subscribe<SubscriptionModel>(this, "SubscriptionPurchaseMessage", (obj) =>
            //{
            //    if (obj == null)
            //        return;
            //    UpdateSubscriptionData(obj);
            //});

            //MessagingCenter.Subscribe<SubscriptionModel>(this, "SubscriptionPurchaseIfNotExistMessage", (obj) =>
            //{
            //    if (obj == null)
            //        return;
            //    AddSubscriptionDataIfNotExist(obj);
            //});

            SupportEmail.IsVisible = true;
            EmailSupportButton.Clicked += async (object sender, EventArgs e) =>
            {
                await HelperClass.SendMail("Subscription question");
            };
            EmailSupportButton2.Clicked += async (object sender, EventArgs e) =>
            {
                await HelperClass.SendMail("Subscription question");
            };
            EmailSupportButton3.Clicked += async (object sender, EventArgs e) =>
            {
                await HelperClass.SendMail("Subscription question");
            };

            var tapLinkTermsOfUseGestureRecognizer = new TapGestureRecognizer();
            tapLinkTermsOfUseGestureRecognizer.NumberOfTapsRequired = 1;
            tapLinkTermsOfUseGestureRecognizer.Tapped += (s, e) =>
            {
                Device.OpenUri(new Uri("http://drmuscleapp.com/news/terms/"));
            };

            TermsOfUse.GestureRecognizers.Add(tapLinkTermsOfUseGestureRecognizer);

            var tapLinkPrivacyPolicyGestureRecognizer = new TapGestureRecognizer();
            tapLinkPrivacyPolicyGestureRecognizer.NumberOfTapsRequired = 1;
            tapLinkPrivacyPolicyGestureRecognizer.Tapped += (s, e) =>
            {
                Device.OpenUri(new Uri("http://drmuscleapp.com/news/privacy/"));
            };

            PrivacyPolicy.GestureRecognizers.Add(tapLinkPrivacyPolicyGestureRecognizer);
            SignUpLabelLine3.IsVisible = true;
            SignUpLabelLine4.IsVisible = true;
            switch (Device.RuntimePlatform)
            {
                case Device.iOS:
                    RestorePurchaseButton.IsVisible = true;
                    RestorePurchaseButton.Clicked += async (sender, e) => {
                        if (!CrossConnectivity.Current.IsConnected)
                        {
                            await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            {
                                Message = AppResources.PleaseCheckInternetConnection,
                                Title = AppResources.ConnectionError,
                                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                                OkText = "Try again"
                            });
                        }
                        DependencyService.Get<IDrMuscleSubcription>().RestorePurchases();
                    };
                    break;
                
            }
            RefreshLocalized();

            BuyMonthlyAccessButton.Clicked += BuyMonthlyAccessButton_Clicked;
            BuyYearlyAccessButton.Clicked += BuyYearlyAccessButton_Clicked;
            Title = "Subscription";
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) => {
                RefreshLocalized();
            });

            DependencyService.Get<IDrMuscleSubcription>().OnMealPlanAccessPurchased += async delegate
            {
                SignUpLabelLineMealPlan.Text = "You are subscribed to the meal plan add-on";
                BuyMealplanAddon.Text = "You already have access to meal plans";
                App.IsMealPlan = true;
            };
        }

        private void RefreshLocalized()
        {
            //SignUpLabelLine1.Text = "Get a smart coach 24/7 for less than";// AppResources.SignUpToContinueUsing;
            SignUpLabelLine2.Text = "Try it for a month—it's worth it 💪";// AppResources.DrMuscleAfterYourFreeTrial;
            GridTips.IsVisible = true;

            GridTips1.IsVisible = true;
            GridTips2.IsVisible = true;
            //if (Device.RuntimePlatform.Equals(Device.Android))
                LblTooExpensive2.IsVisible = true;
            ImgJonus.IsVisible = true;
            FrmUserReview.IsVisible = true;
            FrmMKJUserReview.IsVisible = true;
            FrmPoteroUserReview.IsVisible = true;
            LblMoreUserReview.IsVisible = true;
            LblTheTestla.IsVisible = true;
            ImgBrandLogo.IsVisible = true;
            FrmExepertReview.IsVisible = true;
            ImgArtin.IsVisible = true;
           // LblExpertReview.IsVisible = true;
            FrmExepertReviewJonny.IsVisible = true;
            SignUpLabelHeading.IsVisible = true;
            switch (Device.RuntimePlatform)
            {
                case Device.iOS:
                    SignUpLabelLine3.Text = AppResources.OnceYouConfirmYourSubscriptionPurchase;
                    SignUpLabelLine4.Text = AppResources.OnceYourSubscriptionIsActiveYourITunesAccountWill;
                    break;
                case Device.Android:
                    SignUpLabelLine3.Text = AppResources.ByTappingContinueYourPaymentWillBeChargedToYourGooglePlayAccount;
                    SignUpLabelLine4.Text = AppResources.YourSubscriptionWillRenewAutomatically;
                    break;
            }
            EmailSupportButton.Text = "Email us, we reply in 1 day";
            EmailSupportButton2.Text = EmailSupportButton.Text;
            EmailSupportButton3.Text = EmailSupportButton.Text;
            RestorePurchaseButton.Text = AppResources.RestorePurchase;
            TermsOfUse.Text = AppResources.TermsOfUse;
            PrivacyPolicy.Text = AppResources.PrivacyPolicy;

        }

        public override async void OnBeforeShow()
        {
            base.OnBeforeShow();

            BuyMonthlyAccessButton.IsVisible = false;
            BuyYearlyAccessButton.IsVisible = false;
            SupportEmail2.IsVisible = false;
            SupportEmail3.IsVisible = false;
            GridTips.IsVisible = true;
            SignUpLabelHeading.IsVisible = true;
            //SignUpLabelLine1.IsVisible = true;
            SignUpLabelLine2.IsVisible = true;
            LblTooExpensive.IsVisible = true;
            SupportEmail.Margin = new Thickness(20, 0);
            TermsPrivacyStack.IsVisible = true;
            LblRead5Star.IsVisible = true;
            // SignUpLabelLine1.Text = "Get a smart coach 24/7 for less than";// AppResources.SignUpToContinueUsing;
            SignUpLabelLine2.Text = "Try it for a month—it's worth it 💪";// AppResources.DrMuscleAfterYourFreeTrial;

            DependencyService.Get<IFirebase>().SetScreenName("subscription_page");
            BuyMonthlyAccessButton.Clicked -= BuyMonthlyAccessButton_Access_Clicked;
            BooleanModel m = await DrMuscleRestClient.Instance.IsV1User();
            bool isLifetime = false;
            bool isTraining = false;
            bool isMealPlan = false;
            if (m != null)
            {
                isLifetime = m.Result;
                if (!isTraining)
                isTraining = m.IsTraining;
                if (!isMealPlan)
                isMealPlan = m.IsMealPlan;
            }
            if (DependencyService.Get<IDrMuscleSubcription>().IsActiveSubscriptions())
            {
                App.IsV1User = true;
                App.IsFreePlan = false;
                App.IsTraining = true;
                if (!isLifetime)
                    isLifetime = true;
                isTraining = true;
                LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                
            }
            if (DependencyService.Get<IDrMuscleSubcription>().IsActiveMealPlan())
            {
                App.IsMealPlan = true;
                
                isMealPlan = true;
                LocalDBManager.Instance.SetDBSetting("IsMealPlanPurchased", "true");
            }

            if (DependencyService.Get<IDrMuscleSubcription>().IsMonthlyAccessPuchased())
            {
                CurrentLog.Instance.IsMonthlyUser = true;
            }
            if (DependencyService.Get<IDrMuscleSubcription>().IsYearlyAccessPuchased())
                CurrentLog.Instance.IsMonthlyUser = false;
            if (isLifetime)
            {
                if (App.IsMealPlan)
                    isMealPlan = true;
                //isTraining = false;
                //isMealPlan = false;
                if (CurrentLog.Instance.IsMonthlyUser == null)
                {
                    var result = await DrMuscleRestClient.Instance.IsMonthlyUser();
                    if (result != null)
                        CurrentLog.Instance.IsMonthlyUser = result.Result;
                }
                

                if (CurrentLog.Instance.IsMonthlyUser == true)
                {
                    SetMonthlyActiveView();
                }
                else
                {

                    GridTips.IsVisible = false;
                    SignUpLabelHeading.IsVisible = false;
                    BuyMonthlyAccessButton.IsVisible = true;
                    LblTooExpensive.IsVisible = false;
                    BuyMonthlyAccessButton.Text = AppResources.YouAlreadyHaveAccess;
                    //SignUpLabelLine1.IsVisible = false;
                    SignUpLabelLine2.Text = AppResources.ThankYou;

                    GridTips1.IsVisible = false;
                    GridTips2.IsVisible = false;
                    LblTooExpensive2.IsVisible = false;
                    ImgJonus.IsVisible = false;
                    FrmUserReview.IsVisible = false;
                    FrmMKJUserReview.IsVisible = false;
                    FrmPoteroUserReview.IsVisible = false;
                    LblMoreUserReview.IsVisible = false;
                    LblTheTestla.IsVisible = false;
                    ImgBrandLogo.IsVisible = false;
                    FrmExepertReview.IsVisible = false;
                    FrmExepertReviewJonny.IsVisible = false;
                    ImgArtin.IsVisible = false;
                    SignUpYearly.IsVisible = false;
                    //LblExpertReview.IsVisible = false;
                    BuyMonthlyAccessButton.Clicked -= BuyMonthlyAccessButton_Access_Clicked;
                    BuyMonthlyAccessButton.Clicked += BuyMonthlyAccessButton_Access_Clicked;
                }
                SignUpLabelLineMealPlan.Text = "You are subscribed to the meal plan add-on";

            }
            else if (DependencyService.Get<IDrMuscleSubcription>().IsMonthlyAccessPuchased())
            {
                isTraining = true;
                CurrentLog.Instance.IsMonthlyUser = true;
                
                SetMonthlyActiveView();
            }

            else if (DependencyService.Get<IDrMuscleSubcription>().IsYearlyAccessPuchased())
            {
                //GridTips.IsVisible = false;
                //SignUpLabelHeading.IsVisible = false;
                //LblTooExpensive.IsVisible = false;
                //SignUpLabelLine2.Text = AppResources.ThankYou;
                //BuyMonthlyAccessButton.IsVisible = true;
                //BuyMonthlyAccessButton.Text = AppResources.YouAlreadyHaveAccess;
                //BuyMonthlyAccessButton.Clicked += async (object sender, EventArgs e) =>
                //{
                //    await PagesFactory.PushAsync<MainAIPage>();
                //};

                //SignUpYearly.IsVisible = false;
                //GridTips1.IsVisible = false;
                //GridTips2.IsVisible = false;
                //LblTooExpensive2.IsVisible = false;
                //ImgJonus.IsVisible = false;
                //FrmUserReview.IsVisible = false;
                //FrmMKJUserReview.IsVisible = false;
                //FrmPoteroUserReview.IsVisible = false;
                //LblMoreUserReview.IsVisible = false;
                //LblTheTestla.IsVisible = false;
                //ImgBrandLogo.IsVisible = false;
                //FrmExepertReview.IsVisible = false;
                //FrmExepertReviewJonny.IsVisible = false;
                //ImgArtin.IsVisible = false;

                //HideTermsOfUseLables();

                isTraining = true;
                CurrentLog.Instance.IsMonthlyUser = false;
            }
            else
            {
                DependencyService.Get<IDrMuscleSubcription>().OnMonthlyAccessPurchased += async delegate {
                    if (Device.RuntimePlatform == Device.Android)
                    {
                        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                    }
                    else
                        await PagesFactory.PushAsync<MainAIPage>(); MessagingCenter.Send<SubscriptionSuccessfulMessage>(new SubscriptionSuccessfulMessage(), "SubscriptionSuccessfulMessage");
                };
                DependencyService.Get<IDrMuscleSubcription>().OnYearlyAccessPurchased += async delegate {
                    if (Device.RuntimePlatform == Device.Android)
                    {
                        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                    }
                    else
                        await PagesFactory.PushAsync<MainAIPage>();
                    MessagingCenter.Send<SubscriptionSuccessfulMessage>(new SubscriptionSuccessfulMessage(), "SubscriptionSuccessfulMessage");
                };

                //SignUpMonthly.IsVisible = true;
                SignUpYearly.IsVisible = true;
                BuyMonthlyAccessButton.IsVisible = true;
                BuyYearlyAccessButton.IsVisible = true;
                LblTooExpensive.IsVisible = true;
               
                //var monthPrice = await DependencyService.Get<IDrMuscleSubcription>().GetMonthlyPrice();
                //SignUpMonthly.Text = $"Try it for {monthPrice} for 1 month, then pay monthly:";

                GridTips1.IsVisible = true;
                GridTips2.IsVisible = true;
                //if (Device.RuntimePlatform.Equals(Device.Android))
                    LblTooExpensive2.IsVisible = true;
                ImgJonus.IsVisible = true;
                FrmUserReview.IsVisible = true;
                FrmMKJUserReview.IsVisible = true;
                FrmPoteroUserReview.IsVisible = true;
                LblMoreUserReview.IsVisible = true;
                LblTheTestla.IsVisible = true;
                ImgBrandLogo.IsVisible = true;
                FrmExepertReview.IsVisible = true;
                FrmExepertReviewJonny.IsVisible = true;
                ImgArtin.IsVisible = true;
                SignUpLabelHeading.IsVisible = true;
            }
            RestorePurchaseMealplan.IsVisible = true;
            //if (isMealPlan)
            //{
                
            //    SignUpLabelLineMealPlan.IsVisible = false;
            //    BuyMealplanAddon.Text = "You already have access to meal plans";
            //    BuyMealplanAddon.Clicked += async (object sender, EventArgs e) =>
            //        {
                        
            //                await PagesFactory.PushAsync<MainAIPage>();
                        
            //        };
            //}
            //else
            //{
            //    BuyMealplanAddon.Text = $"{await DependencyService.Get<IDrMuscleSubcription>().GetMealPlanLabel()}";
            //    RestorePurchaseMealplan.IsVisible = true;
            //    SignUpLabelLineMealPlan.IsVisible = true;
            //    BuyMealplanAddon.Clicked += async (object sender, EventArgs e) =>
            //    {
            //        await DependencyService.Get<IDrMuscleSubcription>().BuyMealPlanAccess();
            //    };
            //}

            //SignUpMonthly.IsVisible = true;
            SignUpYearly.IsVisible = true;
            BuyMonthlyAccessButton.IsVisible = true;
            BuyYearlyAccessButton.IsVisible = true;
            LblTooExpensive.IsVisible = true;
            var monthlyText = await DependencyService.Get<IDrMuscleSubcription>().GetMonthlyButtonLabel();
            monthlyText = monthlyText.Replace("Sign up monthly", AppResources.SignUpMonthly);
            monthlyText = monthlyText.Replace("month", AppResources.Month);
            BuyMonthlyAccessButton.Text = monthlyText;

            //var monthPrice = await DependencyService.Get<IDrMuscleSubcription>().GetMonthlyPrice();
            //SignUpMonthly.Text = $"Try it for {monthPrice} for 1 month, then pay monthly:";

            var yearlyText = await DependencyService.Get<IDrMuscleSubcription>().GetYearlyButtonLabel();
            yearlyText = yearlyText.Replace("year", AppResources.Year);
            yearlyText = yearlyText.Replace("Sign up annual", AppResources.SignUpAnnual);

            var yearPrice = await DependencyService.Get<IDrMuscleSubcription>().GetYearlyPrice();
            SignUpYearly.Text = $"Or save on the annual plan";

            BuyYearlyAccessButton.Text = yearlyText;

            GridTips1.IsVisible = true;
            GridTips2.IsVisible = true;
            //if (Device.RuntimePlatform.Equals(Device.Android))
            LblTooExpensive2.IsVisible = true;
            ImgJonus.IsVisible = true;
            FrmUserReview.IsVisible = true;
            FrmMKJUserReview.IsVisible = true;
            FrmPoteroUserReview.IsVisible = true;
            LblMoreUserReview.IsVisible = true;
            LblTheTestla.IsVisible = true;
            ImgBrandLogo.IsVisible = true;
            FrmExepertReview.IsVisible = true;
            FrmExepertReviewJonny.IsVisible = true;
            ImgArtin.IsVisible = true;
            SignUpLabelHeading.IsVisible = true;

            BuyMealplanAddon.Text = $"{await DependencyService.Get<IDrMuscleSubcription>().GetMealPlanLabel()}";
            RestorePurchaseMealplan.IsVisible = true;
            SignUpLabelLineMealPlan.IsVisible = true;
            
            if (!isMealPlan && !isTraining)
            {
                DependencyService.Get<IDrMuscleSubcription>().OnMonthlyAccessPurchased += async delegate {
                    if (Device.RuntimePlatform == Device.Android)
                    {
                        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                    }
                    else
                        await PagesFactory.PushAsync<MainAIPage>(); MessagingCenter.Send<SubscriptionSuccessfulMessage>(new SubscriptionSuccessfulMessage(), "SubscriptionSuccessfulMessage");
                };
                DependencyService.Get<IDrMuscleSubcription>().OnYearlyAccessPurchased += async delegate {
                    if (Device.RuntimePlatform == Device.Android)
                    {
                        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                    }
                    else
                        await PagesFactory.PushAsync<MainAIPage>();
                    MessagingCenter.Send<SubscriptionSuccessfulMessage>(new SubscriptionSuccessfulMessage(), "SubscriptionSuccessfulMessage");
                };

                //SignUpMonthly.IsVisible = true;
                SignUpYearly.IsVisible = true;
                BuyMonthlyAccessButton.IsVisible = true;
                BuyYearlyAccessButton.IsVisible = true;
                LblTooExpensive.IsVisible = true;

                LblTooExpensive2.IsVisible = false;
                SupportEmail.IsVisible = true;
                //var monthPrice = await DependencyService.Get<IDrMuscleSubcription>().GetMonthlyPrice();
                //SignUpMonthly.Text = $"Try it for {monthPrice} for 1 month, then pay monthly:";

              
                BuyYearlyAccessButton.Text = yearlyText;
                GridTips.IsVisible = true;
                GridTips1.IsVisible = true;
                GridTips2.IsVisible = true;
                //if (Device.RuntimePlatform.Equals(Device.Android))
                LblTooExpensive2.IsVisible = true;
                ImgJonus.IsVisible = true;
                FrmUserReview.IsVisible = true;
                FrmMKJUserReview.IsVisible = true;
                FrmPoteroUserReview.IsVisible = true;
                LblMoreUserReview.IsVisible = true;
                LblTheTestla.IsVisible = true;
                ImgBrandLogo.IsVisible = true;
                FrmExepertReview.IsVisible = true;
                FrmExepertReviewJonny.IsVisible = true;
                ImgArtin.IsVisible = true;
                SignUpLabelHeading.IsVisible = true;

                BuyMealplanAddon.Text = $"{await DependencyService.Get<IDrMuscleSubcription>().GetMealPlanLabel()}";
                RestorePurchaseMealplan.IsVisible = true;
                SignUpLabelLineMealPlan.IsVisible = true;
                BuyMealplanAddon.Clicked += async (object sender, EventArgs e) =>
                {
                    if (BuyMealplanAddon.Text == "You already have access to meal plans")
                    {
                        if (Device.RuntimePlatform == Device.Android)
                        {
                            await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                        }
                        else
                            await PagesFactory.PushAsync<MainAIPage>();
                        App.IsMealPlan = true;
                    }
                    else
                    {
                        await DependencyService.Get<IDrMuscleSubcription>().BuyMealPlanAccess();
                    }
                    
                };
                SignUpLabelLine2.Text = "Try it for a month—it's worth it 💪";
                SignUpLabelLineMealPlan.Text = "Get unlimited meal plans (add-on)";
            }
            else if (isTraining && !isMealPlan)
            {
                if (CurrentLog.Instance.IsMonthlyUser == null)
                {
                    var result = await DrMuscleRestClient.Instance.IsMonthlyUser();
                    if (result != null)
                        CurrentLog.Instance.IsMonthlyUser = result.Result;
                }

                if ((bool)CurrentLog.Instance.IsMonthlyUser)
                {
                    SetMonthlyActiveView();
                }
                else
                {
                    GridTips.IsVisible = true;
                    GridTips1.IsVisible = true;
                    GridTips2.IsVisible = true;
                    //SignUpLabelHeading.IsVisible = false;
                    //LblTooExpensive.IsVisible = false;
                    //SignUpLabelLine2.Text = AppResources.ThankYou;
                    BuyMonthlyAccessButton.IsVisible = true;
                    BuyMonthlyAccessButton.Text = "You already have access";
                    BuyMonthlyAccessButton.Clicked -= BuyMonthlyAccessButton_Access_Clicked;
                    BuyMonthlyAccessButton.Clicked += BuyMonthlyAccessButton_Access_Clicked;
                    SignUpYearly.IsVisible = false;
                    BuyYearlyAccessButton.IsVisible = false;
                    
                    //GridTips1.IsVisible = false;
                    //GridTips2.IsVisible = false;
                    //LblTooExpensive2.IsVisible = false;
                    //ImgJonus.IsVisible = false;
                    //FrmUserReview.IsVisible = false;
                    //FrmMKJUserReview.IsVisible = false;
                    //FrmPoteroUserReview.IsVisible = false;
                    //LblMoreUserReview.IsVisible = false;
                    //LblTheTestla.IsVisible = false;
                    //ImgBrandLogo.IsVisible = false;
                    //FrmExepertReview.IsVisible = false;
                    //FrmExepertReviewJonny.IsVisible = false;
                    //ImgArtin.IsVisible = false;

                    //HideTermsOfUseLables();
                }
                BuyMealplanAddon.Text = $"{await DependencyService.Get<IDrMuscleSubcription>().GetMealPlanLabel()}";
                BuyMealplanAddon.Clicked += async (object sender, EventArgs e) =>
                {
                    if (BuyMealplanAddon.Text == "You already have access to meal plans")
                    {
                        if (Device.RuntimePlatform == Device.Android)
                        {
                            await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                        }
                        else
                            await PagesFactory.PushAsync<MainAIPage>();
                        App.IsMealPlan = true;
                    }
                    else
                    {
                        await DependencyService.Get<IDrMuscleSubcription>().BuyMealPlanAccess();
                    }
                };
                SupportEmail2.IsVisible = true;
                SupportEmail.IsVisible = false;
                LblTooExpensive2.IsVisible = false;
                SignUpLabelLine2.Text = "You are subscribed to Dr. Muscle";
                SignUpLabelLineMealPlan.Text = "Get unlimited meal plans (add-on)";
            }
            else if(!isTraining && isMealPlan)
            {
                SignUpLabelLineMealPlan.Text = "You are subscribed to the meal plan add-on";
                BuyMealplanAddon.Text = "You already have access to meal plans";
                BuyMealplanAddon.Clicked += async (object sender, EventArgs e) =>
                {
                    if (Device.RuntimePlatform == Device.Android)
                    {
                        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                    }
                    else
                        await PagesFactory.PushAsync<MainAIPage>();
                };
                SupportEmail.IsVisible = false;
                LblTooExpensive2.IsVisible = false;
                SupportEmail3.IsVisible = true;
            }
            else
            {
                SignUpLabelLineMealPlan.Text = "You are subscribed to the meal plan add-on";
                BuyMealplanAddon.Text = "You already have access to meal plans";
                BuyMealplanAddon.Clicked += async (object sender, EventArgs e) =>
                {

                    if (Device.RuntimePlatform == Device.Android)
                    {
                        await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                    }
                    else
                        await PagesFactory.PushAsync<MainAIPage>();

                };
                LblRead5Star.IsVisible = false;
                SignUpLabelHeading.Text = "Thank you!";
                SupportEmail.Margin = new Thickness(20, 15, 20, 0);
                TermsPrivacyStack.IsVisible = false;
                //GridTips.IsVisible = false;
                //SignUpLabelHeading.IsVisible = false;
                LblTooExpensive.IsVisible = false;
                SignUpLabelLine2.Text = "You are subscribed to Dr. Muscle";
                BuyMonthlyAccessButton.IsVisible = true;
                BuyMonthlyAccessButton.Text = "You already have access";
                BuyMonthlyAccessButton.Clicked -= BuyMonthlyAccessButton_Access_Clicked;
                BuyMonthlyAccessButton.Clicked += BuyMonthlyAccessButton_Access_Clicked;
                SupportEmail.IsVisible = true;
                SignUpYearly.IsVisible = false;
                BuyYearlyAccessButton.IsVisible = false;
                GridTips.IsVisible = false;
                GridTips1.IsVisible = false;
                GridTips2.IsVisible = false;
                LblTooExpensive2.IsVisible = false;
                ImgJonus.IsVisible = false;
                FrmUserReview.IsVisible = false;
                FrmMKJUserReview.IsVisible = false;
                FrmPoteroUserReview.IsVisible = false;
                LblMoreUserReview.IsVisible = false;
                LblTheTestla.IsVisible = false;
                ImgBrandLogo.IsVisible = false;
                FrmExepertReview.IsVisible = false;
                FrmExepertReviewJonny.IsVisible = false;
                ImgArtin.IsVisible = false;

                HideTermsOfUseLables();

                
            }

            if (Device.RuntimePlatform.Equals(Device.iOS))
            {
                SupportEmail.IsVisible = false;
                SupportEmail3.IsVisible = false;
                SupportEmail2.IsVisible = false;
                LblTooExpensive2.IsVisible = false;
            }
        }

        async void SetMonthlyActiveView()
        {
            BuyMonthlyAccessButton.Text = "You are on the monthly plan";

            DependencyService.Get<IDrMuscleSubcription>().OnYearlyAccessPurchased += async delegate {
                if (Device.RuntimePlatform == Device.Android)
                {
                    await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                }
                else
                    await PagesFactory.PushAsync<MainAIPage>(); MessagingCenter.Send<SubscriptionSuccessfulMessage>(new SubscriptionSuccessfulMessage(), "SubscriptionSuccessfulMessage");
            };
            BuyMonthlyAccessButton.IsVisible = true;
            BuyYearlyAccessButton.IsVisible = true;
            LblTooExpensive.IsVisible = true;


            SignUpYearly.IsVisible = true;
            var yearlyText = await DependencyService.Get<IDrMuscleSubcription>().GetYearlyButtonLabel();
            yearlyText = yearlyText.Replace("year", AppResources.Year);
            yearlyText = yearlyText.Replace("Sign up annual", AppResources.SignUpAnnual);
            BuyYearlyAccessButton.Text = yearlyText;

            GridTips1.IsVisible = true;
            GridTips2.IsVisible = true;
            if (Device.RuntimePlatform.Equals(Device.Android))
                LblTooExpensive2.IsVisible = true;
            ImgJonus.IsVisible = true;
            FrmUserReview.IsVisible = true;
            FrmMKJUserReview.IsVisible = true;
            FrmPoteroUserReview.IsVisible = true;
            LblMoreUserReview.IsVisible = true;
            LblTheTestla.IsVisible = true;
            ImgBrandLogo.IsVisible = true;
            FrmExepertReview.IsVisible = true;
            FrmExepertReviewJonny.IsVisible = true;
            ImgArtin.IsVisible = true;
            //LblExpertReview.IsVisible = true;
            SignUpLabelHeading.IsVisible = true;
            BuyMonthlyAccessButton.Clicked -= BuyMonthlyAccessButton_Access_Clicked;
            BuyMonthlyAccessButton.Clicked += BuyMonthlyAccessButton_Access_Clicked;

        }
        private void RemoveClickEvent(Button b)
        {
            //FieldInfo f1 = typeof(Control).GetField("EventClick",
            //    BindingFlags.Static | BindingFlags.NonPublic);

            //object obj = f1.GetValue(b);
            //PropertyInfo pi = b.GetType().GetProperty("Events",
            //    BindingFlags.NonPublic | BindingFlags.Instance);

            //EventHandlerList list = (EventHandlerList)pi.GetValue(b, null);
            //list.RemoveHandler(obj, list[obj]);
            
        }

        async void BuyMonthlyAccessButton_Access_Clicked(object sender, EventArgs e)
        {
            if (BuyMonthlyAccessButton.Text.Equals(AppResources.YouAlreadyHaveAccess) || BuyMonthlyAccessButton.Text.Equals("You are on the monthly plan"))
            {
                if (Device.RuntimePlatform == Device.Android)
                {
                    await PagesFactory.PushAsyncWithoutBefore<MainAIPage>();
                }
                else
                    await PagesFactory.PushAsync<MainAIPage>();
            }
        }

            async void BuyMonthlyAccessButton_Clicked(object sender, EventArgs e)
        {
            try
            {

            
            if (!CrossConnectivity.Current.IsConnected)
            {
                ConnectionErrorPopup();

            }
            if (BuyMonthlyAccessButton.Text.Equals(AppResources.YouAlreadyHaveAccess) || BuyMonthlyAccessButton.Text.Equals("You are on the monthly plan"))
            {
                return;
            }
            await DependencyService.Get<IDrMuscleSubcription>().BuyMonthlyAccess();
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
                
            }
        }
        async void BuyYearlyAccessButton_Clicked(object sender, EventArgs e)
        {
            try {
            if (!CrossConnectivity.Current.IsConnected)
            {
                ConnectionErrorPopup();

            }
            await DependencyService.Get<IDrMuscleSubcription>().BuyYearlyAccess();
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
                
            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            var rndm = new Random();
            BotList.Clear();
            var review = userReviewList.ElementAt(rndm.Next(0, 9));
            var expertReview = expertReviewList.ElementAt(rndm.Next(0, 9));
            //LblReview.Text = review.Review;
            //LblReviewerName.Text = review.ReviewerName;

            LblMKJReview.Text = "\"AI is great and makes it very easy\"";
            LblMKJsubHeadingReviewer.Text = "\"Easy for me to know how many reps to do and how much weight to lift. No more guessing. This really is something different.\"";
            LblMKJReviewerName.Text = "MKJ&MKJ";

            LblPoteroReview.Text = "\"Gained 10 lbs\"";
            LblPoterosubHeadingReviewer.Text = "\"Have been in and out of the gym for a few years with modest gains, however, this app helped me gain 10 lbs and become significantly more defined. Very easy to use.\"";
            LblPoteroReviewerName.Text = "Potero2122";

            LblReview.Text = "\"Takes the brain work out of weights, reps, and sets\"";
            LblsubHeadingReviewer.Text = "\"For basic strength training this app out performs the many methods/app I have tried in my 30+ years of body/strength training.\"";
            LblReviewerName.Text = "TijFamily916";


            LblReview1.Text = "\"I haven’t skipped a workout since I started using it\"";
            LblsubHeadingReviewer1.Text = "\"This app creates new workouts automatically. They're challenging enough for me to keep building muscle without overtraining. It also keeps me motivated by showing me my progress.\"";
            LblReviewerName1.Text = "Artin Entezarjou, MD, PhD(c)";

            string cweight = "", todayweight = "", liftedweight = "";
            
            if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
            {
                cweight = "95 kg";
                todayweight = "120 kg";
                liftedweight = "242 kg";
            }
            else
            {
                cweight = "210 lbs";
                todayweight = "265 lbs";
                liftedweight = "535 lbs";
            }
            LblReview2.Text = $"\"I was never that heavy ({cweight})\"";
            LblsubHeadingReviewer2.Text = $"\"My strength on hip trusts exploded from something like {todayweight} to {liftedweight}. The app has scientific algorithms... simple stupid and effective wether its raining or sunshine, just follow the app and don't overthink to much.\"";
            LblReviewerName2.Text = "Jonas Notter, World Natural Bodybuilding Champion";
            //BotList.Add(new BotModel()
            //{
            //    Part1 = "Expert reviews",
            //    Part2 = expertReview.Review,
            //    Part3 = expertReview.ReviewerName,
            //    Type = BotType.Review
            //});
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
        }

        //void lstChats_ItemTapped(System.Object sender, Xamarin.Forms.ItemTappedEventArgs e)
        //{
        //    if (lstChats.SelectedItem != null)
        //        lstChats.SelectedItem = null;
        //}
        private List<ReviewsModel> GetReviews()
        {
            List<ReviewsModel> reviews = new List<ReviewsModel>();
            reviews.Add(new ReviewsModel()
            {
                Review = "For basic strength training this app out performs the many methods/apps I have tried in my 30+ years of body/strength training. What I like the most is that it take the brain work out of weights, reps, and sets (if you follow a structured workout). What I like even more is the exceptional customer engagement.",
                ReviewerName = "TijFamily916"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Let me just say, I was thinking of being an online personal trainer but after using and seeing the power of this app, I sincerely can’t charge people the rates I had in mind when this app does it at a fraction of the cost. The man behind it, Dr. Juneau is the real deal too.",
                ReviewerName = "Rajib Ghosh"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "love seeing my progress on my 1 RM while varying my weight and rep count. Also feel like I am getting more results in a shorter time utilizing the rest pause method. Loving the workouts and the feedback from the app",
                ReviewerName = "Randall Duke"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Maximizing the time in the gym takes preparation. This app eliminates that and does a better job then I did with hours of preparation. I've seen amazing gains with less work.",
                ReviewerName = "Raymond Backers"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Great alternative to an actual human personal trainer if your schedule is always dynamic. The charts and graphs and many various options are outstanding.",
                ReviewerName = "Daniel Quick"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Dr Carl has used science and experience to create an app that will continually push you to the limits. I've been using this app for about a month now, and am moving weight that I didn't think was possible in this short amount of time. I've been lifting for years, but this app would be just as affective for a beginner. One of the best things about it, is Dr Carl listens to the users and their feedback, and is constantly making improvements.",
                ReviewerName = "DeeBee78"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "This app is absolutely amazing. I have been in and out of the gym for a few years with some light progress every time and modest gains, however, the implementation of this app helped me gain 10 lbs and become significantly more defined in the first 6 weeks. Very easy to use, and the customer service is incredible. This app is really great for anyone from beginners to experts.",
                ReviewerName = "Potero2122"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "When I first trialed the app, I wasn’t sure I’d like it, but after having stuck with it for a couple of months, I’m sold. The AI is great and makes it very easy for me to know how many reps to do and how much weight to lift. No more guessing. He brings all the science of lifting to this app, and I’d been lifting regularly for two years. This really is something different than any other app out there.",
                ReviewerName = "MKJ&MKJ"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "This is a very good app to invest in. It's already a good design and has great workouts that will help you continually build muscle and break through plateaus, but they are constantly working to improve it based on customer feedback. The most important thing about this app is the customer service. Christelle and Carl are always available to assist you in anyway they can in a very timely manner, most of the time within an hour of submitting your question or issue. I would recommend this app to everyone serious about building muscle.",
                ReviewerName = "David Fechter"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "I have been using Dr. Muscle for two years now and this app gives me confidence and provides structure to my workouts. I love that the app adapts to you and is quite \"forgiving\" when you do fail while encouraging you to push harder each time. It has really demystified all the elements of training for hypertrophy so I can get straight to lifting after a hard day at work without having to think about everything! I look forward to the analysis of my \"performance\" after every exercise and love to see those green check marks indicating progress. I have recently subscribed to \"Eve\" the dietary equivalent to this app and while it's in its early stages of development I'm looking forward to similarly great things.",
                ReviewerName = "Remone Mundle"
            });

            return reviews;
        }

        private List<ReviewsModel> GetExpertsReviews()
        {
            List<ReviewsModel> reviews = new List<ReviewsModel>();
            reviews.Add(new ReviewsModel()
            {
                Review = "It's like a personal trainer in your pocket that tells you exactly what to do... I can't believe how easy it is.",
                ReviewerName = "Jon Benson"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "This app could get a whole lot of people on track integrating the modern science-based approach to building muscle mass and strength",
                ReviewerName = "Will Brink"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Skeptical at first but I gave it a shot. In a few short weeks, not only did I make progress but I hit some new PRs. Now I recommend this app to anyone who will hear me.",
                ReviewerName = "Marc David"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "This app can help you achieve sustained muscle growth and strength development. It's based on rock-solid resistance training SCIENCE that has been proven to work in countless studies.",
                ReviewerName = "Nick Nilsson"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "This program creates new workouts with proven principles to help you build muscle like never before",
                ReviewerName = "Alex Rogers"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Building muscle and getting into the best shape of my life has NEVER been easier. I dare you to try and find another muscle building app like it.",
                ReviewerName = "Tim Ernst"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Whoa, I wish I had this app when I started out. In 5 minutes, I had my workout set up and the app is already telling me what to do next. How much to lift, what exercises to do... the whole nine yards.",
                ReviewerName = "Dave Ruel"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "That's why I like this app so much. When the weights get too heavy, it deloads your weights automatically for you. That way, in the long run, you can build more muscle and keep your joints healthy.",
                ReviewerName = "Lee Hayward"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "I interviewed Dr. Carl Juneau and I can tell you this is REAL science with cutting-edge strategies you can use right now increase both strength AND muscle in less time.",
                ReviewerName = "Pete Genot"
            });
            reviews.Add(new ReviewsModel()
            {
                Review = "Love the app's straight forward interface. Easy to log your weight training. What's truly unique though is that it is up to date and science-based. It helps you choose the right format of your workouts based on your training background.",
                ReviewerName = "Dr. Nick Monastiriotis"
            });
            return reviews;
        }

        private void HideTermsOfUseLables()
        {
            SignUpLabelLine3.IsVisible = false;
            SignUpLabelLine4.IsVisible = false;
            //TermsOfUse.IsVisible = false;
            //Pipe.IsVisible = false;
            //PrivacyPolicy.IsVisible = false;
        }

        
        //double x, y;
        //bool isUped = false;
        //void OnPanUpdated(object sender, PanUpdatedEventArgs e)
        //{
        //    // Handle the pan
        //    switch (e.StatusType)
        //    {
        //        case GestureStatus.Running:
        //            // Translate and ensure we don't y + e.TotalY pan beyond the wrapped user interface element bounds.
        //            var translateY = Math.Max(Math.Min(0, y + e.TotalY), -Math.Abs((Height * .25) - Height));
        //            bottomSheet.TranslateTo(bottomSheet.X, translateY, 20);
        //            break;
        //        case GestureStatus.Completed:
        //            // Store the translation applied during the pan
        //            y = bottomSheet.TranslationY;

        //            //at the end of the event - snap to the closest location
        //            var finalTranslation = Math.Max(Math.Min(0, -1000), -Math.Abs(getClosestLockState(e.TotalY + y)));

        //            //depending on Swipe Up or Down - change the snapping animation
        //            if (isSwipeUp(e))
        //            {
        //                bottomSheet.TranslateTo(bottomSheet.X, finalTranslation, 250, Easing.SpringIn);
                        
        //            }
        //            else
        //            {
        //                bottomSheet.TranslateTo(bottomSheet.X, finalTranslation, 250, Easing.SpringOut);
        //            }
        //            isUped = finalTranslation == 0 ? false : true;
        //            ImgArrow.RotateXTo(isUped ? 180 : 0);
        //            //dismiss the keyboard after a transition
        //            y = bottomSheet.TranslationY;

        //            break;

        //    }

        //}

        //public bool isSwipeUp(PanUpdatedEventArgs e)
        //{
        //    if (e.TotalY < 0)
        //    {
        //        return true;
        //    }
        //    return false;
        //}

        //TO-DO: Make this cleaner
        //public double getClosestLockState(double TranslationY)
        //{
        //    //Play with these values to adjust the locking motions - this will change depending on the amount of content ona  apge
        //    var lockStates = new double[] { 0, .5, .85 };

        //    //get the current proportion of the sheet in relation to the screen
        //    var distance = Math.Abs(TranslationY);
        //    var currentProportion = distance / Height;

        //    //calculate which lockstate it's the closest to
        //    var smallestDistance = 10000.0;
        //    var closestIndex = 0;
        //    for (var i = 0; i < lockStates.Length; i++)
        //    {
        //        var state = lockStates[i];
        //        var absoluteDistance = Math.Abs(state - currentProportion);
        //        if (absoluteDistance < smallestDistance)
        //        {
        //            smallestDistance = absoluteDistance;
        //            closestIndex = i;
        //        }
        //    }

        //    var selectedLockState = lockStates[closestIndex];
        //    var TranslateToLockState = getProportionCoordinate(selectedLockState);

        //    return TranslateToLockState;
        //}

        //public double getProportionCoordinate(double proportion)
        //{
        //    return proportion * Height;
        //}

        //void dismissBottomSheet()
        //{

        //    var finalTranslation = Math.Max(Math.Min(0, -1000), -Math.Abs(getProportionCoordinate(0)));
        //    bottomSheet.TranslateTo(bottomSheet.X, finalTranslation, 450, Easing.SpringOut);
        //}

        //void openBottomSheet()
        //{
        //    var finalTranslation = Math.Max(Math.Min(0, -1000), -Math.Abs(getProportionCoordinate(.85)));
        //    bottomSheet.TranslateTo(bottomSheet.X, finalTranslation, 150, Easing.SpringIn);
        //}

        //void TapGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
        //{
        //    if (isUped)
        //    {
        //        isUped = false;
        //        dismissBottomSheet();
        //        ImgArrow.RotateXTo(0);
        //    }
        //    else
        //    {
                
        //        isUped = true;
        //        openBottomSheet();
        //        ImgArrow.RotateXTo(180);
        //    }
        //}

        void HelpGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
        {
            PagesFactory.PushAsync<FAQPage>();
        }

        void MoreUserReviewGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
        {
            Device.OpenUri(new Uri("https://dr-muscle.com/reviews/"));
        }

        void NewUpdatesGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
        {
            //
            Device.OpenUri(new Uri("https://dr-muscle.com/timeline/"));
        }

        void TapMoreExperReviews_Tapped(System.Object sender, System.EventArgs e)
        {
            Device.OpenUri(new Uri("https://dr-muscle.com/reviews/"));
        }
    }
}

